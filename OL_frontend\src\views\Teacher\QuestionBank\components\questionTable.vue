<!-- 试题表格展示 -->
<template>
  <el-table :data="questions" style="width: 100%">
    <el-table-column prop="content" label="题目内容" />
    <el-table-column label="操作" width="180">
      <template #default="{row}">
        <el-button size="small" @click="$emit('edit', row)">编辑</el-button>
        <el-button size="small" type="danger" @click="$emit('delete', row)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
defineProps(['questions'])
defineEmits(['edit', 'delete'])
</script>