// src/utils/axios.js
import axios from 'axios'

const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 为每个请求添加时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        t: new Date().getTime()
      }
    }
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)


// 响应拦截器
service.interceptors.response.use(
  response => {
    // 调试：打印响应状态和数据
    console.log(`API响应: ${response.config.url} - ${response.status}`)
    if (response.data && response.data.success === false) {
      console.warn('API错误:', response.data.message)
    }
    return response
  },
  error => {
    console.error('响应拦截器错误:', error)
    let message = '请求失败'
    if (error.response && error.response.data) {
      message = error.response.data.message || message
    }
    return Promise.reject(new Error(message))
  }
)

export default service