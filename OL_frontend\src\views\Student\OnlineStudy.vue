<template>
  <div class="online-study-page">
    <!-- <Header /> -->
    <div class="study-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton style="width: 100%" animated>
          <template #template>
            <div style="display: flex; gap: 20px">
              <div style="width: 220px">
                <el-skeleton-item variant="p" style="height: 600px" />
              </div>
              <div style="flex: 1">
                <el-skeleton-item variant="p" style="height: 50px; margin-bottom: 20px" />
                <el-skeleton-item variant="p" style="height: 500px; margin-bottom: 20px" />
                <el-skeleton-item variant="p" style="height: 50px" />
              </div>
              <div style="width: 280px">
                <el-skeleton-item variant="p" style="height: 600px" />
              </div>
            </div>
          </template>
        </el-skeleton>
      </div>

      <!-- 错误提示 -->
      <el-alert
        v-else-if="error"
        :title="error"
        type="error"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />

      <!-- 课程内容 -->
      <template v-else>
        <!-- 左侧课程导航 -->
        <div class="course-nav">
          <div class="nav-header">
            <h3>课程导航</h3>
            <el-button type="text" class="switch-btn" @click="showDownloadDialog = true">
              下载课件
            </el-button>
          </div>

          <el-tree
            :data="courseChapters"
            :props="defaultProps"
            @node-click="handleNodeClick"
            :default-expanded-keys="['1']"
            node-key="id"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span
                  v-if="data.type === 'lesson'"
                  class="lesson-status"
                  :class="data.completed ? 'completed' : ''"
                >
                  {{ data.completed ? '已完成' : '未完成' }}
                </span>
              </span>
            </template>
          </el-tree>
        </div>

        <!-- 中间内容区域 -->
        <div class="content-area">
          <div class="content-header">
            <h2 class="course-title">课程: {{ currentCourse.title }}</h2>
          </div>

          <!-- 视频播放器 -->
          <div class="video-player">
            <div class="player-container">
              <video
                ref="videoPlayer"
                :src="videoSrc"
                class="video-element"
                :playbackRate="playbackRate"
                @timeupdate="updateVideoProgress"
                @loadedmetadata="onVideoLoaded"
                controls
              ></video>
              <!-- <div class="video-controls"> -->
              <!-- <div class="progress-bar" @click="seekVideo">
                  <div class="progress-bg"></div>
                  <div class="progress-current" :style="{ width: videoProgress + '%' }"></div>
                  <div class="progress-handle" :style="{ left: videoProgress + '%' }"></div>
                </div> -->
              <!-- <div class="control-buttons">
                  <span class="time-display"
                    >{{ formatTime(currentTime) }} / {{ formatTime(totalDuration) }}</span
                  >
                  <div class="right-controls">
                    <el-input-number
                      v-model="playbackRate"
                      :min="0.5"
                      :max="2"
                      :step="0.25"
                      size="small"
                      @change="changePlaybackRate"
                    />
                    <el-button size="small" @click="toggleFullscreen">
                      <el-icon><FullScreen /></el-icon>
                    </el-button>
                  </div>
                </div> -->
              <!-- </div> -->
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button type="primary" @click="enterExam">
              <el-icon><Document /></el-icon>进入考试
            </el-button>
            <el-button @click="showDownloadDialog = true">
              <el-icon><Download /></el-icon>下载课件
            </el-button>
            <el-button :type="isFavorite ? 'warning' : 'default'" @click="toggleFavorite">
              <el-icon><Star /></el-icon>{{ isFavorite ? '已收藏' : '收藏' }}
            </el-button>
            <el-button @click="takeNotes">
              <el-icon><EditPen /></el-icon>笔记
            </el-button>
          </div>
        </div>

        <!-- 右侧评论区 -->
        <div class="comments-section">
          <h3 class="section-title">评论区</h3>

          <div class="comments-list">
            <div v-for="(comment, index) in comments" :key="index" class="comment-item">
              <div class="comment-avatar">
                <el-avatar :size="40" :icon="UserFilled" />
              </div>
              <div class="comment-content">
                <div class="comment-header">
                  <span class="username">{{ comment.username }}</span>
                  <span class="comment-time">{{ comment.time }}</span>
                </div>
                <div class="comment-text">{{ comment.content }}</div>
                <div class="comment-actions">
                  <el-button type="text" @click="likeComment(comment)">
                    <el-icon><Star /></el-icon> {{ comment.likes }}
                  </el-button>
                  <el-button type="text" @click="replyComment(comment)">
                    <el-icon><ChatDotRound /></el-icon> 回复
                  </el-button>
                </div>

                <!-- 评论回复区域 -->
                <div
                  v-if="comment.showReplies || (comment.replies && comment.replies.length > 0)"
                  class="comment-replies"
                >
                  <div v-for="(reply, rIndex) in comment.replies" :key="rIndex" class="reply-item">
                    <div class="reply-avatar">
                      <el-avatar :size="30" :icon="UserFilled" />
                    </div>
                    <div class="reply-content">
                      <div class="reply-header">
                        <span class="username">{{ reply.username }}</span>
                        <span class="reply-time">{{ reply.time }}</span>
                      </div>
                      <div class="reply-text">{{ reply.content }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 发表评论 -->
          <div class="comment-input">
            <el-input
              v-model="newComment"
              type="textarea"
              :rows="3"
              placeholder="发表评论..."
              maxlength="200"
              show-word-limit
            />
            <div class="comment-submit">
              <span class="comment-count">{{ newComment.length }} / 200</span>
              <el-button type="primary" @click="submitComment"
                >发送 <el-icon><Position /></el-icon
              ></el-button>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 笔记对话框 -->
    <el-dialog v-model="showNotesDialog" title="学习笔记" width="700px">
      <div class="notes-container">
        <el-tabs v-model="notesTabActive">
          <el-tab-pane label="我的笔记" name="myNotes">
            <div class="my-notes">
              <el-input
                v-model="currentNote"
                type="textarea"
                :rows="10"
                placeholder="在这里记录你的学习笔记..."
                maxlength="2000"
                show-word-limit
              />
              <div class="notes-actions">
                <el-button type="primary" @click="saveNotes">保存笔记</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="笔记历史" name="history">
            <div class="notes-history">
              <div v-for="(note, index) in notesHistory" :key="index" class="history-item">
                <div class="history-header">
                  <span class="history-title">{{ note.title }}</span>
                  <span class="history-time">{{ note.time }}</span>
                </div>
                <div class="history-content">{{ note.content }}</div>
                <div class="history-actions">
                  <el-button type="text" @click="editNote(note)">编辑</el-button>
                  <el-button type="text" @click="deleteNote(note)">删除</el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 下载课件对话框 -->
    <el-dialog v-model="showDownloadDialog" title="下载课件" width="600px">
      <div class="download-selector">
        <h4 class="download-title">选择需要下载的课件资料：</h4>

        <el-table
          :data="courseMaterials"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          empty-text="暂无课件资料"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="课件名称" min-width="180" />
          <el-table-column prop="chapter" label="所属章节" min-width="120" />
          <el-table-column prop="type" label="类型" width="80">
            <template #default="{ row }">
              <el-tag :type="getMaterialTagType(row.type)">{{ row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="80" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDownloadDialog = false">取消</el-button>
          <el-button type="primary" @click="downloadMaterials">
            <el-icon><Download /></el-icon>下载
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { useUserStore } from '@/stores/user'
import {
  Document,
  Download,
  Star,
  EditPen,
  UserFilled,
  ChatDotRound,
  Position,
  FullScreen,
  CaretTop,
  Files,
  VideoCamera,
  Document as DocumentIcon,
  Picture,
} from '@element-plus/icons-vue'
import Header from '@/components/header.vue'
// 导入本地视频资源
import videoSrc from '@/assets/video/1378398032-1-16.mp4'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 课程ID
const courseId = ref(null)

// 课程数据
const currentCourse = ref({
  id: null,
  title: '加载中...',
  description: '',
  teacher: '',
  totalDuration: 3600, // 总时长（秒）
})

// 课程章节数据
const courseChapters = ref([])

// 课程模块
const courseModules = ref([
  { id: 1, name: '视频课程' },
  { id: 2, name: 'PDF文档' },
  { id: 3, name: '练习题' },
])

// 课件资料
const courseMaterials = ref([])

// 树形控件配置
const defaultProps = {
  children: 'children',
  label: 'label',
}

// 视频播放相关
const currentTime = ref(0) // 当前播放时间（秒）
const totalDuration = ref(0) // 总时长（秒）
const playbackRate = ref(1) // 播放速度
const videoPlayer = ref(null) // 视频元素引用

// 评论数据
const comments = ref([])

// 笔记相关
const currentNote = ref('')
const notesHistory = ref([
  {
    id: 1,
    title: '第一章笔记',
    time: '2023-06-28',
    content: '今天学习了基本概念，需要记住以下几点...',
  },
  { id: 2, title: '第二章笔记', time: '2023-06-25', content: '关于高级应用的几个要点：1. ...' },
])

// 状态变量
const newComment = ref('')
const showNotesDialog = ref(false)
const notesTabActive = ref('myNotes')
const showDownloadDialog = ref(false)
const selectedMaterials = ref([])

// 收藏状态
const isFavorite = ref(false)

// 计算属性：视频进度百分比
const videoProgress = computed(() => {
  return totalDuration.value > 0 ? (currentTime.value / totalDuration.value) * 100 : 0
})

// 加载状态
const loading = ref(false)
const error = ref(null)

// 从后端获取课程学习数据
const fetchCourseStudyData = async () => {
  loading.value = true
  error.value = null

  try {
    // 获取课程ID
    const id = route.query.course_id
    if (!id) {
      throw new Error('缺少课程ID参数')
    }

    courseId.value = id

    const response = await axios.get(`http://localhost:8000/api/student/courses/${id}/study/`, {
      headers: {
        Authorization: `Bearer ${userStore.token}`,
      },
    })

    if (response.data.success) {
      const data = response.data.data

      // 更新课程信息
      currentCourse.value = {
        ...data.course,
        totalDuration: 3600, // 默认时长
      }

      // 更新章节数据
      courseChapters.value = data.chapters

      // 更新评论数据
      comments.value = data.comments

      // 获取课件资料
      fetchCourseMaterials()

      // 获取收藏状态
      fetchFavoriteStatus()

      console.log('获取到课程学习数据:', data)
    } else {
      error.value = response.data.message || '获取课程学习数据失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    console.error('获取课程学习数据出错:', err)
    error.value = err.message || '网络错误，请稍后重试'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 获取课件资料
const fetchCourseMaterials = async () => {
  try {
    // 获取所有章节的课件
    let allMaterials = []

    // 遍历章节获取课件
    for (const chapter of courseChapters.value) {
      if (chapter.id) {
        const response = await axios.get(
          `http://localhost:8000/api/chapters/${chapter.id}/materials/`,
          {
            headers: {
              Authorization: `Bearer ${userStore.token}`,
            },
          },
        )

        if (response.data.success && response.data.data) {
          // 处理课件数据，添加章节信息
          const materials = response.data.data.map((material) => ({
            id: material.id,
            name: material.title,
            type: material.type,
            size: formatFileSize(material.size || 0),
            chapter: chapter.title,
            url: material.url,
          }))

          allMaterials = [...allMaterials, ...materials]
        }
      }
    }

    // 更新课件列表
    courseMaterials.value = allMaterials
    console.log('获取到课件资料:', courseMaterials.value)
  } catch (err) {
    console.error('获取课件资料出错:', err)
    ElMessage.error('获取课件资料失败，请稍后重试')
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取收藏状态
const fetchFavoriteStatus = async () => {
  try {
    const response = await axios.get(
      `http://localhost:8000/api/courses/${courseId.value}/favorite/`,
      {
        params: { user_id: userStore.username },
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      },
    )

    if (response.data.success) {
      isFavorite.value = response.data.is_favorite
    }
  } catch (err) {
    console.error('获取收藏状态出错:', err)
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    if (isFavorite.value) {
      // 取消收藏
      const response = await axios.delete(
        `http://localhost:8000/api/courses/${courseId.value}/favorite/`,
        {
          params: { user_id: userStore.username },
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        },
      )

      if (response.data.success) {
        isFavorite.value = false
        ElMessage.success('已取消收藏')
      } else {
        ElMessage.error(response.data.message || '取消收藏失败')
      }
    } else {
      // 添加收藏
      const response = await axios.post(
        `http://localhost:8000/api/courses/${courseId.value}/favorite/`,
        {
          user_id: userStore.username,
        },
        {
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        },
      )

      if (response.data.success) {
        isFavorite.value = true
        ElMessage.success('已添加到收藏')
      } else {
        ElMessage.error(response.data.message || '收藏失败')
      }
    }
  } catch (err) {
    console.error('切换收藏状态出错:', err)
    ElMessage.error(err.response?.data?.message || '网络错误，请稍后重试')
  }
}

// 处理章节点击
const handleNodeClick = (data) => {
  if (data.type === 'lesson') {
    console.log('加载课程:', data.label)
    // 这里可以添加加载课程内容的逻辑
  }
}

// 格式化时间
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`
}

// 进入考试
const enterExam = () => {
  // 跳转到考试列表页面，传递当前课程ID
  router.push({
    path: '/exam-list',
    query: { course_id: courseId.value },
  })
}

// 打开笔记
const takeNotes = () => {
  showNotesDialog.value = true
}

// 保存笔记
const saveNotes = () => {
  if (currentNote.value.trim()) {
    ElMessage.success('笔记保存成功')
    // 这里添加保存笔记的逻辑

    // 模拟添加到历史
    const now = new Date()
    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`

    notesHistory.value.unshift({
      id: notesHistory.value.length + 1,
      title: `笔记 ${dateStr}`,
      time: dateStr,
      content: currentNote.value,
    })
  } else {
    ElMessage.warning('笔记内容不能为空')
  }
}

// 编辑笔记
const editNote = (note) => {
  currentNote.value = note.content
  notesTabActive.value = 'myNotes'
}

// 删除笔记
const deleteNote = (note) => {
  ElMessage.success('笔记已删除')
  notesHistory.value = notesHistory.value.filter((item) => item.id !== note.id)
}

// 点赞评论
const likeComment = async (comment) => {
  try {
    const response = await axios.post(
      `http://localhost:8000/api/comments/${comment.id}/like/`,
      {},
      {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      },
    )

    if (response.data.success) {
      // 更新评论点赞数
      comment.likes = response.data.data.likes
      ElMessage.success('点赞成功')
    } else {
      ElMessage.error(response.data.message || '点赞失败')
    }
  } catch (err) {
    console.error('点赞评论出错:', err)
    ElMessage.error(err.response?.data?.message || '网络错误，请稍后重试')
  }
}

// 回复评论
const replyComment = (comment) => {
  // 切换显示回复区域
  comment.showReplies = !comment.showReplies

  // 设置回复对象
  if (comment.showReplies) {
    newComment.value = `@${comment.username} `
    // 滚动到评论输入框
    document.querySelector('.comment-input').scrollIntoView({ behavior: 'smooth' })
  }
}

// 提交评论
const submitComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('评论内容不能为空')
    return
  }

  try {
    // 检查是否是回复
    let parentId = null
    let content = newComment.value

    // 检查是否以@开头，如果是则可能是回复
    if (content.startsWith('@')) {
      const match = content.match(/@([^\s]+)/)
      if (match) {
        // 查找被回复的评论
        const replyToUsername = match[1]
        const parentComment = comments.value.find((c) => c.username === replyToUsername)

        if (parentComment) {
          parentId = parentComment.id
        }
      }
    }

    const response = await axios.post(
      `http://localhost:8000/api/courses/${courseId.value}/comments/`,
      {
        content: content,
        parent_id: parentId,
        user_id: userStore.username, // 使用当前用户名
      },
      {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      },
    )

    if (response.data.success) {
      const newCommentData = response.data.data

      if (parentId) {
        // 如果是回复，添加到对应评论的回复列表中
        const parentComment = comments.value.find((c) => c.id === parentId)
        if (parentComment) {
          if (!parentComment.replies) {
            parentComment.replies = []
          }
          parentComment.replies.push(newCommentData)
        }
      } else {
        // 如果是新评论，添加到评论列表顶部
        newCommentData.replies = [] // 初始化回复列表
        comments.value.unshift(newCommentData)
      }

      newComment.value = ''
      ElMessage.success('评论发表成功')
    } else {
      ElMessage.error(response.data.message || '评论发表失败')
    }
  } catch (err) {
    console.error('提交评论出错:', err)
    ElMessage.error(err.response?.data?.message || '网络错误，请稍后重试')
  }
}

// 视频加载完成时
const onVideoLoaded = () => {
  if (videoPlayer.value) {
    totalDuration.value = videoPlayer.value.duration
    console.log('视频加载完成，总时长:', totalDuration.value)
  }
}

// 更新视频进度
const updateVideoProgress = () => {
  if (videoPlayer.value) {
    currentTime.value = videoPlayer.value.currentTime
  }
}

// 改变播放速度
const changePlaybackRate = (rate) => {
  if (videoPlayer.value) {
    videoPlayer.value.playbackRate = rate
  }
}

// 跳转到视频指定位置
const seekVideo = (e) => {
  if (videoPlayer.value && totalDuration.value > 0) {
    const progressBar = e.currentTarget
    const clickPosition = e.offsetX / progressBar.offsetWidth
    const seekTime = totalDuration.value * clickPosition
    videoPlayer.value.currentTime = seekTime
  }
}

// 切换全屏
const toggleFullscreen = () => {
  const videoElement = videoPlayer.value
  if (!document.fullscreenElement && videoElement) {
    if (videoElement.requestFullscreen) {
      videoElement.requestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 获取课件图标
const getMaterialIcon = (type) => {
  switch (type) {
    case 'video':
      return VideoCamera
    case 'pdf':
      return DocumentIcon
    case 'ppt':
      return Picture
    case 'doc':
      return Files
    default:
      return DocumentIcon
  }
}

// 下载课件
const downloadMaterials = async () => {
  if (selectedMaterials.value.length === 0) {
    ElMessage.warning('请至少选择一个课件资料')
    return
  }

  try {
    const response = await axios.post(
      'http://localhost:8000/api/materials/batch-download/',
      {
        material_ids: selectedMaterials.value,
      },
      {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      },
    )

    if (response.data.success) {
      const downloadLinks = response.data.data

      // 显示下载成功提示
      const names = downloadLinks.map((item) => item.title).join('、')
      ElMessage.success(`开始下载课件：${names}`)

      // 关闭对话框
      showDownloadDialog.value = false

      // 实际下载逻辑 - 这里使用简单的方式，实际中可能需要更复杂的处理
      downloadLinks.forEach((link) => {
        // 创建一个隐藏的a标签并触发下载
        const a = document.createElement('a')
        a.href = link.download_url
        a.download = link.title
        a.target = '_blank'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      })
    } else {
      ElMessage.error(response.data.message || '下载失败')
    }
  } catch (err) {
    console.error('下载课件出错:', err)
    ElMessage.error(err.response?.data?.message || '网络错误，请稍后重试')
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedMaterials.value = selection.map((item) => item.id)
}

// 获取课件标签类型
const getMaterialTagType = (type) => {
  const typeMap = {
    video: 'success',
    doc: 'primary',
    pdf: 'info',
    ppt: 'warning',
    image: 'danger',
    audio: '',
  }
  return typeMap[type] || ''
}

// 组件挂载时
onMounted(() => {
  // 获取课程学习数据
  fetchCourseStudyData()
})
</script>

<style scoped>
.online-study-page {
  padding: 0 80px 40px 80px;
  max-width: 1800px;
  margin: 0 auto;
}

/* 视频元素样式 */
.video-element {
  width: 100%;
  height: 500px;
  background-color: #000;
  object-fit: contain;
}

.loading-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.study-container {
  display: flex;
  gap: 20px;
  min-height: 800px;
  max-width: 1600px;
  margin: 0 auto;
}

/* 左侧导航样式 */
.course-nav {
  width: 220px;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.nav-header h3 {
  margin: 0;
  color: #409eff;
}

.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lesson-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #f56c6c;
  color: white;
}

.lesson-status.completed {
  background-color: #67c23a;
}

/* 中间内容区域样式 */
.content-area {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  min-width: 0; /* 确保flex项目可以正确缩小 */
}

.content-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.course-title {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

/* 视频播放器样式 */
.video-player {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
  width: 100%;
}

.player-container {
  position: relative;
  width: 100%;
}

.video-placeholder {
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 24px;
}

.video-controls {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px;
  color: #fff;
}

.progress-bar {
  position: relative;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  cursor: pointer;
  margin-bottom: 10px;
}

.progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 3px;
}

.progress-current {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #409eff;
  border-radius: 3px;
}

.progress-handle {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #fff;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-display {
  font-size: 14px;
}

.right-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

/* 评论区样式 */
.comments-section {
  width: 300px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  overflow-y: auto;
  max-height: 800px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.comments-list {
  margin-bottom: 20px;
  overflow-y: auto;
  max-height: 500px;
}

.comment-item {
  display: flex;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-avatar {
  margin-right: 15px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 5px;
}

.username {
  font-weight: 500;
  color: #303133;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-text {
  margin-bottom: 10px;
  line-height: 1.5;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.comment-replies {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.reply-item {
  display: flex;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.reply-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.reply-avatar {
  margin-right: 10px;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 5px;
}

.reply-time {
  font-size: 12px;
  color: #909399;
}

.reply-text {
  margin-bottom: 10px;
  line-height: 1.5;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
}

.comment-input {
  margin-top: 20px;
}

.comment-submit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.comment-count {
  color: #909399;
  font-size: 12px;
}

/* 笔记对话框样式 */
.notes-container {
  min-height: 400px;
}

.my-notes {
  display: flex;
  flex-direction: column;
}

.notes-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

.notes-history {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.history-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.history-title {
  font-weight: bold;
  color: #303133;
}

.history-time {
  color: #909399;
  font-size: 14px;
}

.history-content {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
}

.history-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

/* 下载课件对话框样式 */
.download-selector {
  padding: 10px 0;
}

.download-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.material-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.material-item:last-child {
  border-bottom: none;
}

.material-info {
  display: flex;
  align-items: center;
}

.material-icon {
  margin-right: 10px;
  color: #409eff;
}

.material-name {
  flex: 1;
  margin-right: 10px;
}

.material-size {
  color: #909399;
  font-size: 12px;
}

.username,
.comment-time,
.reply-time {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
