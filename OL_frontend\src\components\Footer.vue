<template>
  <footer class="site-footer">
    <div class="footer-content">
      <div class="footer-section">
        <h3 class="footer-title">关于我们</h3>
        <p>学习系统是一个专注于在线教育的平台，提供丰富的课程资源和学习工具，帮助学生更好地学习和成长。</p>
      </div>
      
      <div class="footer-section">
        <h3 class="footer-title">帮助中心</h3>
        <ul class="footer-links">
          <li><a href="#">常见问题</a></li>
          <li><a href="#">使用指南</a></li>
          <li><a href="#">学习方法</a></li>
          <li><a href="#">意见反馈</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h3 class="footer-title">联系我们</h3>
        <ul class="footer-contact">
          <li><el-icon><Location /></el-icon> 地址：中国北京市海淀区学院路XX号</li>
          <li><el-icon><Phone /></el-icon> 电话：010-12345678</li>
          <li><el-icon><Message /></el-icon> 邮箱：<EMAIL></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h3 class="footer-title">关注我们</h3>
        <div class="social-icons">
          <a href="#" class="social-icon"><el-icon><ChatDotRound /></el-icon></a>
          <a href="#" class="social-icon"><el-icon><Share /></el-icon></a>
          <a href="#" class="social-icon"><el-icon><Link /></el-icon></a>
        </div>
      </div>
    </div>
    
    <div class="footer-bottom">
      <p>&copy; 2025 学习系统 版权所有 | 隐私政策 | 服务条款</p>
    </div>
  </footer>
</template>

<script setup>
import { Location, Phone, Message, ChatDotRound, Share, Link } from '@element-plus/icons-vue'
</script>

<style scoped>
.site-footer {
  background-color: var(--bg-footer);
  padding: 40px 0 20px;
  color: var(--text-regular);
  border-top: 1px solid var(--border-light);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20px;
}

.footer-section {
  width: 23%;
  margin-bottom: 20px;
}

.footer-title {
  font-size: 18px;
  color: var(--text-primary);
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 10px;
}

.footer-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links,
.footer-contact {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li,
.footer-contact li {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-contact li .el-icon {
  margin-right: 10px;
  color: var(--primary-color);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--bg-container);
  border-radius: 50%;
  color: var(--primary-color);
  transition: all 0.3s;
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: white;
}

.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-lighter);
  color: var(--text-secondary);
  font-size: 14px;
}

@media (max-width: 768px) {
  .footer-section {
    width: 48%;
  }
}

@media (max-width: 576px) {
  .footer-section {
    width: 100%;
  }
}
</style> 