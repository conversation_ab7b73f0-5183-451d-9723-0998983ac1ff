<template>
    <div class="question-selector">
        <div class="selector-header">
            <el-input v-model="searchText" placeholder="搜索题目内容" clearable style="width: 300px; margin-right: 10px;"
                @keyup.enter="fetchQuestions" @clear="fetchQuestions" />
            <el-select v-model="selectedBank" placeholder="选择题库" clearable filterable
                style="width: 200px; margin-right: 10px;" @change="fetchQuestions">
                <el-option v-for="bank in questionBanks" :key="bank.id" :label="bank.name" :value="bank.id" />
            </el-select>
            <el-select v-model="selectedType" placeholder="选择题型" clearable style="width: 120px; margin-right: 10px;"
                @change="fetchQuestions">
                <el-option v-for="type in questionTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
            <el-button type="primary" @click="fetchQuestions">
                <el-icon>
                    <Search />
                </el-icon> 搜索
            </el-button>
        </div>

        <div class="selector-main">
            <div class="bank-list">
                <el-tree :data="bankTree" node-key="id" :props="treeProps" :expand-on-click-node="false"
                    @node-click="handleBankClick" />
            </div>

            <div class="question-list">
                <el-table :data="questions" border height="500" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="id" label="ID" width="80" />
                    <el-table-column prop="type" label="题型" width="120">
                        <template #default="{ row }">
                            <el-tag :type="getQuestionTypeTag(row.type)" size="small">
                                {{ getQuestionTypeText(row.type) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="content" label="题目内容">
                        <template #default="{ row }">
                            <div v-html="row.content" class="question-content"></div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="difficulty" label="难度" width="100">
                        <template #default="{ row }">
                            <el-rate v-model="row.difficulty" disabled :max="5" show-score text-color="#ff9900"
                                score-template="{value}" />
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalQuestions"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="fetchQuestions"
                    @current-change="fetchQuestions" style="margin-top: 15px;" />
            </div>
        </div>

        <div class="selected-questions">
            <h3>已选题目 ({{ selectedQuestions.length }})</h3>
            <div class="selected-list">
                <el-tag v-for="q in selectedQuestions" :key="q.id" closable @close="removeSelected(q)"
                    style="margin-right: 8px; margin-bottom: 8px;">
                    {{ q.id }}. {{ q.content.substring(0, 30) }}...
                </el-tag>
            </div>
        </div>

        <div class="selector-footer">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="confirmSelection">确认选择</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from '@/utils/axios'

const props = defineProps({
    selectedQuestions: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['select', 'cancel'])

// 搜索条件
const searchText = ref('')
const selectedBank = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 数据
const questionBanks = ref([])
const bankTree = ref([])
const questions = ref([])
const totalQuestions = ref(0)
const selectedQuestions = ref([...props.selectedQuestions])

// 题型选项
const questionTypes = ref([
    { value: 'single_choice', label: '单选题' },
    { value: 'multiple_choice', label: '多选题' },
    { value: 'judgment', label: '判断题' },
    { value: 'fill_blank', label: '填空题' },
    { value: 'essay', label: '问答题' }
])

// 树形配置
const treeProps = ref({
    label: 'name',
    children: 'children'
})

// 方法
const fetchQuestionBanks = async () => {
    try {
        const response = await axios.get('/question-banks/mine/')
        questionBanks.value = response.data

        // 构建树形结构
        bankTree.value = [{
            id: 'my',
            name: '我的题库',
            children: questionBanks.value.filter(b => b.visibility === 0)
        }, {
            id: 'public',
            name: '公开题库',
            children: questionBanks.value.filter(b => b.visibility === 1)
        }]
    } catch (error) {
        console.error('获取题库列表失败:', error)
    }
}

const fetchQuestions = async () => {
    try {
        const params = {
            search: searchText.value,
            bank_id: selectedBank.value,
            type: selectedType.value,
            page: currentPage.value,
            page_size: pageSize.value,
            exclude: props.selectedQuestions.map(q => q.id).join(',')
        }

        const response = await axios.get('/questions/', { params })
        questions.value = response.data.results
        totalQuestions.value = response.data.count
    } catch (error) {
        console.error('获取题目列表失败:', error)
    }
}

const handleBankClick = (node) => {
    if (node.children) return // 只处理叶子节点
    selectedBank.value = node.id
    fetchQuestions()
}

const handleSelectionChange = (selection) => {
    selectedQuestions.value = [
        ...props.selectedQuestions,
        ...selection.filter(
            item => !props.selectedQuestions.some(q => q.id === item.id)
        )
    ]
}

const removeSelected = (question) => {
    selectedQuestions.value = selectedQuestions.value.filter(q => q.id !== question.id)
}

const getQuestionTypeText = (type) => {
    const typeMap = {
        'single_choice': '单选题',
        'multiple_choice': '多选题',
        'judgment': '判断题',
        'fill_blank': '填空题',
        'essay': '问答题'
    }
    return typeMap[type] || type
}

const getQuestionTypeTag = (type) => {
    const typeTags = {
        'single_choice': '',
        'multiple_choice': 'success',
        'judgment': 'warning',
        'fill_blank': 'info',
        'essay': 'danger'
    }
    return typeTags[type] || ''
}

const confirmSelection = () => {
    emit('select', selectedQuestions.value)
}

const cancel = () => {
    emit('cancel')
}

// 初始化
onMounted(() => {
    fetchQuestionBanks()
    fetchQuestions()
})
</script>

<style scoped>
.question-selector {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.selector-header {
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
}

.selector-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.bank-list {
    width: 250px;
    padding: 15px;
    border-right: 1px solid #ebeef5;
    overflow-y: auto;
}

.question-list {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
}

.question-content {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.selected-questions {
    padding: 15px;
    border-top: 1px solid #ebeef5;
    max-height: 150px;
    overflow-y: auto;
}

.selected-list {
    margin-top: 10px;
}

.selector-footer {
    padding: 15px;
    border-top: 1px solid #ebeef5;
    text-align: right;
}
</style>