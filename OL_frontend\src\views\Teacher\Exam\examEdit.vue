<template>
    <div class="exam-edit-page">
        <!-- 顶部标题及编辑按钮 -->
        <div class="page-header">
            <h1>{{ isEditMode ? '编辑考试' : '考试详情' }}</h1>
            <el-button type="primary" @click="toggleEditMode">
                {{ isEditMode ? '保存并退出编辑' : '进入编辑模式' }}
            </el-button>
        </div>

        <!-- 基本信息区域 -->
        <div class="basic-info">
            <div class="info-item">
                <label>考试名称：</label>
                <span v-if="!isEditMode">{{ examInfo.examName }}</span>
                <el-input v-else v-model="examInfo.examName" placeholder="请输入考试名称" />
            </div>
            <div class="info-row">
                <div class="info-item">
                    <label>开始时间：</label>
                    <span v-if="!isEditMode">{{ examInfo.startTime }}</span>
                    <el-date-picker v-else v-model="examInfo.startTime" type="datetime" placeholder="选择开始时间" />
                </div>
                <div class="info-item">
                    <label>结束时间：</label>
                    <span v-if="!isEditMode">{{ examInfo.endTime }}</span>
                    <el-date-picker v-else v-model="examInfo.endTime" type="datetime" placeholder="选择结束时间" />
                </div>
                <div class="info-item">
                    <label>考试时长：</label>
                    <span v-if="!isEditMode">{{ examInfo.duration }}</span>
                    <el-input v-else v-model="examInfo.duration" placeholder="如：2小时30分钟" />
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <label>允许中断考试：</label>
                    <span v-if="!isEditMode">{{ examInfo.allowInterrupt ? '是' : '否' }}</span>
                    <el-switch v-else v-model="examInfo.allowInterrupt" />
                </div>
                <div class="info-item">
                    <label>中断计入时长：</label>
                    <span v-if="!isEditMode">{{ examInfo.interruptDuration }}</span>
                    <el-input v-else v-model="examInfo.interruptDuration" placeholder="请输入中断计入时长" />
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <label>展示答案解析：</label>
                    <span v-if="!isEditMode">{{ examInfo.showAnalysis ? '是' : '否' }}</span>
                    <el-switch v-else v-model="examInfo.showAnalysis" />
                </div>
                <div class="info-item">
                    <label>错题进入错题本：</label>
                    <span v-if="!isEditMode">{{ examInfo.错题ToWrongBook ? '是' : '否' }}</span>
                    <el-switch v-else v-model="examInfo.错题ToWrongBook" />
                </div>
                <div class="info-item">
                    <label>允许查看成绩：</label>
                    <span v-if="!isEditMode">{{ examInfo.allowViewScore ? '是' : '否' }}</span>
                    <el-switch v-else v-model="examInfo.allowViewScore" />
                </div>
            </div>
            <div class="info-item">
                <label>设置及格分数：</label>
                <span v-if="!isEditMode">{{ examInfo.passScore }}</span>
                <el-input v-else v-model.number="examInfo.passScore" placeholder="请输入及格分数" />
            </div>
            <div class="info-row">
                <div class="info-item">
                    <label>参与班级：</label>
                    <span v-if="!isEditMode">{{ examInfo.joinClasses }}</span>
                    <el-input v-else v-model="examInfo.joinClasses" placeholder="请输入参与班级" />
                </div>
                <div class="info-item">
                    <label>批阅人员：</label>
                    <span v-if="!isEditMode">{{ examInfo.correctors }}</span>
                    <el-input v-else v-model="examInfo.correctors" placeholder="请输入批阅人员" />
                </div>
            </div>
            <div class="info-item">
                <label>考试说明：</label>
                <span v-if="!isEditMode">{{ examInfo.instructions }}</span>
                <el-input v-else v-model="examInfo.instructions" type="textarea" placeholder="请输入考试说明" rows="3" />
            </div>
        </div>

        <!-- 试卷内容区域 -->
        <div class="paper-content">
            <h2>试卷内容</h2>
            <el-table :data="paperList" border style="width: 100%">
                <el-table-column prop="id" label="编号" />
                <el-table-column prop="name" label="名称" />
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button type="text" @click="handleReplacePaper(scope.row)">
                            更换试卷
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 试卷预览区域 -->
            <div class="paper-preview">
                <h3>编辑试卷</h3>
                <div class="preview-container">
                    <div class="preview-item" v-for="(item, index) in previewList" :key="index">
                        <div class="preview-placeholder">
                            {{ item }}
                        </div>
                    </div>
                    <div class="page-nav">
                        <span>{{ currentPage }} of {{ totalPages }}</span>
                        <el-button @click="currentPage = Math.max(1, currentPage - 1)">
                            <el-icon>
                                <ArrowLeft />
                            </el-icon>
                        </el-button>
                        <el-button @click="currentPage = Math.min(totalPages, currentPage + 1)" >
                            <el-icon>
                                <ArrowRight />
                            </el-icon>
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作按钮（编辑模式下显示） -->
        <div class="bottom-buttons" v-if="isEditMode">
            <el-button type="primary" @click="saveExam">
                确认并保存
            </el-button>
            <el-button @click="cancelEdit">
                取消编辑
            </el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElButton, ElInput, ElDatePicker, ElSwitch, ElTable, ElTableColumn } from 'element-plus'

// 考试信息（模拟初始数据，实际可从接口获取）
const examInfo = reactive({
    examName: '计算机导论期末考试',
    startTime: '2023-11-12 09:00',
    endTime: '2023-11-12 11:30',
    duration: '2小时30分钟',
    allowInterrupt: true,
    interruptDuration: '10',
    showAnalysis: true,
    错题ToWrongBook: true,
    allowViewScore: true,
    passScore: 50,
    joinClasses: '计算机1班,计算机2班',
    correctors: 'xxx',
    instructions: '考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明考试说明',
})

// 试卷列表（模拟数据）
const paperList = ref([
    {
        id: '0012',
        name: '计算机科学导论A卷',
    },
])

// 试卷预览数据（模拟）
const previewList = ref(['试卷预览1', '试卷预览2', '试卷预览3'])
const currentPage = ref(1)
const totalPages = ref(previewList.value.length)

// 编辑模式标识
const isEditMode = ref(false)

// 切换编辑模式方法
const toggleEditMode = () => {
    isEditMode.value = !isEditMode.value
}

// 更换试卷方法
const handleReplacePaper = (row) => {
    if (isEditMode.value) {
        // 这里可实现更换试卷逻辑，比如弹出选择试卷弹窗等
        console.log('更换试卷：', row)
    } else {
        console.log('查看状态下无法更换试卷')
    }
}

// 保存考试信息方法
const saveExam = () => {
    // 这里可实现与后端交互保存数据逻辑
    console.log('保存考试信息：', examInfo)
    toggleEditMode() // 保存后退出编辑模式，也可根据需求调整
}

// 取消编辑方法
const cancelEdit = () => {
    // 可根据需求决定是否重置数据，这里简单退出编辑模式
    isEditMode.value = false
}
</script>

<style scoped>
.exam-edit-page {
    margin: 0 60px 40px 60px;
    padding: 20px 60px;
    background-color: #fff;
    min-height: calc(100vh - 64px);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.basic-info {
    margin-bottom: 30px;
}

.info-row {
    display: flex;
    gap: 40px;
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.info-item label {
    margin-bottom: 5px;
    font-weight: bold;
}

.paper-content {
    margin-bottom: 30px;
}

.paper-preview {
    margin-top: 20px;
}

.preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.preview-item {
    width: 400px;
    height: 500px;
    border: 1px solid #ddd;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    background-color: #f9f9f9;
}

.preview-placeholder {
    color: #999;
}

.page-nav {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bottom-buttons {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}
</style>