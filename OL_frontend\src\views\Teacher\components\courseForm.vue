<template>
    <div class="course-form-container">
        <el-steps :active="currentStep" finish-status="success" class="creation-steps">
            <el-step title="课程信息" />
            <el-step title="添加章节" />
            <el-step title="上传内容" />
        </el-steps>

        <!-- 第一步：课程基本信息 -->
        <div v-if="currentStep === 1" class="step-container">
            <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
                <el-form-item label="课程标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入课程标题" />
                </el-form-item>

                <el-form-item label="课程分类" prop="category">
                    <el-select v-model="form.category" placeholder="请选择课程分类" style="width: 100%"
                        :loading="loadingCategories" clearable>
                        <el-option v-for="category in categories" :key="category.id" :label="category.name"
                            :value="category.id" />
                    </el-select>
                </el-form-item>

                <el-form-item label="课程简介" prop="description">
                    <el-input v-model="form.description" type="textarea" rows="4" placeholder="请输入课程简介" />
                </el-form-item>

                <el-form-item label="封面图">
                    <div class="cover-upload-container">
                        <div class="cover-preview" v-if="form.coverUrl">
                            <img :src="form.coverUrl" alt="封面预览" class="cover-image" />
                            <div class="cover-remove" @click="removeCover">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </div>
                        </div>
                        <el-upload class="upload-btn" :action="coverUploadAction" :show-file-list="false"
                            :before-upload="beforeCoverUpload" :on-success="handleCoverUploadSuccess">
                            <el-button type="primary">上传封面图</el-button>
                            <template #tip>
                                <div class="el-upload__tip">建议尺寸：800×450像素，大小不超过2MB</div>
                            </template>
                        </el-upload>
                    </div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitCourseInfo" :loading="submitting">
                        保存并继续
                    </el-button>
                    <el-button @click="cancel">取消</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 第二步：章节管理 -->
        <div v-else-if="currentStep === 2" class="step-container">
            <div class="chapter-management">
                <el-button type="primary" @click="addChapter">添加章节</el-button>

                <div v-if="localChapters.length > 0" class="chapter-list">
                    <el-collapse v-model="activeChapter">
                        <el-collapse-item v-for="chapter in localChapters" :key="chapter.tempId" :name="chapter.tempId">
                            <template #title>
                                <div class="chapter-header">
                                    <el-input v-model="chapter.title" placeholder="章节标题" @click.stop />
                                    <el-button size="small" type="danger" @click.stop="removeChapter(chapter.tempId)">
                                        删除
                                    </el-button>
                                </div>
                            </template>

                            <div class="chapter-content">
                                <el-input v-model="chapter.description" type="textarea" placeholder="章节描述" rows="3" />
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
                <el-empty v-else description="暂无章节" :image-size="100" />

                <div class="step-actions">
                    <el-button @click="currentStep = 1">上一步</el-button>
                    <el-button type="primary" @click="saveChapters">保存并继续</el-button>
                </div>
            </div>
        </div>

        <!-- 第三步：内容上传 -->
        <div v-else class="step-container">
            <div class="content-upload">
                <el-tabs v-model="activeUploadTab">
                    <el-tab-pane label="课件管理" name="materials">
                        <!-- 统一课件管理区域 -->
                        <div class="material-management">
                            <el-button type="primary" @click="showMaterialDialog = true">
                                上传课件
                            </el-button>

                            <!-- 课件列表 -->
                            <el-table :data="allMaterials" style="width: 100%" empty-text="暂无课件">
                                <el-table-column prop="title" label="课件名称" width="180" />
                                <el-table-column prop="type" label="类型" width="100">
                                    <template #default="{ row }">
                                        <el-tag :type="getMaterialTagType(row.type)">{{ row.type }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="size" label="大小" width="120">
                                    <template #default="{ row }">
                                        {{ formatFileSize(row.size) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="200">
                                    <template #default="{ row }">
                                        <el-button size="small" @click="previewMaterial(row)">预览</el-button>
                                        <el-button size="small" type="danger"
                                            @click="removeMaterial(row.tempId || row.id)">
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="轮播图" name="carousels">
                        <div class="carousel-management">
                            <el-upload class="carousel-upload" :action="carouselUploadAction" :multiple="true"
                                :show-file-list="false" :before-upload="beforeCarouselUpload"
                                :on-success="handleCarouselUploadSuccess">
                                <el-button type="primary">上传轮播图</el-button>
                                <template #tip>
                                    <div class="el-upload__tip">建议尺寸：1200×600像素，大小不超过5MB</div>
                                </template>
                            </el-upload>

                            <div v-if="tempCarousels.length > 0" class="carousel-list">
                                <div v-for="(item, index) in tempCarousels" :key="item.tempId" class="carousel-item"
                                    draggable="true" @dragstart="handleDragStart(index)"
                                    @dragover.prevent="handleDragOver(index)" @drop="handleDrop(index)"
                                    @dragenter="handleDragEnter(index)" @dragleave="handleDragLeave(index)">
                                    <img :src="item.image_url" class="carousel-image" />
                                    <div class="carousel-actions">
                                        <el-button size="small" type="danger"
                                            @click.stop="removeTempCarousel(item.tempId)">
                                            删除
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                            <el-empty v-else description="暂无轮播图" :image-size="100" />
                        </div>
                    </el-tab-pane>
                </el-tabs>

                <!-- 第三步操作按钮 -->
                <div class="step-actions">
                    <el-button @click="currentStep = 2">上一步</el-button>
                    <el-button type="primary" @click="completeCreation">保存</el-button>
                </div>

                <!-- 上传课件对话框 -->
                <el-dialog v-model="showMaterialDialog" title="上传课件" width="50%">
                    <el-form :model="materialForm" label-width="100px">
                        <el-form-item label="课件标题" prop="title" required>
                            <el-input v-model="materialForm.title" placeholder="请输入课件标题" />
                        </el-form-item>

                        <el-form-item label="课件类型" prop="type" required>
                            <el-select v-model="materialForm.type" placeholder="请选择课件类型">
                                <el-option label="文档" value="doc" />
                                <el-option label="视频" value="video" />
                                <el-option label="图片" value="image" />
                                <el-option label="音频" value="audio" />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="课件文件" required>
                            <el-upload class="upload-demo" drag :action="materialUploadAction"
                                :before-upload="beforeMaterialUpload" :on-success="handleMaterialUploadSuccess"
                                :show-file-list="false">
                                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                                <div class="el-upload__text">
                                    将文件拖到此处，或<em>点击上传</em>
                                </div>
                                <template #tip>
                                    <div class="el-upload__tip">
                                        支持上传文档、图片、音频和视频文件，大小不超过50MB
                                    </div>
                                </template>
                            </el-upload>
                        </el-form-item>

                        <el-form-item label="时长(秒)" v-if="['video', 'audio'].includes(materialForm.type)">
                            <el-input-number v-model="materialForm.duration" :min="0" />
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <el-button @click="showMaterialDialog = false">取消</el-button>
                        <el-button type="primary" @click="addMaterial"
                            :disabled="!materialForm.url || !materialForm.title.trim()">
                            确认
                        </el-button>
                    </template>
                </el-dialog>

                <!-- 预览对话框 -->
                <el-dialog v-model="showPreviewDialog" :title="previewMaterial?.title" width="80%" top="5vh">
                    <!-- 图片预览 -->
                    <div v-if="previewMaterial?.type === 'image'" class="preview-content">
                        <img :src="previewMaterial.url"
                            style="max-width: 100%; max-height: 70vh; display: block; margin: 0 auto;">
                    </div>

                    <!-- 视频预览 -->
                    <div v-else-if="previewMaterial?.type === 'video'" class="preview-content">
                        <video controls style="width: 100%; max-height: 70vh;">
                            <source :src="previewMaterial.url" :type="getVideoMimeType(previewMaterial.url)">
                            您的浏览器不支持视频播放
                        </video>
                    </div>

                    <!-- 音频预览 -->
                    <div v-else-if="previewMaterial?.type === 'audio'" class="preview-content">
                        <audio controls style="width: 100%;">
                            <source :src="previewMaterial.url" :type="getAudioMimeType(previewMaterial.url)">
                            您的浏览器不支持音频播放
                        </audio>
                    </div>

                    <!-- PDF预览 -->
                    <div v-else-if="isPdfFile(previewMaterial?.url)" class="preview-content">
                        <iframe :src="`/pdf-viewer/web/viewer.html?file=${encodeURIComponent(previewMaterial.url)}`"
                            style="width: 100%; height: 70vh; border: none;">
                        </iframe>
                    </div>

                    <!-- Office文档预览 -->
                    <div v-else-if="isOfficeFile(previewMaterial?.url)" class="preview-content">
                        <iframe
                            :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(previewMaterial.url)}`"
                            width="100%" height="600px" frameborder="0">
                        </iframe>
                    </div>

                    <!-- 文本文件预览 -->
                    <div v-else-if="isTextFile(previewMaterial?.url)" class="preview-content">
                        <el-scrollbar style="height: 70vh;">
                            <pre>{{ textContent }}</pre>
                        </el-scrollbar>
                    </div>

                    <!-- 不支持预览的格式 -->
                    <div v-else class="no-preview">
                        <p>该文件类型不支持在线预览</p>
                        <el-button type="primary" @click="downloadFile(previewMaterial?.url, previewMaterial?.title)">
                            下载文件
                        </el-button>
                    </div>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, UploadFilled } from '@element-plus/icons-vue'
import axios from '@/utils/axios'

const router = useRouter()
const props = defineProps({
    teacherId: {
        type: String,
        required: true
    },
    initialData: {
        type: Object,
        default: null
    },
    isEditMode: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(["submit", "cancel"])

// 步骤控制
const currentStep = ref(1)
const activeChapter = ref(null)
const activeUploadTab = ref('materials')

// 表单相关
const formRef = ref(null)
const form = reactive({
    title: "",
    category: null,
    description: "",
    coverUrl: "",
})

const rules = {
    title: [{ required: true, message: "请输入课程标题", trigger: "blur" }],
    category: [{ required: true, message: "请选择课程分类", trigger: "change" }],
    description: [{ required: true, message: "请输入课程简介", trigger: "blur" }],
}

// 课程数据
const courseId = ref(null)
const localChapters = ref([])
const tempCarousels = ref([])
const categories = ref([])
const loadingCategories = ref(false)
const submitting = ref(false)

// 上传配置
const carouselUploadAction = "/api/upload/carousel/"
const coverUploadAction = "/api/upload/cover/"
const materialUploadAction = "/api/upload/material/"

// 课件管理相关
const showMaterialDialog = ref(false)
const materialForm = ref({
    title: '',
    type: '',
    url: '',
    size: 0,
    duration: 0,
    sort_order: 0
})

// 预览相关状态
const showPreviewDialog = ref(false)
const textContent = ref('')

// 拖拽相关状态
const dragIndex = ref(null)
const dragOverIndex = ref(null)

// 计算所有课件
const allMaterials = computed(() => {
    return localChapters.value.flatMap(chapter =>
        (chapter.materials || []).map(material => ({
            ...material,
            chapterTitle: chapter.title
        }))
    )
})

// 初始化
onMounted(async () => {
    await fetchCategories()
    if (props.isEditMode && props.initialData) {
        await initFromExistingCourse()
    }
})

// 获取分类
const fetchCategories = async () => {
    try {
        loadingCategories.value = true
        const response = await axios.get("/course-categories/")
        categories.value = response.data.data || []

        if (props.isEditMode && props.initialData?.category) {
            form.category = props.initialData.category.id
        }
    } catch (error) {
        ElMessage.error("获取分类失败: " + (error.response?.data?.message || error.message))
    } finally {
        loadingCategories.value = false
    }
}

// 初始化已有课程
const initFromExistingCourse = async () => {
    try {
        courseId.value = props.initialData.id
        form.title = props.initialData.title
        form.description = props.initialData.description
        form.coverUrl = props.initialData.cover_url

        // 确保分类数据已加载后再设置当前分类
        await fetchCategories()

        // 设置当前分类（确保使用与选项相同的类型）
        if (props.initialData.category) {
            form.category = props.initialData.category.id
        }

        // 获取章节和课件数据
        const [chaptersRes, carouselsRes] = await Promise.all([
            axios.get(`/courses/${courseId.value}/chapters/`),
            axios.get(`/courses/${courseId.value}/carousels/`)
        ])

        // 获取每个章节的课件
        const chaptersWithMaterials = await Promise.all(
            (chaptersRes.data.data || []).map(async chapter => {
                const materialsRes = await axios.get(`/chapters/${chapter.id}/materials/`)
                return {
                    ...chapter,
                    tempId: chapter.id,
                    materials: materialsRes.data.data || []
                }
            })
        )

        localChapters.value = chaptersWithMaterials
        tempCarousels.value = (carouselsRes.data.data || []).map(item => ({
            ...item,
            tempId: item.id || Date.now()
        }))

    } catch (error) {
        ElMessage.error('初始化课程失败: ' + error.message)
    }
}

// 添加章节
const addChapter = () => {
    const newChapter = {
        tempId: Date.now(),
        title: `第${localChapters.value.length + 1}章`,
        description: '',
        sort_order: localChapters.value.length,
        materials: []
    }
    localChapters.value = [...localChapters.value, newChapter]
    activeChapter.value = newChapter.tempId
}

// 删除章节
const removeChapter = (tempId) => {
    ElMessageBox.confirm('确定删除此章节吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        localChapters.value = localChapters.value.filter(c => c.tempId !== tempId)
        if (activeChapter.value === tempId) {
            activeChapter.value = localChapters.value[0]?.tempId || null
        }
    }).catch(() => { })
}

// 第一步：暂存课程信息
const submitCourseInfo = async () => {
    try {
        await formRef.value.validate()
        currentStep.value = 2
    } catch (error) {
        ElMessage.error('请完善课程信息')
    }
}

// 第二步：暂存章节
const saveChapters = () => {
    currentStep.value = 3
}

// 第三步：最终提交
const completeCreation = async () => {
    try {
        submitting.value = true

        const payload = {
            course: {
                title: form.title,
                category_id: form.category,
                description: form.description,
                cover_url: form.coverUrl,
                teacher_id: props.teacherId,
                status: 'draft'
            },
            chapters: localChapters.value.map(ch => ({
                title: ch.title,
                description: ch.description || '',
                sort_order: ch.sort_order || 0,
                materials: (ch.materials || []).map(m => ({
                    title: m.title,
                    type: m.type,
                    url: m.url,
                    size: m.size || 0,
                    duration: m.duration || 0,
                    sort_order: m.sort_order || 0
                }))
            })),
            carousels: tempCarousels.value.map(item => ({
                image_url: item.image_url,
                sort_order: item.sort_order || 0
            }))
        }

        const url = props.isEditMode
            ? `/courses/${courseId.value}/batch-update/`
            : `/courses/batch-create/`

        const response = await axios.post(url, payload)

        if (response.data.success) {
            ElMessage.success({
                message: props.isEditMode ? '课程更新成功' : '课程创建成功',
                duration: 1500,
                onClose: () => router.push('/course-manage')
            })
            emit('submit', response.data.data)
        }
    } catch (error) {
        console.error('Error details:', error.response?.data)
        ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
    } finally {
        submitting.value = false
    }
}

// 取消编辑
const cancel = () => {
    ElMessageBox.confirm('确定放弃当前编辑吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        emit('cancel')
    }).catch(() => { })
}

// 封面图上传
const beforeCoverUpload = (file) => {
    const isImage = file.type.startsWith("image/")
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isImage || !isLt2M) {
        ElMessage.error("请上传小于2MB的图片")
        return false
    }
    return true
}

const handleCoverUploadSuccess = (response) => {
    if (response.code === 200) {
        form.coverUrl = response.data.url
    }
}

// 轮播图上传
const beforeCarouselUpload = (file) => {
    const isImage = file.type.startsWith("image/")
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isImage || !isLt5M) {
        ElMessage.error("请上传小于5MB的图片")
        return false
    }
    return true
}

const handleCarouselUploadSuccess = (response) => {
    if (response.code === 200) {
        tempCarousels.value = [
            ...tempCarousels.value,
            {
                tempId: Date.now(),
                image_url: response.data.url,
                sort_order: tempCarousels.value.length
            }
        ]
    }
}

// 课件上传前处理
const beforeMaterialUpload = (file) => {
    const isLt50M = file.size / 1024 / 1024 < 50
    if (!isLt50M) {
        ElMessage.error('文件大小不能超过50MB')
        return false
    }
    materialForm.value.type = getFileType(file.name)
    materialForm.value.size = file.size
    return true
}

// 课件上传成功处理
const handleMaterialUploadSuccess = (response) => {
    if (response.code === 200) {
        materialForm.value.url = response.data.url
        materialForm.value.size = response.data.size
        ElMessage.success('文件上传成功')
    } else {
        ElMessage.error(response.message || '文件上传失败')
    }
}

// 添加课件
const addMaterial = () => {
    if (!materialForm.value.title.trim()) {
        ElMessage.error('请输入课件标题')
        return
    }
    if (!materialForm.value.url) {
        ElMessage.error('请先上传文件')
        return
    }

    if (localChapters.value.length === 0) {
        localChapters.value.push({
            tempId: Date.now(),
            title: '默认章节',
            description: '',
            sort_order: 0,
            materials: []
        })
    }

    const newMaterial = {
        ...materialForm.value,
        tempId: Date.now()
    }

    localChapters.value[0].materials = [
        ...(localChapters.value[0].materials || []),
        newMaterial
    ]

    showMaterialDialog.value = false
    materialForm.value = {
        title: '',
        type: 'doc',
        url: '',
        size: 0,
        duration: 0,
        sort_order: 0
    }
}

// 删除课件
const removeMaterial = (materialId) => {
    ElMessageBox.confirm('确定删除此课件吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        localChapters.value = localChapters.value.map(chapter => ({
            ...chapter,
            materials: (chapter.materials || []).filter(
                m => m.id !== materialId && m.tempId !== materialId
            )
        }))
        ElMessage.success('删除成功')
    }).catch(() => { })
}

// 预览课件
const previewMaterial = async (material) => {
    try {
        // 设置当前预览的课件
        previewMaterial.value = material

        // 如果是文本文件，预先加载内容
        if (isTextFile(material.url)) {
            try {
                const response = await axios.get(material.url, { responseType: 'text' })
                textContent.value = response.data
            } catch (error) {
                textContent.value = '无法加载文本内容'
            }
        }

        showPreviewDialog.value = true
    } catch (error) {
        ElMessage.error('预览失败: ' + error.message)
    }
}

// 下载文件
const downloadFile = (url, name) => {
    try {
        if (!url) {
            ElMessage.error('无效的文件URL')
            return
        }

        // 创建一个隐藏的iframe来触发下载
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        iframe.src = url
        document.body.appendChild(iframe)

        // 设置超时自动移除iframe
        setTimeout(() => {
            document.body.removeChild(iframe)
        }, 5000)

        ElMessage.success('开始下载文件')
    } catch (error) {
        ElMessage.error('下载失败: ' + error.message)
    }
}

// 获取文件类型
const getFileType = (filename) => {
    const extension = filename?.split('.').pop()?.toLowerCase() || ''
    const typeMap = {
        'pdf': 'doc', 'doc': 'doc', 'docx': 'doc', 'ppt': 'doc', 'pptx': 'doc',
        'xls': 'doc', 'xlsx': 'doc', 'txt': 'doc', 'md': 'doc',
        'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'gif': 'image',
        'bmp': 'image', 'webp': 'image', 'svg': 'image',
        'mp4': 'video', 'mov': 'video', 'avi': 'video', 'wmv': 'video',
        'flv': 'video', 'mkv': 'video', 'webm': 'video',
        'mp3': 'audio', 'wav': 'audio', 'ogg': 'audio', 'aac': 'audio',
        'flac': 'audio', 'm4a': 'audio'
    }
    return typeMap[extension] || 'doc'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i])
}

// 获取标签类型
const getMaterialTagType = (type) => {
    const types = {
        doc: '',
        video: 'danger',
        image: 'success',
        audio: 'warning'
    }
    return types[type] || ''
}

// 判断是否是Office文件
const isOfficeFile = (url) => {
    const ext = getFileExt(url)
    return ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)
}

// 判断是否是PDF文件
const isPdfFile = (url) => {
    return getFileExt(url) === 'pdf'
}

// 判断是否是文本文件
const isTextFile = (url) => {
    if (!url) return false
    const ext = getFileExt(url)
    return ['txt', 'md', 'json', 'xml', 'csv', 'html', 'htm', 'js', 'css', 'log'].includes(ext)
}

// 获取文件扩展名
const getFileExt = (url) => {
    if (!url) return ''
    // 去除URL参数
    const cleanUrl = url.split('?')[0].split('#')[0]
    return cleanUrl.split('.').pop()?.toLowerCase() || ''
}

// 获取视频MIME类型
const getVideoMimeType = (url) => {
    const ext = getFileExt(url)
    const types = {
        'mp4': 'video/mp4',
        'webm': 'video/webm',
        'ogg': 'video/ogg',
        'mov': 'video/quicktime'
    }
    return types[ext] || 'video/mp4'
}

// 获取音频MIME类型
const getAudioMimeType = (url) => {
    const ext = getFileExt(url)
    const types = {
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'ogg': 'audio/ogg',
        'aac': 'audio/aac'
    }
    return types[ext] || 'audio/mpeg'
}

// 拖拽相关方法
const handleDragStart = (index) => {
    dragIndex.value = index
}

const handleDragOver = (index) => {
    dragOverIndex.value = index
    event.preventDefault()
}

const handleDrop = (index) => {
    if (dragIndex.value !== null && dragIndex.value !== index) {
        const newCarousels = [...tempCarousels.value]
        const [removed] = newCarousels.splice(dragIndex.value, 1)
        newCarousels.splice(index, 0, removed)

        // 更新排序值
        tempCarousels.value = newCarousels.map((item, idx) => ({
            ...item,
            sort_order: idx
        }))
    }
    dragIndex.value = null
    dragOverIndex.value = null
}

const handleDragEnter = (index) => {
    if (dragIndex.value !== null && dragIndex.value !== index) {
        dragOverIndex.value = index
    }
}

const handleDragLeave = (index) => {
    if (dragIndex.value !== null && dragIndex.value !== index) {
        dragOverIndex.value = null
    }
}

// 删除轮播图
const removeTempCarousel = (tempId) => {
    tempCarousels.value = tempCarousels.value.filter(item => item.tempId !== tempId)
}

// 删除封面图
const removeCover = () => {
    form.coverUrl = ''
}
</script>

<style scoped>
.course-form-container {
    padding: 20px;
}

.creation-steps {
    margin-bottom: 30px;
}

.step-container {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.cover-upload-container {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 10px;
}

.cover-preview {
    position: relative;
    width: 200px;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #dcdfe6;
}

.cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cover-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.chapter-management {
    padding: 20px;
}

.chapter-header {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.chapter-content {
    padding: 10px;
}

.material-management {
    padding: 20px;
}

.carousel-management {
    padding: 20px;
}

.carousel-item {
    position: relative;
    width: 200px;
    height: 120px;
    margin: 10px;
    display: inline-block;
    transition: transform 0.2s;
    cursor: move;
    border: 2px solid transparent;
}

.carousel-item.dragging {
    opacity: 0.5;
}

.carousel-item.drag-over {
    border: 2px dashed #409eff;
    transform: scale(1.05);
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.carousel-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px;
    display: flex;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
}

.carousel-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.step-actions {
    margin-top: 20px;
    text-align: center;
}

.upload-demo {
    width: 100%;
}

.el-upload__tip {
    margin-top: 10px;
    color: var(--el-text-color-secondary);
    font-size: 12px;
}

.no-preview {
    text-align: center;
    padding: 40px 0;
}

.ghost {
    opacity: 0.5;
    background: #c8ebfb;
}

.el-table {
    margin-top: 20px;
}

.el-collapse {
    margin-top: 20px;
}

.el-collapse-item {
    margin-bottom: 10px;
}

.el-tabs {
    margin-top: 20px;
}

.preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70vh;
    overflow: auto;
}

.no-preview {
    text-align: center;
    padding: 40px 0;
}

.no-preview p {
    margin-bottom: 20px;
    font-size: 16px;
    color: var(--el-text-color-regular);
}
</style>