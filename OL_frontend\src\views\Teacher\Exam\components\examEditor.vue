<template>
    <div class="exam-editor">
        <el-form :model="form" label-width="120px" ref="formRef">
            <!-- 基础信息 -->
            <el-card class="info-card">
                <template #header>
                    <div class="card-header">
                        <span>考试基本信息</span>
                    </div>
                </template>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="考试名称" prop="title" required>
                            <el-input v-model="form.title" placeholder="请输入考试名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="关联试卷" prop="paper" required>
                            <div class="paper-selector">
                                <el-select v-model="form.paper" filterable remote reserve-keyword placeholder="请搜索并选择试卷"
                                    :remote-method="searchPapers" :loading="paperLoading" style="width: 100%">
                                    <el-option v-for="item in paperOptions" :key="item.id"
                                        :label="`${item.title} (${item.teacher_name})`" :value="item.id" />
                                </el-select>
                                <el-checkbox v-model="showAllPapers" style="margin-left: 10px;"
                                    @change="handleShowAllChange">
                                    显示所有试卷
                                </el-checkbox>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="考试描述" prop="description">
                    <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入考试描述" />
                </el-form-item>
            </el-card>

            <!-- 时间设置 -->
            <el-card class="time-card">
                <template #header>
                    <div class="card-header">
                        <span>考试时间设置</span>
                    </div>
                </template>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="开始时间" prop="start_time" required>
                            <el-date-picker v-model="form.start_time" type="datetime" placeholder="选择考试开始时间"
                                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="结束时间" prop="end_time" required>
                            <el-date-picker v-model="form.end_time" type="datetime" placeholder="选择考试结束时间"
                                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="考试时长(分钟)" prop="duration_minutes" required>
                    <el-input-number v-model="form.duration_minutes" :min="30" :max="300" controls-position="right"
                        placeholder="请输入考试时长" />
                </el-form-item>
            </el-card>

            <!-- 考试设置 -->
            <el-card class="settings-card">
                <template #header>
                    <div class="card-header">
                        <span>考试设置</span>
                    </div>
                </template>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="及格分数" prop="pass_score">
                            <el-input-number v-model="form.pass_score" :min="0" :max="100" controls-position="right"
                                placeholder="请输入及格分数" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="总分" prop="total_score">
                            <el-input-number v-model="form.total_score" :min="1" :max="200" controls-position="right"
                                placeholder="请输入总分" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="允许中断考试" prop="allow_break">
                            <el-switch v-model="form.allow_break" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="中断计入时长" prop="break_affect_time" v-if="form.allow_break">
                            <el-switch v-model="form.break_affect_time" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="允许多次考试" prop="allow_multiple">
                            <el-switch v-model="form.allow_multiple" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="显示答案解析" prop="show_analysis">
                            <el-switch v-model="form.show_analysis" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="错题入错题本" prop="add_to_wrong">
                            <el-switch v-model="form.add_to_wrong" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="成绩显示方式" prop="show_score_type">
                            <el-select v-model="form.show_score_type" style="width: 100%">
                                <el-option label="立即显示" :value="1" />
                                <el-option label="考试后显示" :value="2" />
                                <el-option label="不显示" :value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="防作弊设置" prop="anti_cheat_options">
                    <el-checkbox-group v-model="form.anti_cheat_options">
                        <el-checkbox label="shuffle_questions">题目乱序</el-checkbox>
                        <el-checkbox label="shuffle_options">选项乱序</el-checkbox>
                        <el-checkbox label="screen_watermark">屏幕水印</el-checkbox>
                        <el-checkbox label="disable_copy">禁止复制</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-card>

            <!-- 参与人员 -->
            <el-card class="participants-card">
                <template #header>
                    <div class="card-header">
                        <span>参与人员</span>
                    </div>
                </template>

                <el-form-item label="参与班级" prop="classes">
                    <el-select v-model="form.classes" multiple filterable remote reserve-keyword placeholder="请搜索并选择班级"
                        :remote-method="searchClasses" :loading="classLoading" style="width: 100%">
                        <el-option v-for="item in classOptions" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>

                <el-form-item label="批阅人员" prop="correctors">
                    <el-select v-model="form.correctors" multiple filterable remote reserve-keyword
                        placeholder="请搜索并选择批阅教师" :remote-method="searchTeachers" :loading="teacherLoading"
                        style="width: 100%">
                        <el-option v-for="item in teacherOptions" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </el-card>

            <!-- 考试说明 -->
            <el-card class="instructions-card">
                <template #header>
                    <div class="card-header">
                        <span>考试说明</span>
                    </div>
                </template>

                <el-form-item prop="instructions">
                    <el-input v-model="form.instructions" type="textarea" :rows="5" placeholder="请输入考试说明，将显示给学生" />
                </el-form-item>
            </el-card>

            <!-- 底部操作按钮 -->
            <div class="form-actions">
                <el-button type="primary" @click="submitForm">
                    {{ isEditMode ? '保存修改' : '创建考试' }}
                </el-button>
                <el-button @click="cancel">取消</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'
import { useUserStore } from '@/stores/user'

const props = defineProps({
    examId: {
        type: String,
        default: null
    }
})

const emit = defineEmits(['success', 'cancel'])

const userStore = useUserStore()

// 表单数据
const form = ref({
    title: '',
    description: '',
    paper: null,
    start_time: '',
    end_time: '',
    duration_minutes: 90,
    pass_score: 60,
    total_score: 100,
    allow_break: false,
    break_affect_time: false,
    allow_multiple: false,
    show_analysis: false,
    add_to_wrong: false,
    show_score_type: 1,
    anti_cheat_options: [],
    classes: [],
    correctors: [],
    instructions: ''
})

// 搜索选项
const showAllPapers = ref(false)
const paperOptions = ref([])
const paperLoading = ref(false)
const classOptions = ref([])
const classLoading = ref(false)
const teacherOptions = ref([])
const teacherLoading = ref(false)

const formRef = ref(null)
const isEditMode = computed(() => !!props.examId)

// 获取考试详情
const fetchExamDetail = async () => {
    try {
        const response = await axios.get(`/exams/${props.examId}/`)
        form.value = response.data

        // 初始化防作弊选项（字符串转数组）
        if (form.value.anti_cheat_options) {
            form.value.anti_cheat_options = form.value.anti_cheat_options.split(',')
        } else {
            form.value.anti_cheat_options = []
        }

        // 获取关联试卷选项
        if (form.value.paper) {
            const paperRes = await axios.get(`/exam-papers/${form.value.paper}/`)
            paperOptions.value = [paperRes.data]
        }

        // 获取关联班级选项
        if (form.value.classes && form.value.classes.length > 0) {
            const classRes = await axios.get('/classes/', {
                params: { ids: form.value.classes.join(',') }
            })
            classOptions.value = classRes.data.data || []
        }

        // 获取批阅教师选项
        if (form.value.correctors && form.value.correctors.length > 0) {
            const teacherRes = await axios.get('/teachers/', {
                params: { ids: form.value.correctors.join(',') }
            })
            teacherOptions.value = teacherRes.data.data || []
        }
    } catch (error) {
        ElMessage.error('获取考试详情失败: ' + (error.response?.data?.message || error.message))
        emit('cancel')
    }
}

// 搜索试卷
const searchPapers = async (query = '') => {
    try {
        paperLoading.value = true
        const params = {
            search: query,
            status: 'published'
        }

        if (!showAllPapers.value) {
            params.teacher_id = userStore.username
        }

        const response = await axios.get('/exam-papers/', { params })
        paperOptions.value = response.data.results
    } catch (error) {
        ElMessage.error('搜索试卷失败: ' + (error.response?.data?.message || error.message))
        paperOptions.value = []
    } finally {
        paperLoading.value = false
    }
}

// 切换显示全部试卷
const handleShowAllChange = (value) => {
    searchPapers('')
}

// 搜索班级
const searchClasses = async (query = '') => {
    try {
        classLoading.value = true
        const response = await axios.get('/classes/', {
            params: { search: query }
        })
        classOptions.value = response.data.data || []
    } catch (error) {
        ElMessage.error('搜索班级失败: ' + (error.response?.data?.message || error.message))
        classOptions.value = []
    } finally {
        classLoading.value = false
    }
}

// 搜索教师
const searchTeachers = async (query = '') => {
    try {
        teacherLoading.value = true
        const response = await axios.get('/teachers/', {
            params: { search: query }
        })
        teacherOptions.value = response.data.data.map(t => ({
            id: t.username,
            name: t.username
        }))
    } catch (error) {
        ElMessage.error('搜索教师失败: ' + (error.response?.data?.message || error.message))
        teacherOptions.value = []
    } finally {
        teacherLoading.value = false
    }
}

// 提交表单
const submitForm = async () => {
    try {
        // 验证表单
        if (!form.value.title) {
            ElMessage.error('请输入考试名称')
            return
        }

        if (!form.value.paper) {
            ElMessage.error('请选择关联试卷')
            return
        }

        if (!form.value.start_time || !form.value.end_time) {
            ElMessage.error('请设置考试时间')
            return
        }

        if (new Date(form.value.start_time) >= new Date(form.value.end_time)) {
            ElMessage.error('结束时间必须晚于开始时间')
            return
        }

        // 准备数据
        const payload = {
            ...form.value,
            teacher: userStore.username,
            paper: form.value.paper,
            classes: form.value.classes,
            correctors: form.value.correctors,
            anti_cheat_options: form.value.anti_cheat_options.join(',')
        }

        // 提交数据
        let response
        if (isEditMode.value) {
            response = await axios.put(`/exams/${props.examId}/`, payload)
        } else {
            response = await axios.post('/exams/', payload)
        }

        ElMessage.success(isEditMode.value ? '考试更新成功' : '考试创建成功')
        emit('success', response.data)
    } catch (error) {
        ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
    }
}

// 取消
const cancel = () => {
    emit('cancel')
}

// 初始化
onMounted(() => {
    // 初始化教师搜索
    searchTeachers('').then(() => {
        // 默认添加当前用户为批阅人
        if (teacherOptions.value.length === 0 && userStore.username) {
            teacherOptions.value = [{
                id: userStore.username,
                name: userStore.username
            }]
            form.value.correctors = [userStore.username]
        }
    })

    if (isEditMode.value) {
        fetchExamDetail()
    } else {
        // 新建考试时设置默认值
        form.value.correctors = [userStore.username]
        searchPapers()
    }
})
</script>

<style scoped>
.exam-editor {
    padding: 20px;
}

.info-card,
.time-card,
.settings-card,
.participants-card,
.instructions-card {
    margin-bottom: 20px;
}

.card-header {
    font-weight: bold;
}

.form-actions {
    margin-top: 20px;
    text-align: center;
}

.paper-selector {
    display: flex;
    align-items: center;
}
</style>