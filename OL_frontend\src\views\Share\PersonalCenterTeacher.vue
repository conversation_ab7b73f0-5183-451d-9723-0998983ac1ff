<template>
    <div class="teacher-center">
        <!-- 个人信息区域 -->
        <div class="user-info-section">
            <div class="user-avatar" @click="showAvatarDialog = true">
                <el-avatar :size="100" :icon="UserFilled" :src="userInfo.avatar" />
                <div class="avatar-overlay">
                    <el-icon>
                        <EditPen />
                    </el-icon>
                </div>
            </div>
            <div class="user-details">
                <h2 class="teacher-name">{{ userInfo.nickname }}</h2>
                <p class="teacher-id">教师ID: {{ userInfo.teacherId }}</p>
            </div>
        </div>

        <!-- 功能按钮区域 -->
        <div class="action-buttons">
            <el-button type="primary" size="large" @click="navigateToCourseManagement">
                <el-icon>
                    <Collection />
                </el-icon>
                <span>课程管理</span>
            </el-button>
            <el-button type="success" size="large" @click="navigateToQuestionBank">
                <el-icon>
                    <Document />
                </el-icon>
                <span>题考中心</span>
            </el-button>
            <el-button type="warning" size="large" @click="navigateToExamEditor">
                <el-icon>
                    <EditPen />
                </el-icon>
                <span>考试编辑</span>
            </el-button>
        </div>

        <!-- 学生账号管理 -->
        <div class="student-management">
            <div class="section-header">
                <h3 class="section-title">学生账号管理</h3>
                <el-input v-model="searchQuery" placeholder="搜索学号或姓名" style="width: 240px" clearable
                    @clear="fetchStudents" @keyup.enter="fetchStudents">
                    <template #append>
                        <el-button :icon="Search" @click="fetchStudents" />
                    </template>
                </el-input>
            </div>
            <el-table :data="studentList" border style="width: 100%" v-loading="loading">
                <el-table-column prop="username" label="学号" width="320" sortable />
                <el-table-column prop="username" label="昵称" width="320" />
                <!-- <el-table-column prop="className" label="班级" /> -->
                <el-table-column prop="status" label="状态" width="300" sortable>
                    <template #default="{ row }">
                        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                            {{ row.status === 1 ? '启用' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="220" fixed="right">
                    <template #default="{ row }">
                        <el-button size="small" @click="resetPassword(row)">
                            <el-icon>
                                <Refresh />
                            </el-icon>
                            <span>重置密码</span>
                        </el-button>
                        <el-button size="small" :type="row.status === 1 ? 'danger' : 'success'"
                            @click="toggleAccountStatus(row)">
                            <el-icon>
                                <CircleCloseFilled v-if="row.status === 1" />
                                <CircleCheckFilled v-else />
                            </el-icon>
                            <span>{{ row.status === 1 ? '禁用' : '启用' }}</span>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalStudents"
                :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                @size-change="fetchStudents" @current-change="fetchStudents" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
    UserFilled,
    EditPen,
    Collection,
    Document,
    Refresh,
    CircleCloseFilled,
    CircleCheckFilled,
    Search
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = ref({
    nickname: '张老师',
    teacherId: userStore.username || 'T123456',
    avatar: ''
})

// 学生列表数据
const studentList = ref([])
const loading = ref(false)
const searchQuery = ref('')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalStudents = ref(0)

// 获取学生列表
const fetchStudents = async () => {
    try {
        loading.value = true
        const params = {
            page: currentPage.value,
            size: pageSize.value,
            search: searchQuery.value
        }

        const response = await axios.get('/api/students/info', { params })
        studentList.value = response.data.data
        totalStudents.value = response.data.total || 0
    } catch (error) {
        ElMessage.error('获取学生列表失败: ' + error.message)
    } finally {
        loading.value = false
    }
}

// 导航方法
const navigateToCourseManagement = () => {
    router.push('/course-manage')
}

const navigateToQuestionBank = () => {
    router.push('/tikao')
}

const navigateToExamEditor = () => {
    router.push('/exam-edit')
}

// 重置学生密码
const resetPassword = async (student) => {
    try {
        await ElMessageBox.confirm(
            `确定要重置学生 ${student.name || student.username} 的密码吗?`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )
        await axios.post(`/api/students/${student.id}/reset-password/`)
        ElMessage.success(`已重置学生 ${student.name || student.username} 的密码为用户名`)
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('操作失败: ' + error.message)
        }
    }
}

// 切换账号状态
const toggleAccountStatus = async (student) => {
    try {
        const action = student.status === 1 ? '禁用' : '启用'
        await ElMessageBox.confirm(
            `确定要${action}学生 ${student.name || student.username} 的账号吗?`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        await axios.post(`/api/students/${student.id}/toggle-status/`)
        ElMessage.success(`已${action}学生 ${student.name || student.username} 的账号`)
        fetchStudents() // 刷新列表
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('操作失败: ' + error.message)
        }
    }
}

// 初始化加载数据
onMounted(() => {
    fetchStudents()
})
</script>

<style scoped>
.teacher-center {
    width: 100%;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.user-info-section {
    background-color: var(--el-color-primary);
    color: white;
    padding: 30px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.user-avatar {
    margin-right: 30px;
    position: relative;
    cursor: pointer;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.user-avatar:hover .avatar-overlay {
    opacity: 1;
}

.teacher-name {
    font-size: 24px;
    margin: 0 0 10px 0;
}

.teacher-id {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.action-buttons .el-button {
    flex: 1;
    padding: 12px 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-title {
    font-size: 18px;
    margin: 0;
    color: var(--el-text-color-primary);
}

.student-management {
    margin-bottom: 20px;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.el-table {
    margin-bottom: 20px;
}

.el-button span {
    margin-left: 5px;
}
</style>