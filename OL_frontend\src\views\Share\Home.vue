<template>
    <div class="home-page" v-if="!isLoading">
        <!-- 课程轮播图 -->
        <div class="banner-section">
            <el-carousel :interval="4000" type="card" height="350px">
                <el-carousel-item v-for="banner in banners" :key="banner.id">
                    <div class="banner-content"
                        :style="{ backgroundImage: banner.bgUrl ? `url(${banner.bgUrl})` : 'none', backgroundColor: '#409EFF' }">
                        <div class="banner-overlay"></div>
                        <div class="banner-info">
                            <h2 class="banner-title">{{ banner.title }}</h2>
                            <p class="banner-desc">{{ banner.description }}</p>
                            <el-button type="primary" size="large" @click="viewCourse(banner.id)">查看课程</el-button>
                        </div>
                        <div class="banner-image">
                            <el-icon :size="120">
                                <Collection />
                            </el-icon>
                        </div>
                    </div>
                </el-carousel-item>
            </el-carousel>
        </div>

        <!-- 课程分类 + 筛选按钮 -->
        <div class="category-section">
            <div class="section-header">
                <h2 class="section-title">课程分类</h2>
                <!-- <el-radio-group v-model="activeTab" size="small">
                    <el-radio-button v-for="cat in categories" :key="cat.id" :label="cat.id.toString()">
                        {{ cat.name }}
                    </el-radio-button>
                </el-radio-group> -->
            </div>

            <div class="category-tabs">
                <el-tabs v-model="activeTab">
                    <el-tab-pane v-for="cat in categories" :key="cat.id" :label="cat.name" :name="cat.id.toString()">
                        <div class="course-grid">
                            <el-row :gutter="20" v-if="getCoursesByCategory(cat.id).length > 0">
                                <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="course in getCoursesByCategory(cat.id)"
                                    :key="course.id">
                                    <courseCard :course="course" />
                                </el-col>
                            </el-row>
                            <el-empty v-else description="暂无该分类课程" />
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>

        <!-- 推荐 / 热门 / 最新 课程 -->
        <section v-for="section in dynamicSections" :key="section.key" :class="section.key + '-section'">
            <div class="section-header">
                <h2 class="section-title">{{ section.title }}</h2>
                <el-link type="primary" @click="viewAll(section.key)">
                    查看全部 <el-icon>
                        <ArrowRight />
                    </el-icon>
                </el-link>
            </div>
            <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="course in section.list" :key="course.id">
                    <courseCard :course="course" />
                </el-col>
            </el-row>
        </section>

        <!-- 跑马灯通知 -->
        <div class="announcement-section">
            <el-alert title="最新通知" type="info" :closable="false" show-icon>
                <div class="marquee">
                    <span v-for="(notice, idx) in notifications" :key="idx">
                        {{ notice.notification_text }}
                        <el-divider direction="vertical" />
                    </span>
                </div>
            </el-alert>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'
import { Collection, ArrowRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import courseCard from '@/HomePage/courseCard.vue'

const router = useRouter()

// 状态变量
const isLoading = ref(true)
const banners = ref([])
// 课程分类
const categories = ref([
    { id: 1, name: '计算机科学' },
    { id: 2, name: '数学' },
    { id: 3, name: '物理' }
])
const allCourses = ref([])
// 公告数据
const notifications = ref([])
const notificationForm = ref({
    id: null,
    notification_text: '',
    publish_time: ''
})

// 使用activeTab统一控制radio和tabs
const activeTab = ref('1')

// 推荐/热门/最新 分类课程
const recommendedCourses = computed(() =>
    allCourses.value.filter((c) => c.isRecommended)
)
const popularCourses = computed(() =>
    allCourses.value.filter((c) => c.isPopular)
)
const latestCourses = computed(() =>
    allCourses.value.filter((c) => c.isNew)
)

// 聚合定义用于渲染 section
const dynamicSections = computed(() => [
    { key: 'recommended', title: '推荐课程', list: recommendedCourses.value },
    { key: 'popular', title: '热门课程', list: popularCourses.value },
    { key: 'latest', title: '最新课程', list: latestCourses.value }
])

// 根据分类 & 排序筛选课程
function getCoursesByCategory(categoryId) {
    if (!allCourses.value) {
        console.log('allCourses is not initialized yet')
        return []
    }
    // console.log(allCourses.value.filter((c) => c.categoryId == categoryId))
    return allCourses.value.filter((c) => c.categoryId === categoryId)
}

// 导航到课程详情
const viewCourse = (courseId) => {
    router.push({ path: '/course/detail', query: { id: courseId } })
}

// '查看全部' 跳转函数，需后端实现对应页面
const viewAll = (sectionKey) => {
    router.push({ path: `/courses/${sectionKey}` })
}

// 获取后端数据
async function fetchInitialData() {
    isLoading.value = true
    try {
        const courseRes = await axios.get('/api/courses_list')
        allCourses.value = courseRes.data.map(c => ({
            id: c.id,
            title: c.title,
            description: c.description,
            cover_url: c.cover_url,
            update_time: c.update_time,
            categoryId: c.category.id,
            enroll_count: c.enroll_count,
            favorite_count: c.favorite_count,
            isRecommended: c.recommend_level >= 1,
            isPopular: c.enroll_count >= 1,
            isNew: Date.now() - new Date(c.update_time).getTime() < 30 * 24 * 60 * 60 * 1000
        }))
        
        console.log('获取到的课程数据:', allCourses.value)

        banners.value = allCourses.value.filter(c => c.isRecommended).slice(0, 3).map(c => ({
            id: c.id,
            title: c.title,
            description: c.description,
            bgUrl: c.cover_url
        }))
        console.log('获取到的轮播图数据:', banners.value)

        fetchNotifications()
        // 设置默认激活的标签页
        if (categories.value.length > 0) {
            activeTab.value = categories.value[0].id.toString()
        }
    } catch (e) {
        console.error('获取首页数据失败', e)
    } finally {
        isLoading.value = false
    }
}

// 获取通知列表
const fetchNotifications = async () => {
    try {
        const res = await axios.get('/api/notifications')
        notifications.value = res.data
    } catch (error) {
        ElMessage.error('获取通知列表失败')
        console.error(error)
    }
}


onMounted(fetchInitialData)
</script>

<style scoped>
.home-page {
    padding: 0 80px 40px 80px;
}

/* 轮播图样式 */
.banner-section {
    margin-bottom: 40px;
}

.banner-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    color: white;
    border-radius: 10px;
    position: relative;
    background-size: cover;
    background-position: center;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    /* 半透明黑色蒙版 */
}

.banner-info {
    position: relative;
    /* 确保内容在蒙版上方 */
    z-index: 1;
    color: white;
    flex: 1;
}

.banner-title {
    font-size: 28px;
    margin-bottom: 10px;
}

.banner-desc {
    font-size: 16px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.banner-image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 200px;
}

/* 课程分类样式 */
.category-section,
.recommended-section,
.popular-section,
.latest-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    font-size: 22px;
    color: var(--text-primary);
    margin: 0;
    position: relative;
    padding-left: 15px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.course-grid {
    margin-top: 20px;
}

/* 课程卡片样式 */
.course-card {
    background-color: var(--bg-card);
    border: 1px solid var(--course-card-border);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    cursor: pointer;
}

.course-card:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.course-image {
    width: 100%;
    height: 160px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--primary-light-9);
    position: relative;
}

.course-image .el-icon {
    color: var(--primary-color);
}

.course-info {
    padding: 15px;
}

.course-tags {
    margin-bottom: 10px;
}

.course-title {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: var(--text-primary);
}

.course-desc {
    margin: 0 0 15px 0;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    height: 63px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.course-meta {
    display: flex;
    font-size: 13px;
    color: var(--text-secondary);
}

.course-meta span {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.course-meta .el-icon {
    margin-right: 5px;
}

/* 徽章样式 */
.new-badge,
.hot-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.new-badge {
    background-color: var(--warning-color);
}

.hot-badge {
    background-color: var(--danger-color);
}

/* 跑马灯通知样式 */
.announcement-section {
    margin-bottom: 40px;

    /* 修改图标大小和位置 */
    :deep(.el-alert__icon) {
        font-size: 20px;
        /* 图标大小 */
        margin-right: 12px;
        /* 图标与文字的间距 */
        align-self:flex-start;
        /* 垂直居中 */
    }
}

.marquee {
    white-space: nowrap;
    overflow: hidden;
    animation: marquee 30s linear infinite;
}

.marquee span {
    margin-right: 20px;
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
    }
}
</style>