<template>
  <div class="my-question-bank">
    <div class="page-header">
      <h2 class="page-title">我的题库</h2>
      <el-button type="primary" @click="createNewBank" style="margin-left: 20px;">
        <el-icon>
          <Plus />
        </el-icon> 新建题库
      </el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="myQuestionBanks.length === 0" class="empty-state">
      <el-empty description="暂无题库数据" />
      <el-button type="primary" @click="createNewBank">创建第一个题库</el-button>
    </div>

    <el-card v-for="bank in myQuestionBanks" :key="bank.id" class="bank-card" shadow="hover"
      style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <div class="bank-title">
            <span style="font-size: 16px; font-weight: 500;">{{ bank.name }}</span>
            <el-tag :type="bank.visibility === 0 ? 'danger' : 'success'" size="small" effect="light">
              {{ bank.visibility === 0 ? '私有' : '公开' }}
            </el-tag>
          </div>
          <div>
            <el-button type="primary" size="small" @click="editBank(bank.id)" style="margin-right: 8px;">
              <el-icon>
                <Edit />
              </el-icon> 编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteBank(bank)">
              <el-icon>
                <Delete />
              </el-icon> 删除
            </el-button>
          </div>
        </div>
      </template>

      <div class="card-content">
        <div class="info-row">
          <span class="label">题库描述：</span>
          <span class="value">{{ bank.description || '暂无描述' }}</span>
        </div>
        <div class="info-row">
          <span class="label">试题数量：</span>
          <span class="value">{{ bank.question_count }} 道</span>
        </div>
        <div class="info-row">
          <span class="label">标签：</span>
          <span class="value">
            <el-tag v-for="tag in bank.tags" :key="tag" size="small" style="margin-right: 5px;">
              {{ tag }}
            </el-tag>
            <span v-if="bank.tags.length === 0">暂无标签</span>
          </span>
        </div>
        <div class="info-row">
          <span class="label">创建时间：</span>
          <span class="value">{{ formatTime(bank.create_time) }}</span>
        </div>
        <div class="info-row">
          <span class="label">更新时间：</span>
          <span class="value">{{ formatTime(bank.update_time) }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import axios from '@/utils/axios.js'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 题库数据
const myQuestionBanks = ref([])
const loading = ref(true)

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '未知时间'
  const date = new Date(timeStr)
  return date.toLocaleString()
}

// 获取题库列表
const fetchQuestionBanks = async () => {
  try {
    const teacherId = userStore.username
    loading.value = true;
    console.log('正在请求个人题库数据...');
    
    const response = await axios.get('/question-banks/mine', {
      params: { teacher_id: teacherId },
      headers: {
        Authorization: `Bearer ${userStore.token}`,
      },
    });
    
    console.log('个人题库API响应:', response.data);
    
    // 直接使用返回的数据，假设后端返回的格式是 { success: true, data: [...] }
    if (response.data.success && Array.isArray(response.data.data)) {
      myQuestionBanks.value = response.data.data;
    } else {
      myQuestionBanks.value = [];
      ElMessage.error('获取个人题库数据格式不正确');
      console.error('意外的数据格式:', response.data);
    }
  } catch (error) {
    console.error('请求个人题库错误:', error);
    console.error('错误详情:', error.response?.data);
    
    ElMessage.error('获取个人题库失败: ' + (error.response?.data?.message || error.message));
    if (error.response?.status === 401) {
      router.push('/login');
    }
  } finally {
    loading.value = false;
  }
};

// 新建题库
const createNewBank = () => {
  router.push({ path: '/questionbank-edit' })
}

const editBank = (id) => {
  router.push({ path: `/questionbank-edit/${id}` })
}

// 删除题库
const deleteBank = (bank) => {
  ElMessageBox.confirm(
    `确定要删除题库 "${bank.name}" 吗？此操作将删除题库中的所有试题且不可恢复。`,
    '警告',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          try {
            await axios.delete(`/question-banks/${bank.id}`)
            ElMessage.success('删除成功')
            await fetchQuestionBanks()
            done()
          } catch (error) {
            ElMessage.error('删除失败: ' + error.message)
          } finally {
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      }
    }
  )
}

// 初始化加载数据
onMounted(() => {
  fetchQuestionBanks()
})
</script>

<style scoped>
.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.bank-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.bank-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.bank-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.label {
  color: #606266;
  width: 80px;
  text-align: right;
  margin-right: 12px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  flex-grow: 1;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  gap: 20px;
}
</style>