<template>
  <div class="online-exam-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
      <div class="loading-text">正在加载考试数据...</div>
    </div>

    <!-- 错误状态 -->
    <el-alert v-else-if="error" :title="error" type="error" :closable="false" show-icon />

    <!-- 考试内容 -->
    <div v-else-if="examData.questions.length > 0">
      <!-- 考试时间倒计时 -->
      <div class="exam-header">
        <h2 class="exam-title">{{ examData.title }}</h2>
        <div class="exam-timer">
          <span>考试时间剩余: </span>
          <span class="timer">{{ formatTime(remainingTime) }}</span>
        </div>
      </div>

      <div class="exam-container">
        <!-- 左侧题目导航 -->
        <div class="question-nav">
          <h3 class="nav-title">题号导航</h3>

          <div class="question-type">
            <h4>单选题</h4>
            <div class="question-buttons">
              <button
                v-for="(question, index) in singleChoiceQuestions"
                :key="'single-' + index"
                class="question-btn"
                :class="{
                  active: currentQuestion === question.globalIndex,
                  answered: isQuestionAnswered(question.globalIndex),
                }"
                @click="goToQuestion(question.globalIndex)"
              >
                {{ index + 1 }}
              </button>
            </div>
          </div>

          <div class="question-type">
            <h4>多选题</h4>
            <div class="question-buttons">
              <button
                v-for="(question, index) in multiChoiceQuestions"
                :key="'multi-' + index"
                class="question-btn"
                :class="{
                  active: currentQuestion === question.globalIndex,
                  answered: isQuestionAnswered(question.globalIndex),
                }"
                @click="goToQuestion(question.globalIndex)"
              >
                {{ index + 1 }}
              </button>
            </div>
          </div>

          <div class="question-type">
            <h4>判断题</h4>
            <div class="question-buttons">
              <button
                v-for="(question, index) in trueFalseQuestions"
                :key="'tf-' + index"
                class="question-btn"
                :class="{
                  active: currentQuestion === question.globalIndex,
                  answered: isQuestionAnswered(question.globalIndex),
                }"
                @click="goToQuestion(question.globalIndex)"
              >
                {{ index + 1 }}
              </button>
            </div>
          </div>
        </div>

        <!-- 右侧题目内容 -->
        <div class="question-content">
          <div class="question-container">
            <div class="question-header">
              <h3>题{{ currentQuestion + 1 }}</h3>
              <span class="question-type-label">
                {{ getQuestionTypeLabel(examData.questions[currentQuestion].type) }}
                ({{ examData.questions[currentQuestion].score }}分)
              </span>
            </div>

            <div class="question-body">
              <p class="question-text">{{ examData.questions[currentQuestion].text }}</p>

              <!-- 单选题 -->
              <div
                v-if="examData.questions[currentQuestion].type === 'single'"
                class="options-container"
              >
                <div
                  v-for="(option, index) in examData.questions[currentQuestion].options"
                  :key="index"
                  class="option-item"
                >
                  <el-radio
                    v-model="userAnswers[currentQuestion]"
                    :label="String.fromCharCode(65 + index)"
                  >
                    {{ String.fromCharCode(65 + index) }}. {{ option }}
                  </el-radio>
                </div>
              </div>

              <!-- 多选题 -->
              <div
                v-else-if="examData.questions[currentQuestion].type === 'multi'"
                class="options-container"
              >
                <div
                  v-for="(option, index) in examData.questions[currentQuestion].options"
                  :key="index"
                  class="option-item"
                >
                  <el-checkbox
                    v-model="userAnswers[currentQuestion][String.fromCharCode(65 + index)]"
                  >
                    {{ String.fromCharCode(65 + index) }}. {{ option }}
                  </el-checkbox>
                </div>
              </div>

              <!-- 判断题 -->
              <div
                v-else-if="examData.questions[currentQuestion].type === 'tf'"
                class="options-container"
              >
                <div class="option-item">
                  <el-radio v-model="userAnswers[currentQuestion]" label="T"> 正确 </el-radio>
                </div>
                <div class="option-item">
                  <el-radio v-model="userAnswers[currentQuestion]" label="F"> 错误 </el-radio>
                </div>
              </div>
            </div>
          </div>

          <!-- 翻页控制 -->
          <div class="pagination-controls">
            <div class="page-info">
              {{ currentQuestion + 1 }} of {{ examData.questions.length }}
            </div>
            <div class="page-buttons">
              <el-button @click="prevQuestion" :disabled="currentQuestion === 0">
                <el-icon><ArrowLeft /></el-icon>
              </el-button>
              <el-button
                @click="nextQuestion"
                :disabled="currentQuestion === examData.questions.length - 1"
              >
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 保存和提交按钮 -->
          <div class="action-buttons">
            <el-button type="primary" @click="saveAnswers">保存</el-button>
            <el-button type="success" @click="confirmSubmit">完成考试</el-button>
          </div>
        </div>
      </div>

      <!-- 提交确认对话框 -->
      <el-dialog v-model="showSubmitDialog" title="确认提交" width="400px">
        <div class="submit-dialog-content">
          <p>您确定要提交本次考试吗？</p>
          <p>已答题数: {{ answeredCount }} / {{ examData.questions.length }}</p>
          <p v-if="unansweredCount > 0" class="warning-text">
            您还有 {{ unansweredCount }} 题未作答，提交后将无法修改答案。
          </p>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showSubmitDialog = false">继续答题</el-button>
            <el-button type="primary" @click="submitExam">确认提交</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import axios from 'axios'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 考试数据
const examData = ref({
  id: null,
  title: '',
  duration_minutes: 0,
  total_score: 0,
  questions: [],
})

// 加载状态
const loading = ref(true)
const error = ref(null)

// 用户答案
const userAnswers = ref([])

// 获取考试数据
const fetchExamData = async () => {
  try {
    const examId = route.query.exam_id
    if (!examId) {
      ElMessage.error('缺少考试ID参数')
      router.push('/my-courses')
      return
    }

    loading.value = true
    const response = await axios.get(`/api/student/exams/${examId}/`, {
      headers: { Authorization: `Bearer ${userStore.token}` },
    })

    if (response.data.success) {
      const data = response.data.data
      examData.value = {
        id: data.id,
        title: data.title,
        duration_minutes: data.duration_minutes,
        total_score: data.total_score,
        questions: data.questions.map((q) => ({
          id: q.id,
          paper_question_id: q.paper_question_id,
          type: mapQuestionType(q.type),
          text: q.content,
          options: q.options || [],
          score: q.score,
        })),
      }

      // 初始化答案和计时器
      initUserAnswers()
      remainingTime.value = data.duration_minutes * 60
      startTimer()
    } else {
      error.value = response.data.message || '获取考试数据失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    console.error('获取考试数据失败:', err)
    error.value = err.response?.data?.message || '网络错误，请稍后重试'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 映射题目类型
const mapQuestionType = (backendType) => {
  const typeMap = {
    single_choice: 'single',
    multiple_choice: 'multi',
    judgment: 'tf',
  }
  return typeMap[backendType] || backendType
}

// 初始化用户答案
const initUserAnswers = () => {
  userAnswers.value = []
  examData.value.questions.forEach((question, index) => {
    if (question.type === 'multi') {
      userAnswers.value[index] = {}
      question.options.forEach((_, optIndex) => {
        userAnswers.value[index][String.fromCharCode(65 + optIndex)] = false
      })
    } else {
      userAnswers.value[index] = ''
    }
  })
}

// 当前题目索引
const currentQuestion = ref(0)

// 考试剩余时间（秒）
const remainingTime = ref(0)

// 定时器
let timer = null

// 提交对话框显示控制
const showSubmitDialog = ref(false)

// 计算已答题数量
const answeredCount = computed(() => {
  return userAnswers.value.filter((answer, index) => {
    if (examData.value.questions[index].type === 'multi') {
      return Object.values(answer).some((val) => val === true)
    }
    return answer !== ''
  }).length
})

// 计算未答题数量
const unansweredCount = computed(() => {
  return examData.value.questions.length - answeredCount.value
})

// 计算属性：单选题列表
const singleChoiceQuestions = computed(() => {
  return examData.value.questions
    .map((question, index) => ({ ...question, globalIndex: index }))
    .filter((question) => question.type === 'single')
})

// 计算属性：多选题列表
const multiChoiceQuestions = computed(() => {
  return examData.value.questions
    .map((question, index) => ({ ...question, globalIndex: index }))
    .filter((question) => question.type === 'multi')
})

// 计算属性：判断题列表
const trueFalseQuestions = computed(() => {
  return examData.value.questions
    .map((question, index) => ({ ...question, globalIndex: index }))
    .filter((question) => question.type === 'tf')
})

// 检查题目是否已答
const isQuestionAnswered = (index) => {
  if (index >= userAnswers.value.length) return false

  if (examData.value.questions[index].type === 'multi') {
    return Object.values(userAnswers.value[index]).some((val) => val === true)
  }

  return userAnswers.value[index] !== ''
}

// 获取题目类型标签
const getQuestionTypeLabel = (type) => {
  switch (type) {
    case 'single':
      return '单选题'
    case 'multi':
      return '多选题'
    case 'tf':
      return '判断题'
    default:
      return '未知类型'
  }
}

// 格式化时间
const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
}

// 前往指定题目
const goToQuestion = (index) => {
  if (index >= 0 && index < examData.value.questions.length) {
    currentQuestion.value = index
  }
}

// 下一题
const nextQuestion = () => {
  if (currentQuestion.value < examData.value.questions.length - 1) {
    currentQuestion.value++
  }
}

// 上一题
const prevQuestion = () => {
  if (currentQuestion.value > 0) {
    currentQuestion.value--
  }
}

// 保存答案
const saveAnswers = () => {
  // 这里可以添加保存到本地存储或发送到服务器的逻辑
  ElMessage.success('答案已保存')
}

// 确认提交
const confirmSubmit = () => {
  showSubmitDialog.value = true
}

// 提交考试
const submitExam = async () => {
  try {
    showSubmitDialog.value = false

    // 构建答案数据
    const answers = {}
    examData.value.questions.forEach((question, index) => {
      answers[question.id] = userAnswers.value[index]
    })

    const submitData = {
      answers: answers,
      student_id: userStore.username,
    }

    const response = await axios.post(
      `/api/student/exams/${examData.value.id}/submit/`,
      submitData,
      {
        headers: { Authorization: `Bearer ${userStore.token}` },
      },
    )

    if (response.data.success) {
      ElMessage.success('考试提交成功')

      // 清除定时器
      if (timer) {
        clearInterval(timer)
      }

      // 显示考试结果
      const result = response.data.data
      await ElMessageBox.alert(
        `考试已提交成功！\n得分：${result.total_score}/${result.max_score}\n正确率：${result.accuracy_rate}%\n${result.is_passed ? '恭喜通过！' : '未达到及格线，请继续努力！'}`,
        '考试结果',
        {
          confirmButtonText: '确定',
          type: result.is_passed ? 'success' : 'warning',
        },
      )

      // 跳转回课程页面
      router.push('/my-courses')
    } else {
      ElMessage.error(response.data.message || '提交失败')
    }
  } catch (err) {
    console.error('提交考试失败:', err)
    ElMessage.error(err.response?.data?.message || '提交失败，请稍后重试')
  }
}

// 开始计时
const startTimer = () => {
  timer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      // 时间到，自动提交
      clearInterval(timer)
      ElMessageBox.alert('考试时间已到，系统将自动提交您的答案', '提示', {
        confirmButtonText: '确定',
        callback: () => {
          submitExam()
        },
      })
    }
  }, 1000)
}

// 组件挂载时
onMounted(() => {
  fetchExamData()

  // 添加离开页面提示
  window.addEventListener('beforeunload', (e) => {
    e.preventDefault()
    e.returnValue = '考试正在进行中，确定要离开吗？'
  })
})

// 组件卸载前
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 移除事件监听
  window.removeEventListener('beforeunload', () => {})
})
</script>

<style scoped>
.online-exam-page {
  padding: 0 80px 40px 80px;
  max-width: 1200px;
  margin: 0 auto;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.exam-title {
  font-size: 22px;
  margin: 0;
  color: var(--text-primary);
}

.exam-timer {
  font-size: 24px;
  font-weight: bold;
}

.timer {
  color: var(--danger-color);
}

.exam-container {
  display: flex;
  gap: 20px;
  min-height: 600px;
}

.question-nav {
  width: 300px;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.nav-title {
  color: #409eff;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.question-type {
  margin-bottom: 30px;
}

.question-type h4 {
  margin-bottom: 15px;
  color: #303133;
}

.question-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.question-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  transition: all 0.3s;
}

.question-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.question-btn.active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.question-btn.answered {
  background-color: #67c23a;
  color: white;
  border-color: #67c23a;
}

.question-btn.answered.active {
  background-color: #409eff;
  border-color: #409eff;
}

.question-content {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.question-container {
  flex: 1;
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.question-header h3 {
  margin: 0;
  margin-right: 10px;
  font-size: 18px;
}

.question-type-label {
  color: #909399;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 30px;
  color: #303133;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-item {
  padding: 15px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.option-item:hover {
  background-color: #f5f7fa;
}

.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.page-info {
  font-size: 16px;
  color: #909399;
}

.page-buttons {
  display: flex;
  gap: 15px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 20px;
}

.submit-dialog-content {
  text-align: center;
  padding: 20px 0;
}

.warning-text {
  color: #f56c6c;
  font-weight: bold;
  margin-top: 10px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: var(--text-secondary);
}
</style>
