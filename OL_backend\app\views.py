import os
import uuid
import datetime
import oss2
import hashlib
import time

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status
from rest_framework import generics, filters
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth.hashers import make_password, check_password
from django.utils import timezone
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.conf import settings
from django.http import Http404
from django.db.models import Q, Case, When, F, FloatField, Count, Avg
from django.db.models.functions import Coalesce


from .models import (
    User, Course, CourseCarousel, CourseCategory, CourseChapter, CourseMaterial, CourseComment, CourseFavorite, Notification,
    QuestionBank, Question, ExamPaper, PaperQuestion, Exam, StudentEnrollCourse, StudentInfo
)
from .serializers import (
    LoginSerializer, RegisterSerializer, UserSerializer,
    CourseCommentSerializer, CourseFavoriteSerializer, NotificationSerializer,
    CourseSerializer, CourseCreateSerializer, CourseChapterSerializer, CourseCarouselSerializer,  CourseCategorySerializer, CourseMaterialSerializer,
    QuestionBankSerializer, QuestionSerializer, ExamPaperSerializer, PaperQuestionSerializer, ExamSerializer, ExamPaperCreateSerializer, 
)

class LoginView(APIView):
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        username = serializer.validated_data['username']
        password = serializer.validated_data['password']
        
        try:
            user = User.objects.get(username=username)
            
            # 检查账号状态
            if user.status == 0:
                return Response(
                    {'message': '账号已被禁用'}, 
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 密码验证
            if not self.check_cloud_password(password, user.password):
                return Response(
                    {'message': '密码错误'}, 
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # 生成token（生产环境建议使用JWT）
            token = self.generate_token(username)
            
            return Response({
                'message': '登录成功',
                'username': user.username,
                'role': user.role,
                'status': user.status,
                'token': token,
            })
            
        except User.DoesNotExist:
            return Response(
                {'message': '用户不存在'}, 
                status=status.HTTP_404_NOT_FOUND
            )

    def check_cloud_password(self, input_password, stored_password):
        """
        密码验证方法，支持多种存储方式
        """
        # 情况1：如果云端是明文存储
        if input_password == stored_password:
            return True
            
        # 情况2：如果云端是Django默认的PBKDF2哈希
        try:
            if check_password(input_password, stored_password):
                return True
        except:
            pass
            
        # 情况3：如果云端是MD5加密
        input_md5 = hashlib.md5(input_password.encode()).hexdigest()
        if input_md5 == stored_password:
            return True
            
        return False

    def generate_token(self, username):
        """生成临时token（生产环境应使用JWT）"""
        return hashlib.md5(
            f"{username}:{uuid.uuid4()}:{time.time()}".encode()
        ).hexdigest()

class RegisterView(APIView):
    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            username = serializer.validated_data['username']
            password = serializer.validated_data['password']
            
            # 检查用户名是否已存在
            if User.objects.filter(username=username).exists():
                return Response({"message": "用户名已存在"}, status=400)

            
            # 密码处理（可选明文或MD5加密）
            password_stored = password  # 明文存储
            # password_stored = hashlib.md5(password.encode()).hexdigest()  # MD5加密存储
            
            # 创建用户
            user = User.objects.create(
                username=username,
                password=password_stored,
                role='student',          # 默认角色
                status=1,               # 假设1表示活跃用户
                create_time=timezone.now(),
                update_time=timezone.now(),
                last_login=timezone.now()
            )
            
            return Response({
                'message': '注册成功',
                'username': user.username,
                'role': user.role
            }, status=status.HTTP_201_CREATED)
        
        return Response({'message': list(serializer.errors.values())[0]}, status=status.HTTP_400_BAD_REQUEST)


# 教师管理学生账号模块
class StudentListView(APIView):
    """
    获取学生账号列表（支持分页和搜索）
    """
    def get(self, request):
        # 获取查询参数
        search = request.query_params.get('search', '').strip()
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 10))
        
        # 构建基础查询
        queryset = User.objects.filter(role='student')
        
        # 添加搜索条件
        if search:
            queryset = queryset.filter(Q(username__icontains=search))
        
        # 计算总数
        total = queryset.count()
        
        # 分页处理
        start = (page - 1) * size
        end = start + size
        students = queryset.order_by('username')[start:end]
        
        # 序列化数据
        serializer = UserSerializer(students, many=True)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data,
            'total': total,
            'page': page,
            'size': size
        })

class ResetPasswordView(APIView):
    """
    重置学生密码为用户名
    """
    def post(self, request, user_id):
        try:
            student = User.objects.get(id=user_id, role='student')
        except User.DoesNotExist:
            return Response({
                'code': 404,
                'message': '学生不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 重置密码为用户名
        student.password = student.username
        student.save()
        
        return Response({
            'code': 200,
            'message': '密码已重置为用户名',
            'data': {
                'id': student.id,
                'username': student.username
            }
        })

class ToggleAccountStatusView(APIView):
    """
    禁用/启用学生账号
    """
    def post(self, request, user_id):
        try:
            student = User.objects.get(id=user_id, role='student')
        except User.DoesNotExist:
            return Response({
                'code': 404,
                'message': '学生不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 切换状态 (1启用, 0禁用)
        student.status = 0 if student.status == 1 else 1
        student.save()
        
        return Response({
            'code': 200,
            'message': '账号状态已更新',
            'data': {
                'id': student.id,
                'username': student.username,
                'status': student.get_status_display()
            }
        })
    
class OssUploadView(APIView):
    @method_decorator(never_cache)
    def post(self, request, upload_type):
        try:
            if upload_type not in ['cover', 'carousel', 'material', 'attachment', 'avatar']:
                return Response({'error': 'Invalid upload type'}, status=400)
                
            file = request.FILES.get('file')
            if not file:
                return Response({'error': 'No file uploaded'}, status=400)
            
            # 添加bucket验证
            if not settings.OSS_BUCKET_NAME:
                return Response({'error': 'OSS bucket not configured'}, status=500)
                
            auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
            bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET_NAME)  # 注意参数顺序
            
            # 生成存储路径
            dir_prefix = settings.OSS_DIRS.get(upload_type, 'others/')
            date_path = datetime.datetime.now().strftime('%Y/%m/%d')
            file_ext = os.path.splitext(file.name)[1].lower()
            filename = f"{dir_prefix}{date_path}/{uuid.uuid4()}{file_ext}"
            
            try:
                result = bucket.put_object(filename, file)
                if result.status != 200:
                    return Response({'error': 'OSS upload failed'}, status=500)
                    
                file_url = f"{settings.OSS_IMAGE_URL}{filename}"
                
                # 如果是头像上传，直接更新用户头像
                if upload_type == 'avatar':
                    username = request.data.get('username')
                    if username:
                        try:
                            user = User.objects.get(username=username)
                            student_info, created = StudentInfo.objects.get_or_create(user=user)
                            student_info.avatar = file_url
                            student_info.save()
                        except User.DoesNotExist:
                            pass  # 用户不存在，忽略更新
                
                return Response({
                    'success': True,
                    'message': 'Upload successful',
                    'url': file_url,
                    'data': {
                        'url': file_url,
                        'filename': filename,
                        'size': file.size,
                        'type': file.content_type
                    }
                })
            except Exception as e:
                return Response({'error': f'OSS error: {str(e)}'}, status=500)
                
        except Exception as e:
            return Response({'error': str(e)}, status=500)
    
# 课程模块视图
class CourseDetailView(APIView):
    def get(self, request, course_id):
        """获取课程详情（含轮播图和章节）"""
        course = get_object_or_404(Course, id=course_id)
        carousels = CourseCarousel.objects.filter(course_id=course_id).order_by('sort_order')
        chapters = CourseChapter.objects.filter(course_id=course_id).order_by('sort_order')
        
        course_data = CourseSerializer(course).data
        course_data['carousels'] = CourseCarouselSerializer(carousels, many=True).data
        course_data['chapters'] = CourseChapterSerializer(chapters, many=True).data
        
        return Response({
            'success': True,
            'data': course_data
        })

    def put(self, request, course_id):
        """更新课程信息（完整更新）"""
        course = get_object_or_404(Course, id=course_id)
        
        try:
            # 1. 更新课程基础信息
            serializer = CourseSerializer(course, data=request.data, partial=False)
            serializer.is_valid(raise_exception=True)
            updated_course = serializer.save()
            
            # 2. 处理轮播图（先删除旧的，再创建新的）
            carousels_data = request.data.get('carousels', [])
            with transaction.atomic():
                CourseCarousel.objects.filter(course_id=course_id).delete()
                CourseCarousel.objects.bulk_create([
                    CourseCarousel(
                        course=updated_course,
                        image_url=item['image_url'],
                        sort_order=item.get('sort_order', index)
                    ) for index, item in enumerate(carousels_data)
                ])
            
            return Response({
                'success': True,
                'data': CourseSerializer(updated_course).data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'更新失败: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, course_id):
        """删除课程"""
        try:
            course = get_object_or_404(Course, id=course_id)
            
            # 使用事务确保数据一致性
            with transaction.atomic():
                # 先删除关联的轮播图（可选）
                CourseCarousel.objects.filter(course_id=course_id).delete()
                # 再删除课程
                course.delete()
                
                return Response({
                    'success': True,
                    'message': '课程删除成功'
                })
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'删除课程失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CourseCarouselView(APIView):
    @method_decorator(never_cache)  # 禁用缓存
    def delete(self, request, carousel_id):
        """删除轮播图"""
        carousel = get_object_or_404(CourseCarousel, id=carousel_id)
        carousel.delete()
        return Response({'success': True})

    def patch(self, request, carousel_id):
        """更新轮播图顺序"""
        carousel = get_object_or_404(CourseCarousel, id=carousel_id)
        new_order = request.data.get('sort_order')
        
        if not isinstance(new_order, int):
            return Response(
                {'success': False, 'message': '无效的排序值'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        carousel.sort_order = new_order
        carousel.save()
        return Response({'success': True})
    
class CourseCarouselListView(APIView):
    @method_decorator(never_cache)
    def get(self, request, course_id):
        """获取课程轮播图"""
        carousels = CourseCarousel.objects.filter(course_id=course_id).order_by('sort_order')
        serializer = CourseCarouselSerializer(carousels, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })

class CourseBatchCreateView(APIView):
    def post(self, request):
        """批量创建课程及关联数据"""
        try:
            with transaction.atomic():
                # 1. 创建课程
                course_data = request.data.get('course', {})
                course_serializer = CourseSerializer(data=course_data)
                course_serializer.is_valid(raise_exception=True)
                course = course_serializer.save()
                
                # 2. 创建章节和课件
                self._create_chapters(course, request.data.get('chapters', []))
                
                # 3. 创建轮播图
                self._create_carousels(course, request.data.get('carousels', []))
                
                return Response({
                    'success': True,
                    'data': CourseSerializer(course).data
                })
                
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def _create_chapters(self, course, chapters_data):
        for chapter_data in chapters_data:
            chapter = CourseChapter.objects.create(
                course=course,
                title=chapter_data['title'],
                description=chapter_data.get('description', ''),
                sort_order=chapter_data.get('sort_order', 0)
            )
            
            for material_data in chapter_data.get('materials', []):
                CourseMaterial.objects.create(
                    chapter=chapter,
                    title=material_data['title'],
                    type=material_data['type'],
                    url=material_data['url'],
                    size=material_data.get('size', 0),
                    duration=material_data.get('duration', 0),
                    sort_order=material_data.get('sort_order', 0)
                )
    
    def _create_carousels(self, course, carousels_data):
        CourseCarousel.objects.bulk_create([
            CourseCarousel(
                course=course,
                image_url=item['image_url'],
                sort_order=item.get('sort_order', index)
            ) for index, item in enumerate(carousels_data)
        ])

class CourseBatchUpdateView(APIView):
    def post(self, request, course_id):
        """批量更新课程及所有关联数据"""
        try:
            course = get_object_or_404(Course, id=course_id)
            
            with transaction.atomic():
                # 1. 更新课程基础信息
                course_serializer = CourseSerializer(
                    course, 
                    data=request.data.get('course', {}), 
                    partial=True
                )
                course_serializer.is_valid(raise_exception=True)
                updated_course = course_serializer.save()
                
                # 2. 重新创建所有章节和课件
                CourseChapter.objects.filter(course_id=course_id).delete()
                self._create_chapters(updated_course, request.data.get('chapters', []))
                
                # 3. 重新创建所有轮播图
                CourseCarousel.objects.filter(course_id=course_id).delete()
                self._create_carousels(updated_course, request.data.get('carousels', []))
                
                return Response({
                    'success': True,
                    'data': CourseSerializer(updated_course).data
                })
                
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    # 复用创建逻辑
    _create_chapters = CourseBatchCreateView._create_chapters
    _create_carousels = CourseBatchCreateView._create_carousels

class TeacherCourseView(APIView):
    @method_decorator(never_cache)
    def get(self, request):
        teacher_id = "2022150024"  # 静态教师ID
        status_filter = request.query_params.get('status')
        
        queryset = Course.objects.filter(teacher_id=teacher_id)
        if status_filter in ['draft', 'published']:
            queryset = queryset.filter(status=status_filter)
            
        serializer = CourseSerializer(queryset, many=True)
        return Response({'success': True, 'data': serializer.data})

class CourseStatusView(APIView):
    @method_decorator(never_cache)
    def patch(self, request, course_id):
        course = get_object_or_404(Course, id=course_id)
        new_status = request.data.get('status')
        
        if new_status not in ['draft', 'published']:
            return Response(
                {'success': False, 'message': '无效的状态值'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        course.status = new_status
        course.save()
        return Response({'success': True, 'data': CourseSerializer(course).data})

class CourseCategoryListView(APIView):
    @method_decorator(never_cache)
    def get(self, request):
        categories = CourseCategory.objects.filter(status=True)
        serializer = CourseCategorySerializer(categories, many=True)
        return Response({'success': True, 'data': serializer.data})

class CourseCreateView(APIView):
    def post(self, request):
        try:
            # 第一步：仅创建基础课程
            serializer = CourseCreateSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # 先保存基础课程（不含轮播图）
            course = Course.objects.create(
                teacher_id=request.data.get('teacher_id'),
                category_id =request.data.get('category'),
                title=request.data.get('title'),
                description=request.data.get('description'),
                cover_url=request.data.get('cover_url'),
                status='draft'
            )
            
            return Response({
                'success': True,
                'data': {
                    'id': course.id,
                    'message': '请继续添加章节内容'
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

class CourseChapterView(APIView):
    @method_decorator(never_cache)
    def get(self, request, course_id):
        """获取课程所有章节"""
        chapters = CourseChapter.objects.filter(course_id=course_id).order_by('sort_order')
        serializer = CourseChapterSerializer(chapters, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    def put(self, request, course_id):
        """批量更新章节 - 主要修改点"""
        try:
            if not isinstance(request.data, list):
                return Response(
                    {'success': False, 'message': '请求数据应为章节数组'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            course = get_object_or_404(Course, id=course_id)
            
            with transaction.atomic():
                # 先验证所有数据
                chapters = []
                for chapter_data in request.data:
                    chapter = CourseChapter.objects.get(
                        id=chapter_data['id'],
                        course_id=course_id
                    )
                    serializer = CourseChapterSerializer(
                        chapter, 
                        data=chapter_data, 
                        partial=True
                    )
                    serializer.is_valid(raise_exception=True)
                    chapters.append((chapter, serializer))
                
                # 全部验证通过后再保存
                for chapter, serializer in chapters:
                    serializer.save()
            
            return Response({'success': True})
            
        except CourseChapter.DoesNotExist:
            return Response(
                {'success': False, 'message': '章节不存在或不属于本课程'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'success': False, 'message': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class CourseMaterialView(APIView):
    """课程课件管理"""
    def get(self, request, chapter_id=None, material_id=None):
        """获取课件列表或单个课件详情"""
        if material_id:
            material = get_object_or_404(CourseMaterial, id=material_id)
            return Response({
                'success': True,
                'data': CourseMaterialSerializer(material).data
            })
        
        materials = CourseMaterial.objects.filter(chapter_id=chapter_id).order_by('sort_order')
        return Response({
            'success': True,
            'data': CourseMaterialSerializer(materials, many=True).data
        })
    
    def post(self, request, chapter_id):
        try:
            if not chapter_id or not str(chapter_id).isdigit():
                return Response(
                    {'success': False, 'message': '无效的章节ID'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 修改教师验证逻辑
            chapter = get_object_or_404(CourseChapter, id=int(chapter_id))
            
            serializer = CourseMaterialSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            material = serializer.save(chapter=chapter)
            
            return Response({
                'success': True,
                'data': CourseMaterialSerializer(material).data
            }, status=status.HTTP_201_CREATED)
        except Http404:
            return Response(
                {'success': False, 'message': '章节不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'success': False, 'message': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def put(self, request, material_id):
        """更新课件"""
        try:
            material = get_object_or_404(CourseMaterial, id=material_id)
            serializer = CourseMaterialSerializer(material, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            updated_material = serializer.save()
            
            return Response({
                'success': True,
                'data': CourseMaterialSerializer(updated_material).data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, material_id):
        """删除课件"""
        try:
            material = get_object_or_404(CourseMaterial, id=material_id)
            material.delete()
            
            return Response({
                'success': True,
                'message': '课件删除成功'
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

# 添加学生课程相关视图
class StudentCourseView(APIView):
    @method_decorator(never_cache)
    def get(self, request):
        """获取学生已选课程列表"""
        try:
            # 调试信息：打印请求参数
            print(f"请求参数: {request.query_params}")
            print(f"请求头: {request.headers.get('Authorization')}")
            
            # 从请求头中获取token并解析用户名
            auth_header = request.headers.get('Authorization')
            username = None
            
            if auth_header and auth_header.startswith('Bearer '):
                # 从请求数据中获取用户名（同时兼容username和student_id参数）
                username = request.query_params.get('username') or request.query_params.get('student_id')
                
                if not username:
                    return Response({'success': False, 'message': '缺少用户名或学生ID'}, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({'success': False, 'message': '未授权'}, status=status.HTTP_401_UNAUTHORIZED)
                
            # 获取用户
            try:
                user = User.objects.get(username=username)
                
                # 验证用户角色
                if user.role != 'student':
                    return Response({'success': False, 'message': '只有学生可以查看已选课程'}, status=status.HTTP_403_FORBIDDEN)
                    
            except User.DoesNotExist:
                # 调试模式：如果找不到用户且用户名是student1，则创建测试用户
                if username == 'student1':
                    user = User.objects.create(
                        username='student1',
                        password='password123',  # 实际中应该加密
                        role='student',
                        status=1
                    )
                else:
                    return Response({'success': False, 'message': f'找不到用户: {username}'}, status=status.HTTP_404_NOT_FOUND)
            
            # 查询该学生已选课程
            try:
                # 尝试查询学生已注册的课程
                enrolled_courses = StudentEnrollCourse.objects.filter(student=user)
                courses = [enroll.course for enroll in enrolled_courses]
            except:
                # 如果出错或没有注册课程，返回一些示例课程
                courses = Course.objects.filter(status='published')[:4]  # 临时：返回最多4门已发布课程
            
            # 构造响应数据，添加学习进度等信息
            response_data = []
            for i, course in enumerate(courses):
                # 模拟不同的进度数据
                progress_data = {
                    0: {'progress': 65, 'completedTasks': 13, 'totalTasks': 20, 'status': 'inProgress'},
                    1: {'progress': 30, 'completedTasks': 6, 'totalTasks': 20, 'status': 'inProgress'},
                    2: {'progress': 45, 'completedTasks': 9, 'totalTasks': 20, 'status': 'inProgress'},
                    3: {'progress': 100, 'completedTasks': 18, 'totalTasks': 18, 'status': 'completed'}
                }
                
                course_data = CourseSerializer(course).data
                # 添加学习进度信息
                course_data.update(progress_data.get(i, {'progress': 0, 'completedTasks': 0, 'totalTasks': 0, 'status': 'notStarted'}))
                # 添加教师名称
                course_data['teacher'] = f"{course.teacher_id}教授"  # 实际应从User表获取教师名称
                
                response_data.append(course_data)
            
            return Response({'success': True, 'data': response_data})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StudentCourseEnrollView(APIView):
    @method_decorator(never_cache)
    def post(self, request, course_id):
        """学生注册课程"""
        try:
            # 获取当前用户和课程
            # 从请求头中获取token并解析用户名
            auth_header = request.headers.get('Authorization')
            username = None
            
            if auth_header and auth_header.startswith('Bearer '):
                # 从token中解析出用户名，实际项目中应当验证token
                token = auth_header.split(' ')[1]
                # 这里简化处理，假设token中包含用户名
                try:
                    # 从请求数据中获取用户名（兼容username和student_id参数）
                    username = request.data.get('username') or request.data.get('student_id')
                    # 如果请求数据中没有用户名，则使用默认用户名(测试用)
                    if not username:
                        username = 'student1'  # 使用一个测试用户名
                    print(f"解析到用户名: {username}")
                except:
                    pass
            
            if not username:
                return Response({
                    'success': False,
                    'message': '无法识别用户身份'
                }, status=status.HTTP_401_UNAUTHORIZED)
                
            # 查找对应用户
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                # 调试模式：如果找不到用户且用户名是student1，则创建测试用户
                if username == 'student1':
                    user = User.objects.create(
                        username='student1',
                        password='password123',  # 实际中应该加密
                        role='student',
                        status=1
                    )
                else:
                    return Response({
                        'success': False, 
                        'message': f'找不到用户: {username}'
                    }, status=status.HTTP_404_NOT_FOUND)
                
            # 获取课程
            course = get_object_or_404(Course, id=course_id)
            
            # 验证用户角色
            if user.role != 'student':
                return Response({
                    'success': False,
                    'message': '只有学生可以注册课程'
                }, status=status.HTTP_403_FORBIDDEN)
            print(f"用户 {user.username} 正在注册课程 {course.title}")
            print(f"用户身份: {user.role}")
            
            # 检查是否已经注册
            already_enrolled = StudentEnrollCourse.objects.filter(
                student=user,
                course=course
            ).exists()
            
            if already_enrolled:
                return Response({
                    'success': False,
                    'message': '您已经注册了该课程'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建课程注册记录
            from django.utils import timezone
            enroll_record = StudentEnrollCourse.objects.create(
                student=user,
                course=course,
                enroll_time=timezone.now(),
                progress=0,
                status=1  # 1表示在学
            )
            
            # 更新课程注册人数
            course.enroll_count = course.enroll_count + 1
            course.save()
            
            return Response({
                'success': True,
                'message': '课程注册成功',
                'data': {
                    'enroll_id': enroll_record.id,
                    'enroll_time': enroll_record.enroll_time
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'课程注册失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# 添加学生课程学习视图
class StudentCourseStudyView(APIView):
    @method_decorator(never_cache)
    def get(self, request, course_id):
        """获取课程学习内容"""
        try:
            # 查询课程信息
            course = get_object_or_404(Course, id=course_id)
            course_data = CourseSerializer(course).data
            
            # 查询课程章节
            chapters = CourseChapter.objects.filter(course_id=course_id).order_by('sort_order')
            chapters_data = []
            
            for chapter in chapters:
                # 查询章节下的课件
                materials = CourseMaterial.objects.filter(chapter=chapter).order_by('sort_order')
                materials_data = CourseMaterialSerializer(materials, many=True).data
                
                # 构建章节数据
                chapter_data = {
                    'id': str(chapter.id),
                    'label': chapter.title,
                    'children': []
                }
                
                # 添加课件作为子节点
                for i, material in enumerate(materials_data):
                    lesson_data = {
                        'id': f"{chapter.id}-{i+1}",
                        'label': material['title'],
                        'type': 'lesson',
                        'completed': False,  # 默认未完成
                        'material_id': material['id'],
                        'material_type': material['type'],
                        'material_url': material['url']
                    }
                    chapter_data['children'].append(lesson_data)
                
                chapters_data.append(chapter_data)
            
            # 获取课程评论
            comments = CourseComment.objects.filter(
                course_id=course_id,
                parent__isnull=True
            ).order_by('-create_time')[:10]  # 只获取前10条评论
            
            comments_data = CourseCommentSerializer(comments, many=True).data
            
            # 构建响应数据
            response_data = {
                'course': course_data,
                'chapters': chapters_data,
                'comments': comments_data
            }
            
            return Response({'success': True, 'data': response_data})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# 添加课程评论视图
class CourseCommentView(APIView):
    @method_decorator(never_cache)
    def get(self, request, course_id):
        """获取课程评论列表"""
        try:
            # 获取课程的主评论（非回复）
            comments = CourseComment.objects.filter(
                course_id=course_id,
                parent__isnull=True
            ).order_by('-create_time')
            
            serializer = CourseCommentSerializer(comments, many=True)
            return Response({'success': True, 'data': serializer.data})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request, course_id):
        """添加课程评论"""
        try:
            content = request.data.get('content')
            parent_id = request.data.get('parent_id')
            user_id = request.data.get('user_id')  # 实际应该从认证中获取
            
            if not content:
                return Response({'success': False, 'message': '评论内容不能为空'}, status=status.HTTP_400_BAD_REQUEST)
                
            # 获取课程
            course = get_object_or_404(Course, id=course_id)
            
            # 获取用户
            user = get_object_or_404(User, username=user_id)
            
            # 处理回复
            parent = None
            if parent_id:
                parent = get_object_or_404(CourseComment, id=parent_id)
            
            # 创建评论
            comment = CourseComment.objects.create(
                course=course,
                user=user,
                content=content,
                parent=parent
            )
            
            # 如果是回复，使用回复序列化器
            if parent:
                from .serializers import CourseCommentReplySerializer
                serializer = CourseCommentReplySerializer(comment)
            else:
                serializer = CourseCommentSerializer(comment)
            
            return Response({'success': True, 'data': serializer.data}, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CommentLikeView(APIView):
    def post(self, request, comment_id):
        """点赞评论"""
        try:
            comment = get_object_or_404(CourseComment, id=comment_id)
            comment.likes += 1
            comment.save()
            
            return Response({
                'success': True, 
                'data': {'id': comment.id, 'likes': comment.likes}
            })
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# 添加课程收藏视图
class CourseFavoriteView(APIView):
    def post(self, request, course_id):
        """添加课程收藏"""
        try:
            # 从请求头中获取token并解析用户名
            auth_header = request.headers.get('Authorization')
            username = None
            
            if auth_header and auth_header.startswith('Bearer '):
                # 从请求数据中获取用户名（兼容username和student_id参数）
                username = request.data.get('username') or request.data.get('student_id') or request.query_params.get('username') or request.query_params.get('student_id')
                
                # 调试信息
                print(f"CourseFavoriteView POST 请求参数: {request.query_params}")
                print(f"CourseFavoriteView POST 请求数据: {request.data}")
                
                if not username:
                    # 使用默认测试用户（仅用于开发）
                    username = 'student1'
                    print(f"使用默认测试用户: {username}")
            else:
                return Response({'success': False, 'message': '未授权'}, status=status.HTTP_401_UNAUTHORIZED)
                
            # 获取课程和用户
            course = get_object_or_404(Course, id=course_id)
            
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                # 调试模式：如果找不到用户且用户名是student1，则创建测试用户
                if username == 'student1':
                    user = User.objects.create(
                        username='student1',
                        password='password123',  # 实际中应该加密
                        role='student',
                        status=1
                    )
                else:
                    return Response({'success': False, 'message': f'找不到用户: {username}'}, status=status.HTTP_404_NOT_FOUND)
            
            # 检查是否已经收藏
            favorite, created = CourseFavorite.objects.get_or_create(
                user=user,
                course=course
            )
            
            if not created:
                return Response({'success': True, 'message': '已经收藏过此课程', 'is_favorite': True})
            
            return Response({
                'success': True, 
                'message': '收藏成功',
                'is_favorite': True,
                'data': CourseFavoriteSerializer(favorite).data
            })
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, course_id):
        """取消课程收藏"""
        try:
            # 从请求头中获取token并解析用户名
            auth_header = request.headers.get('Authorization')
            username = None
            
            if auth_header and auth_header.startswith('Bearer '):
                # 从请求数据中获取用户名（兼容username和student_id参数）
                username = request.query_params.get('username') or request.query_params.get('student_id') or request.data.get('username') or request.data.get('student_id')
                
                # 调试信息
                print(f"CourseFavoriteView DELETE 请求参数: {request.query_params}")
                print(f"CourseFavoriteView DELETE 请求数据: {request.data}")
                
                if not username:
                    # 使用默认测试用户（仅用于开发）
                    username = 'student1'
                    print(f"使用默认测试用户: {username}")
            else:
                return Response({'success': False, 'message': '未授权'}, status=status.HTTP_401_UNAUTHORIZED)
                
            # 获取用户
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                # 调试模式：如果找不到用户且用户名是student1，则创建测试用户
                if username == 'student1':
                    user = User.objects.create(
                        username='student1',
                        password='password123',  # 实际中应该加密
                        role='student',
                        status=1
                    )
                else:
                    return Response({'success': False, 'message': f'找不到用户: {username}'}, status=status.HTTP_404_NOT_FOUND)
            
            # 查找并删除收藏
            try:
                favorite = CourseFavorite.objects.get(user=user, course_id=course_id)
                favorite.delete()
                return Response({'success': True, 'message': '取消收藏成功', 'is_favorite': False})
            except CourseFavorite.DoesNotExist:
                return Response({'success': True, 'message': '未收藏此课程', 'is_favorite': False})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def get(self, request, course_id=None):
        """查询课程收藏状态或用户所有收藏"""
        try:
            # 从请求头中获取token并解析用户名
            auth_header = request.headers.get('Authorization')
            username = None
            
            if auth_header and auth_header.startswith('Bearer '):
                # 从请求数据中获取用户名（兼容username和student_id参数）
                username = request.query_params.get('username') or request.query_params.get('student_id') or request.data.get('username') or request.data.get('student_id')
                
                # 调试信息
                print(f"CourseFavoriteView GET 请求参数: {request.query_params}")
                print(f"CourseFavoriteView GET 请求数据: {request.data}")
                
                if not username:
                    # 使用默认测试用户（仅用于开发）
                    username = 'student1'
                    print(f"使用默认测试用户: {username}")
            else:
                return Response({'success': False, 'message': '未授权'}, status=status.HTTP_401_UNAUTHORIZED)
                
            # 获取用户
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                # 调试模式：如果找不到用户且用户名是student1，则创建测试用户
                if username == 'student1':
                    user = User.objects.create(
                        username='student1',
                        password='password123',  # 实际中应该加密
                        role='student',
                        status=1
                    )
                else:
                    return Response({'success': False, 'message': f'找不到用户: {username}'}, status=status.HTTP_404_NOT_FOUND)
            
            if course_id:
                # 查询特定课程的收藏状态
                is_favorite = CourseFavorite.objects.filter(user=user, course_id=course_id).exists()
                return Response({'success': True, 'is_favorite': is_favorite})
            else:
                # 查询用户所有收藏
                favorites = CourseFavorite.objects.filter(user=user).order_by('-create_time')
                serializer = CourseFavoriteSerializer(favorites, many=True)
                return Response({'success': True, 'data': serializer.data})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# 添加课件下载视图
class CourseMaterialDownloadView(APIView):
    def get(self, request, material_id):
        """获取课件下载链接"""
        try:
            # 获取课件
            material = get_object_or_404(CourseMaterial, id=material_id)
            
            # 在实际应用中，可能需要检查用户权限，确保只有选课学生才能下载
            
            # 返回下载链接（这里直接返回原始URL，实际中可能需要生成临时下载链接）
            return Response({
                'success': True,
                'data': {
                    'download_url': material.url,
                    'filename': material.title,
                    'type': material.type
                }
            })
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """批量获取课件下载链接"""
        try:
            material_ids = request.data.get('material_ids', [])
            if not material_ids:
                return Response({'success': False, 'message': '请选择要下载的课件'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 查询所有课件
            materials = CourseMaterial.objects.filter(id__in=material_ids)
            
            # 构建下载链接列表
            download_links = []
            for material in materials:
                download_links.append({
                    'id': material.id,
                    'title': material.title,
                    'type': material.type,
                    'download_url': material.url
                })
            
            return Response({'success': True, 'data': download_links})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
# 获取所有课程信息
class CourseListView(generics.ListAPIView):
    queryset = Course.objects.all().order_by('-update_time')
    serializer_class = CourseSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['category_id', 'recommend_level']
    ordering_fields = ['create_time', 'update_time', 'enroll_count', 'favorite_count']
    
class MinimalCourseListView(APIView):
    """获取课程列表(用于教师板块)"""
    
    def get(self, request):
        courses = Course.objects.filter(status='published').values('id', 'title')
        return Response({
            'success': True,
            'data': list(courses)
        })
    
# 移除推荐课程
class RemoveRecommendLevelView(APIView):
    def put(self, request, course_id, format=None):
        try:
            course = Course.objects.get(id=course_id)
        except Course.DoesNotExist:
            return Response(
                {"error": "课程不存在"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 更新推荐等级为0
        course.recommend_level = 0
        course.save()
        
        # 使用序列化器返回更新后的课程数据
        serializer = CourseSerializer(course)
        return Response(serializer.data)
    
# 添加推荐课程
class AddRecommendLevelView(APIView):
    def put(self, request, course_id, format=None):
        try:
            course = Course.objects.get(id=course_id)
        except Course.DoesNotExist:
            return Response(
                {"error": "课程不存在"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 更新推荐等级为0
        course.recommend_level = 1
        course.save()
        
        # 使用序列化器返回更新后的课程数据
        serializer = CourseSerializer(course)
        return Response(serializer.data)


class CourseSearchView(APIView):
    """
    精简版课程搜索接口
    仅支持关键词搜索（标题和描述）
    """

    def get(self, request, format=None):
        keyword = request.query_params.get('keyword', '').strip()
        
        if not keyword:
            return Response(
                {"error": "请输入搜索关键词"},
                status=400
            )
        
        # 搜索标题或描述包含关键词的课程
        courses = Course.objects.filter(
            Q(title__icontains=keyword) | 
            Q(description__icontains=keyword)
        ).order_by('-update_time')[:20]  # 限制返回20条结果
        
        serializer = CourseSerializer(courses, many=True)
        return Response(serializer.data)
    
class NotificationListCreateAPIView(APIView):
    """获取通知列表 & 新增通知"""
    def get(self, request):
        notifications = Notification.objects.all()
        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = NotificationSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class NotificationRetrieveUpdateDestroyAPIView(APIView):
    """获取、编辑、删除单个通知"""
    def get(self, request, pk):
        try:
            notification = Notification.objects.get(pk=pk)
        except Notification.DoesNotExist:
            return Response({"error": "Notification not found"}, status=status.HTTP_404_NOT_FOUND)
        serializer = NotificationSerializer(notification)
        return Response(serializer.data)

    def put(self, request, pk):
        try:
            notification = Notification.objects.get(pk=pk)
        except Notification.DoesNotExist:
            return Response({"error": "Notification not found"}, status=status.HTTP_404_NOT_FOUND)
        request.data['publish_time'] = timezone.now()
        serializer = NotificationSerializer(notification, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            notification = Notification.objects.get(pk=pk)
        except Notification.DoesNotExist:
            return Response({"error": "Notification not found"}, status=status.HTTP_404_NOT_FOUND)
        notification.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

            

# ===============================================================
# 题考中心
# ===============================================================
class PublicQuestionBankView(APIView):
    """获取公开题库列表"""
    permission_classes = [AllowAny] # 允许匿名访问
    
    def get(self, request):
        # 分页设置
        paginator = PageNumberPagination()
        paginator.page_size = 10
        
        # 获取公开题库
        banks = QuestionBank.objects.filter(
            visibility=QuestionBank.PUBLIC
        ).order_by('-update_time')
        
        # 分页处理
        result_page = paginator.paginate_queryset(banks, request)
        serializer = QuestionBankSerializer(result_page, many=True)
        
        return paginator.get_paginated_response(serializer.data)

class QuestionBankDetailView(APIView):
    """题库详情接口"""
    permission_classes = [AllowAny]
    
    def get(self, request, bank_id):
        print(f"请求题库ID: {bank_id}")  # 调试日志
        try:
            bank = get_object_or_404(
                QuestionBank.objects.filter(
                    Q(visibility=QuestionBank.PUBLIC) | Q(teacher_id=request.user.username),
                    id=bank_id
                )
            )
            print(f"找到题库: {bank.name}")  # 调试日志
            serializer = QuestionBankSerializer(bank)
            return Response(serializer.data)
        except Exception as e:
            print(f"获取题库错误: {str(e)}")  # 调试日志
            raise
    
    
class QuestionBankQuestionsView(APIView):
    """获取题库中的题目列表"""
    permission_classes = [AllowAny]
    
    def get(self, request, bank_id):
        # 验证题库访问权限
        queryset = QuestionBank.objects.filter(id=bank_id)
        
        if request.user.is_authenticated:
            queryset = queryset.filter(
                Q(visibility=QuestionBank.PUBLIC) | 
                Q(teacher_id=request.user.username)
            )
        else:
            queryset = queryset.filter(visibility=QuestionBank.PUBLIC)
            
        bank = get_object_or_404(queryset)
        
        # 分页设置
        paginator = PageNumberPagination()
        paginator.page_size = request.query_params.get('page_size', 10)
        
        # 获取题目
        questions = Question.objects.filter(bank=bank).order_by('id')
        
        # 分页处理
        result_page = paginator.paginate_queryset(questions, request)
        serializer = QuestionSerializer(result_page, many=True)
        
        return paginator.get_paginated_response(serializer.data)
    
class MyQuestionBankView(APIView):
    """获取当前教师创建的题库列表"""

    def get(self, request):
        # 获取当前教师创建的题库
        teacher_id = request.query_params.get('teacher_id')
        print(f"当前请求用户: {teacher_id}")  # 添加调试信息
        
        banks = QuestionBank.objects.filter(teacher_id=teacher_id).order_by('-update_time')
        print(f"查询到的题库数量: {banks.count()}")  # 添加调试信息
        
        serializer = QuestionBankSerializer(banks, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })
        

class QuestionBankCreateView(APIView):
    """创建题库"""

    def get(self, request):
        """允许GET请求返回空表单结构"""
        return Response({
            'success': True,
            'data': {
                'name': '',
                'description': '',
                'visibility': 0,
                'courses': [],
                'tags': []
            }
        })

    def post(self, request):
        serializer = QuestionBankSerializer(data=request.data)
        if serializer.is_valid():
            # 确保题库属于当前用户
            bank = serializer.save(teacher_id=request.user.username)
            return Response({
                'success': True,
                'data': QuestionBankSerializer(bank).data,
                'id': bank.id
            }, status=status.HTTP_201_CREATED)
        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

class QuestionBankUpdateView(APIView):
    """更新题库"""
    permission_classes = [AllowAny]

    def put(self, request, bank_id):
        try:
            bank = QuestionBank.objects.get(id=bank_id, teacher_id=request.user.username)
            serializer = QuestionBankSerializer(bank, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    'success': True,
                    'data': serializer.data
                })
            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        except QuestionBank.DoesNotExist:
            return Response({
                'success': False,
                'message': '题库不存在或没有权限'
            }, status=status.HTTP_404_NOT_FOUND)

class QuestionBankDeleteView(APIView):
    """删除题库"""
    permission_classes = [AllowAny]

    def delete(self, request, bank_id):
        try:
            bank = QuestionBank.objects.get(id=bank_id, teacher_id=request.user.username)
            bank.delete()
            return Response({
                'success': True,
                'message': '题库删除成功'
            })
        except QuestionBank.DoesNotExist:
            return Response({
                'success': False,
                'message': '题库不存在或没有权限'
            }, status=status.HTTP_404_NOT_FOUND)

class QuestionBankStatsView(APIView):
    """获取题库统计数据"""
    def get(self, request, bank_id):
        try:
            bank = get_object_or_404(QuestionBank, id=bank_id)
            
            # 1. 计算题目数量
            question_count = Question.objects.filter(bank=bank).count()
            
            # 2. 计算题库使用次数（被多少试卷引用）
            use_count = PaperQuestion.objects.filter(
                question__bank_id=bank_id
            ).values('paper').distinct().count()
            
            # 3. 计算平均难度
            difficulty_stats = Question.objects.filter(
                bank=bank
            ).aggregate(
                avg_difficulty=Avg('difficulty')
            )
            
            # 4. 计算题型分布
            type_distribution = Question.objects.filter(
                bank=bank
            ).values('type').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # 转换为更友好的格式
            type_dist = {}
            for item in type_distribution:
                type_name = dict(Question.QUESTION_TYPE_CHOICES).get(item['type'], item['type'])
                type_dist[type_name] = item['count']
            
            # 组合统计数据
            stats = {
                'question_count': question_count,
                'use_count': use_count,
                'average_difficulty': round(float(difficulty_stats['avg_difficulty'] or 0), 1),
                'type_distribution': type_dist,
                'last_updated': bank.update_time.strftime('%Y-%m-%d %H:%M')
            }
            
            return Response({
                'success': True,
                'data': stats
            })
            
        except QuestionBank.DoesNotExist:
            return Response({
                'success': False,
                'message': '题库不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        
# 试卷、考试--------------------------------------------------------
class StandardPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class ExamPaperListView(APIView):
    pagination_class = StandardPagination
    
    def get(self, request):
        # 获取查询参数
        teacher_id = request.query_params.get('teacher_id')  # 现在这是username
        search = request.query_params.get('search')
        show_all = request.query_params.get('show_all', 'false').lower() == 'true'
        
        # 基础查询
        queryset = ExamPaper.objects.filter(status='published')
        
        # 如果不显示全部，则过滤当前教师的试卷
        if not show_all and teacher_id:
            queryset = queryset.filter(teacher_id=teacher_id)  # 直接使用username过滤
        
        # 搜索功能
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) | 
                Q(description__icontains=search)
            )
            
        # 分页处理
        paginator = self.pagination_class()
        result_page = paginator.paginate_queryset(queryset, request)
        serializer = ExamPaperSerializer(result_page, many=True)
        
        return paginator.get_paginated_response(serializer.data)

class ExamPaperDetailView(APIView):
    
    def get_object(self, pk):
        return get_object_or_404(ExamPaper, pk=pk, teacher=self.request.user)
        
    def get(self, request, pk):
        paper = self.get_object(pk)
        serializer = ExamPaperSerializer(paper)
        return Response(serializer.data)
        
    def put(self, request, pk):
        paper = self.get_object(pk)
        data = request.data.copy()
        data['teacher'] = request.user.id
        
        serializer = ExamPaperCreateSerializer(paper, data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
    def delete(self, request, pk):
        paper = self.get_object(pk)
        paper.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

class ExamPaperPublishView(APIView):
    
    def post(self, request, pk):
        paper = get_object_or_404(ExamPaper, pk=pk, teacher=request.user)
        
        if paper.status == 'published':
            return Response({'detail': '试卷已发布'}, status=status.HTTP_400_BAD_REQUEST)
            
        if paper.paper_questions.count() == 0:
            return Response({'detail': '请先添加题目到试卷'}, status=status.HTTP_400_BAD_REQUEST)
            
        paper.status = 'published'
        paper.save()
        
        return Response({'status': 'published'})

class ExamPaperUnpublishView(APIView):
    
    def post(self, request, pk):
        paper = get_object_or_404(ExamPaper, pk=pk, teacher=request.user)
        
        if paper.status == 'draft':
            return Response({'detail': '试卷已是草稿状态'}, status=status.HTTP_400_BAD_REQUEST)
            
        paper.status = 'draft'
        paper.save()
        
        return Response({'status': 'draft'})

class RandomQuestionView(APIView):
    
    def get(self, request):
        count = min(int(request.query_params.get('count', 10)), 50)
        exclude = request.query_params.get('exclude', '').split(',')
        exclude = [x for x in exclude if x]
        
        queryset = Question.objects.filter(
            bank__teacher_id=request.user.username
        ).exclude(id__in=exclude)
        
        # 可以添加更多筛选条件，如题型、难度等
        type_filter = request.query_params.get('type')
        if type_filter:
            queryset = queryset.filter(type=type_filter)
            
        questions = list(queryset.order_by('?')[:count])
        serializer = QuestionSerializer(questions, many=True)
        
        return Response(serializer.data)

class ExamListView(APIView):
    """考试列表视图（使用username作为教师标识）"""
    pagination_class = StandardPagination
    
    def get(self, request):
        # 从查询参数获取教师username
        teacher_username = request.query_params.get('teacher_id')  # 参数名保持teacher_id但实际是username
        if not teacher_username:
            return Response(
                {'success': False, 'message': '缺少teacher_id参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证教师用户存在（可选，根据需求决定是否保留）
        try:
            teacher = User.objects.get(username=teacher_username, role='teacher')
        except User.DoesNotExist:
            return Response(
                {'success': False, 'message': '教师用户不存在或没有权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 基础查询
        queryset = Exam.objects.filter(teacher_id=teacher_username)
        
        # 状态筛选
        status_filter = request.query_params.get('status')
        if status_filter:
            status_map = {
                'ongoing': Exam.ONGOING,
                'upcoming': Exam.NOT_STARTED,
                'completed': Exam.ENDED
            }
            if status_filter in status_map:
                queryset = queryset.filter(status=status_map[status_filter])
            
        # 课程筛选
        course_id = request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
            
        # 搜索
        search = request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) | 
                Q(description__icontains=search)
            )
            
        # 排序
        sort = request.query_params.get('sort', '-create_time')
        if sort.lstrip('-') in ['title', 'start_time', 'end_time', 'create_time']:
            queryset = queryset.order_by(sort)
            
        # 分页
        paginator = self.pagination_class()
        result_page = paginator.paginate_queryset(queryset, request)
        serializer = ExamSerializer(result_page, many=True)
        
        return paginator.get_paginated_response(serializer.data)
        
    def post(self, request):
        """创建考试"""
        # 从查询参数获取教师username
        teacher_username = request.query_params.get('teacher_id')
        if not teacher_username:
            return Response(
                {'success': False, 'message': '缺少teacher_id参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证教师用户存在
        try:
            teacher = User.objects.get(username=teacher_username, role='teacher')
        except User.DoesNotExist:
            return Response(
                {'success': False, 'message': '教师用户不存在或没有权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 复制请求数据并设置teacher_id
        data = request.data.copy()
        data['teacher'] = teacher_username  # 使用username而不是ID
        
        # 验证课程属于该教师
        course_id = data.get('course')
        if course_id and not Course.objects.filter(
            id=course_id, 
            teacher_id=teacher_username
        ).exists():
            return Response(
                {'success': False, 'message': '课程不属于当前教师'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证试卷属于该教师
        paper_id = data.get('paper')
        if paper_id and not ExamPaper.objects.filter(
            id=paper_id, 
            teacher_id=teacher_username
        ).exists():
            return Response(
                {'success': False, 'message': '试卷不属于当前教师'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = ExamSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            exam = serializer.save()
            return Response(
                {
                    'success': True,
                    'data': ExamSerializer(exam).data
                }, 
                status=status.HTTP_201_CREATED
            )
        return Response(
            {
                'success': False,
                'message': serializer.errors
            },
            status=status.HTTP_400_BAD_REQUEST
        )

class ExamDetailView(APIView):
    """考试详情视图（使用query_params获取用户信息）"""
    
    def get_teacher(self, teacher_id):
        """现在直接使用username查询"""
        try:
            return User.objects.get(username=teacher_id, role='teacher')
        except User.DoesNotExist:
            return None
        
    def get_object(self, pk, teacher_id):
        """获取考试对象并验证权限"""
        return get_object_or_404(Exam, pk=pk, teacher_id=teacher_id)  # 直接使用username
    
        
    def get(self, request, pk):
        teacher_id = request.query_params.get('teacher_id')
        if not teacher_id:
            return Response(
                {'success': False, 'message': '缺少teacher_id参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        exam = self.get_object(pk, teacher_id)
        if not exam:
            return Response(
                {'success': False, 'message': '考试不存在或没有权限'},
                status=status.HTTP_404_NOT_FOUND
            )
            
        serializer = ExamSerializer(exam)
        return Response(serializer.data)
        
    def put(self, request, pk):
        teacher_id = request.query_params.get('teacher_id')
        if not teacher_id:
            return Response(
                {'success': False, 'message': '缺少teacher_id参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        exam = self.get_object(pk, teacher_id)
        if not exam:
            return Response(
                {'success': False, 'message': '考试不存在或没有权限'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        data = request.data.copy()
        data['teacher'] = exam.teacher.id  # 保持原有教师不变
        
        serializer = ExamSerializer(exam, data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
    def delete(self, request, pk):
        teacher_id = request.query_params.get('teacher_id')
        if not teacher_id:
            return Response(
                {'success': False, 'message': '缺少teacher_id参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        exam = self.get_object(pk, teacher_id)
        if not exam:
            return Response(
                {'success': False, 'message': '考试不存在或没有权限'},
                status=status.HTTP_404_NOT_FOUND
            )
            
        exam.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class TeacherListView(APIView):
    """获取教师列表"""
    def get(self, request):
        try:
            teachers = User.objects.filter(role='teacher').values('username', 'role')
            return Response({
                'success': True,
                'data': list(teachers)
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# 学生考试相关视图
class StudentExamListView(APIView):
    """学生获取课程考试列表"""
    def get(self, request):
        try:
            course_id = request.query_params.get('course_id')
            if not course_id:
                return Response({
                    'success': False,
                    'message': '缺少course_id参数'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证课程是否存在
            try:
                course = Course.objects.get(id=course_id)
            except Course.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '课程不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 获取该课程的所有考试
            exams = Exam.objects.filter(course_id=course_id).order_by('-create_time')

            # 构建响应数据
            exam_list = []
            for exam in exams:
                exam_data = {
                    'id': exam.id,
                    'title': exam.title,
                    'description': exam.description,
                    'start_time': exam.start_time,
                    'end_time': exam.end_time,
                    'duration_minutes': exam.duration_minutes,
                    'total_score': exam.total_score,
                    'pass_score': exam.pass_score,
                    'status': exam.status,
                    'paper_title': exam.paper.title if exam.paper else '',
                    'course_title': exam.course.title,
                    'teacher_name': exam.teacher_id,
                    'can_take': exam.status == Exam.ONGOING,  # 只有进行中的考试可以参加
                }
                exam_list.append(exam_data)

            return Response({
                'success': True,
                'data': exam_list
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StudentExamDetailView(APIView):
    """学生获取考试详情和题目"""
    def get(self, request, exam_id):
        try:
            # 获取考试信息
            try:
                exam = Exam.objects.select_related('paper', 'course').get(id=exam_id)
            except Exam.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '考试不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 检查考试状态
            if exam.status != Exam.ONGOING:
                return Response({
                    'success': False,
                    'message': '考试未开始或已结束'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取试卷题目
            paper_questions = PaperQuestion.objects.filter(
                paper=exam.paper
            ).select_related('question').order_by('order_num')

            # 构建题目数据
            questions = []
            for pq in paper_questions:
                question = pq.question
                question_data = {
                    'id': question.id,
                    'paper_question_id': pq.id,
                    'type': question.type,
                    'content': question.content,
                    'options': question.options if question.options else [],
                    'score': pq.score,
                    'order_num': pq.order_num
                }
                questions.append(question_data)

            # 构建考试数据
            exam_data = {
                'id': exam.id,
                'title': exam.title,
                'description': exam.description,
                'duration_minutes': exam.duration_minutes,
                'total_score': exam.total_score,
                'pass_score': exam.pass_score,
                'start_time': exam.start_time,
                'end_time': exam.end_time,
                'course_title': exam.course.title,
                'paper_title': exam.paper.title,
                'questions': questions,
                'shuffle_questions': exam.shuffle_questions,
                'shuffle_options': exam.shuffle_options,
                'allow_break': exam.allow_break,
            }

            return Response({
                'success': True,
                'data': exam_data
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StudentExamSubmitView(APIView):
    """学生提交考试答案"""
    def post(self, request, exam_id):
        try:
            # 获取考试信息
            try:
                exam = Exam.objects.select_related('paper', 'course').get(id=exam_id)
            except Exam.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '考试不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 检查考试状态
            if exam.status != Exam.ONGOING:
                return Response({
                    'success': False,
                    'message': '考试未开始或已结束'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取提交的答案
            answers = request.data.get('answers', {})
            student_id = request.data.get('student_id', 'student1')  # 临时使用默认学生ID

            if not answers:
                return Response({
                    'success': False,
                    'message': '答案不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取试卷题目
            paper_questions = PaperQuestion.objects.filter(
                paper=exam.paper
            ).select_related('question').order_by('order_num')

            # 计算得分
            total_score = 0
            correct_count = 0
            question_results = []

            for pq in paper_questions:
                question = pq.question
                question_id = str(question.id)
                user_answer = answers.get(question_id, '')

                # 判断答案是否正确
                is_correct = False
                if question.type in ['single_choice', 'judgment']:
                    # 单选题和判断题
                    is_correct = str(user_answer).upper() == str(question.answer).upper()
                elif question.type == 'multiple_choice':
                    # 多选题 - 简化处理，这里需要根据实际数据结构调整
                    if isinstance(user_answer, dict):
                        selected_options = [k for k, v in user_answer.items() if v]
                        correct_options = question.answer if isinstance(question.answer, list) else [question.answer]
                        is_correct = set(selected_options) == set(correct_options)
                    else:
                        is_correct = False

                if is_correct:
                    total_score += pq.score
                    correct_count += 1

                question_results.append({
                    'question_id': question.id,
                    'user_answer': user_answer,
                    'correct_answer': question.answer,
                    'is_correct': is_correct,
                    'score': pq.score if is_correct else 0,
                    'max_score': pq.score
                })

            # 计算通过状态
            is_passed = total_score >= exam.pass_score

            # 构建结果数据
            result_data = {
                'exam_id': exam.id,
                'exam_title': exam.title,
                'student_id': student_id,
                'total_score': total_score,
                'max_score': exam.total_score,
                'pass_score': exam.pass_score,
                'is_passed': is_passed,
                'correct_count': correct_count,
                'total_questions': len(paper_questions),
                'accuracy_rate': round(correct_count / len(paper_questions) * 100, 2) if paper_questions else 0,
                'submit_time': timezone.now(),
                'question_results': question_results
            }

            # 这里可以保存考试结果到数据库（需要创建ExamResult模型）
            # 暂时只返回结果

            return Response({
                'success': True,
                'message': '考试提交成功',
                'data': result_data
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ClassListView(APIView):
    """获取班级列表"""
    def get(self, request):
        # 这里需要根据你的实际班级模型进行调整
        # 暂时返回空数组
        return Response({
            'success': True,
            'data': []
        })

class UserProfileView(APIView):
    """获取和更新用户个人信息"""
    permission_classes = [AllowAny]  # 开发阶段允许所有访问，生产环境应该限制

    def get(self, request):
        """获取用户个人信息"""
        try:
            # 从请求参数中获取用户名
            username = request.query_params.get('username')
            if not username:
                return Response({'success': False, 'message': '请提供用户名'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 查找用户
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                return Response({'success': False, 'message': '用户不存在'}, status=status.HTTP_404_NOT_FOUND)
            
            # 尝试获取用户的个人信息
            try:
                student_info = StudentInfo.objects.get(user=user)
                profile_data = {
                    'username': user.username,
                    'role': user.role,
                    'nickname': student_info.nickname or username,
                    'studentId': username,  # 假设学号就是用户名
                    'signature': student_info.signature or '这个人很懒，什么都没留下',
                    'avatar': student_info.avatar or '',
                }
            except StudentInfo.DoesNotExist:
                # 如果没有个人信息，使用默认值
                profile_data = {
                    'username': user.username,
                    'role': user.role,
                    'nickname': username,  # 默认使用用户名作为昵称
                    'studentId': username,  # 假设学号就是用户名
                    'signature': '这个人很懒，什么都没留下',  # 默认签名
                    'avatar': '',
                }
            
            # 获取待完成考试数量（模拟数据）
            profile_data['pendingExams'] = 2
            
            return Response({'success': True, 'data': profile_data})
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request):
        """更新用户个人信息"""
        try:
            # 从请求数据中获取用户名和要更新的信息
            username = request.data.get('username')
            if not username:
                return Response({'success': False, 'message': '请提供用户名'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 查找用户
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                return Response({'success': False, 'message': '用户不存在'}, status=status.HTTP_404_NOT_FOUND)
            
            # 获取要更新的字段
            nickname = request.data.get('nickname')
            signature = request.data.get('signature')
            avatar = request.data.get('avatar')
            
            # 获取或创建用户的个人信息
            student_info, created = StudentInfo.objects.get_or_create(user=user)
            
            # 更新信息
            if nickname is not None:
                student_info.nickname = nickname
            if signature is not None:
                student_info.signature = signature
            if avatar is not None:
                student_info.avatar = avatar
                
            # 保存更新
            student_info.save()
            
            return Response({
                'success': True, 
                'message': '个人信息更新成功',
                'data': {
                    'nickname': student_info.nickname,
                    'signature': student_info.signature,
                    'avatar': student_info.avatar
                }
            })
            
        except Exception as e:
            return Response({'success': False, 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
# class StudentCourseEnrollView(APIView):
#     """学生课程注册视图"""
#     permission_classes = [AllowAny]  # 允许所有用户访问
    
#     @method_decorator(never_cache)
#     def post(self, request, course_id):
#         try:
#             # 从请求体中获取用户名
#             username = request.data.get('username')
#             if not username:
#                 return Response({"message": "请提供用户名"}, status=status.HTTP_400_BAD_REQUEST)
            
#             # 查找用户
#             try:
#                 user = User.objects.get(username=username)
#             except User.DoesNotExist:
#                 return Response({"message": "用户不存在"}, status=status.HTTP_404_NOT_FOUND)
            
#             # 检查课程是否存在
#             try:
#                 course = Course.objects.get(id=course_id)
#             except Course.DoesNotExist:
#                 return Response({"message": "课程不存在"}, status=status.HTTP_404_NOT_FOUND)
            
#             # 检查是否已经注册
#             if StudentEnrollCourse.objects.filter(student=user, course=course).exists():
#                 return Response({"message": "您已经注册了该课程"}, status=status.HTTP_400_BAD_REQUEST)
            
#             # 创建注册记录
#             enrollment = StudentEnrollCourse(
#                 student=user,
#                 course=course,
#                 status=1  # 在学状态
#             )
#             enrollment.save()
            
#             # 更新课程注册人数
#             course.enroll_count += 1
#             course.save()
            
#             return Response({"message": "课程注册成功"}, status=status.HTTP_201_CREATED)
            
#         except Exception as e:
#             return Response({"message": f"注册失败: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
