# Generated by Django 5.2.3 on 2025-06-15 15:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentInfo',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='student_info', serialize=False, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
                ('nickname', models.CharField(blank=True, max_length=50, null=True, verbose_name='昵称')),
                ('signature', models.CharField(blank=True, max_length=200, null=True, verbose_name='个人签名')),
                ('avatar', models.CharField(blank=True, max_length=200, null=True, verbose_name='头像地址')),
                ('gender', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=10, null=True, verbose_name='性别')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '学生信息',
                'verbose_name_plural': '学生信息',
            },
        ),
    ]
