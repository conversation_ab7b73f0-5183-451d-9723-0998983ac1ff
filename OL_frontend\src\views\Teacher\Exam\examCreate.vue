<template>
  <div class="exam-create">
    <h2 class="page-title">新建考试</h2>
    <el-form
      ref="formRef"
      :model="form"
      label-width="100px"
      style="max-width: 600px; margin: 0 auto; margin-top: 30px;"
    >
      <el-form-item label="考试名称">
        <el-input v-model="form.name" placeholder="请输入考试名称" />
      </el-form-item>

      <el-form-item label="考试时间">
        <el-date-picker
          v-model="form.timeRange"
          type="datetime"
          placeholder="选择考试开始时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
        />
      </el-form-item>

      <el-form-item label="考试时长">
        <el-input-number
          v-model="form.duration"
          :min="30"
          :max="240"
          unit="分钟"
          placeholder="请输入考试时长"
        />
      </el-form-item>

      <el-form-item label="关联试卷">
        <el-select
          v-model="form.paperId"
          placeholder="请选择关联试卷"
          style="width: 300px;"
        >
          <el-option
            v-for="paper in papers"
            :key="paper.id"
            :label="paper.name"
            :value="paper.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="submitExam"
          style="width: 100%;"
        >
          发布考试
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 模拟试卷数据（可从API获取）
const papers = ref([
  { id: 1, name: '前端入门试卷' },
  { id: 2, name: 'Java基础试卷' }
])

const form = ref({
  name: '',
  timeRange: '',
  duration: 90,
  paperId: null
})

const submitExam = () => {
  // 实际需调用后端接口
  console.log('考试信息:', form.value)
}
</script>

<style scoped>
.page-title {
  text-align: center;
  font-size: 22px;
  font-weight: 600;
  margin: 40px 0;
}
</style>