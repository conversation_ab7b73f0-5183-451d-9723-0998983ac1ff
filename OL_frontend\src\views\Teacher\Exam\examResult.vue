<template>
  <div class="exam-result">
    <div class="page-header">
      <el-button type="text" @click="goBack">
        <el-icon>
          <ArrowLeft />
        </el-icon> 返回
      </el-button>
      <h2 class="page-title">{{ exam.name }} - 考试结果</h2>
    </div>

    <el-card class="exam-summary">
      <div class="summary-row">
        <div class="summary-item">
          <div class="item-title">平均分</div>
          <div class="item-value">{{ statistics.averageScore ? statistics.averageScore.toFixed(1) : '暂无数据' }}</div>
        </div>
        <div class="summary-item">
          <div class="item-title">最高分</div>
          <div class="item-value">{{ statistics.maxScore || '暂无数据' }}</div>
        </div>
        <div class="summary-item">
          <div class="item-title">最低分</div>
          <div class="item-value">{{ statistics.minScore || '暂无数据' }}</div>
        </div>
        <div class="summary-item">
          <div class="item-title">及格率</div>
          <div class="item-value">{{ statistics.passRate ? (statistics.passRate * 100).toFixed(1) + '%' : '暂无数据' }}
          </div>
        </div>
        <div class="summary-item">
          <div class="item-title">参与人数</div>
          <div class="item-value">{{ exam.studentCount || '暂无数据' }}/{{ exam.totalStudents || '暂无数据' }}</div>
        </div>
      </div>
    </el-card>

    <el-card class="score-distribution">
      <h3 class="card-title">分数分布</h3>
      <div class="distribution-chart">
        <canvas ref="chartRef" class="chart-placeholder" width="800" height="300"></canvas>
      </div>
    </el-card>

    <el-card class="question-analysis">
      <h3 class="card-title">题目正确率分析</h3>
      <el-table :data="questionAnalysis" stripe>
        <el-table-column prop="questionIndex" label="题号"></el-table-column>
        <el-table-column prop="questionTitle" label="题目内容" min-width="300px"></el-table-column>
        <el-table-column prop="correctRate" label="正确率">
          <template #default="scope">
            <el-progress :percentage="scope.row.correctRate * 100"
              :color="getProgressColor(scope.row.correctRate)"></el-progress>
            <span class="rate-text">{{ scope.row.correctRate ? (scope.row.correctRate * 100).toFixed(1) + '%' : '暂无数据'
              }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="student-list">
      <h3 class="card-title">学生成绩列表</h3>
      <el-table :data="studentScores" stripe>
        <el-table-column prop="studentId" label="学号"></el-table-column>
        <el-table-column prop="studentName" label="姓名"></el-table-column>
        <el-table-column prop="score" label="得分">
          <template #default="scope">
            <span :class="{ 'high-score': scope.row.score >= 85, 'low-score': scope.row.score < 60 }">
              {{ scope.row.score || '暂无数据' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间"></el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewStudentAnswer(scope.row.studentId)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale } from 'chart.js'

// 注册图表组件（按需引入，避免全量加载）
ChartJS.register(BarElement, CategoryScale, LinearScale)

const router = useRouter()
const route = useRoute()

// 响应式数据
const exam = ref({})
const statistics = ref({})
const questionAnalysis = ref([])
const studentScores = ref([])
const chartRef = ref(null) // 引用 canvas 元素
let chartInstance = null // 图表实例

onMounted(() => {
  // 模拟接口返回数据（实际需替换为真实 API 请求）
  const mockData = {
    exam: {
      id: route.params.id || 'E-2024001',
      name: '2024年度前端期中考试',
      studentCount: 35,
      totalStudents: 40
    },
    statistics: {
      averageScore: 78.5,
      maxScore: 96,
      minScore: 52,
      passRate: 0.886
    },
    questionAnalysis: [
      { questionIndex: 1, questionTitle: 'Vue3 组合式API', correctRate: 0.85 },
      { questionIndex: 2, questionTitle: 'JS 基本数据类型', correctRate: 0.72 },
      { questionIndex: 3, questionTitle: 'React 虚拟DOM原理', correctRate: 0.63 },
      { questionIndex: 4, questionTitle: 'Vue3 响应式原理', correctRate: 0.92 }
    ],
    studentScores: [
      { studentId: 'S-2024001', studentName: '张三', score: 88, submitTime: '2024-06-01 10:55' },
      { studentId: 'S-2024002', studentName: '李四', score: 96, submitTime: '2024-06-01 10:30' },
      { studentId: 'S-2024003', studentName: '王五', score: 75, submitTime: '2024-06-01 10:58' }
    ]
  }

  // 赋值响应式数据
  exam.value = mockData.exam
  statistics.value = mockData.statistics
  questionAnalysis.value = mockData.questionAnalysis
  studentScores.value = mockData.studentScores

  // 初始化图表
  initChart()
})

const initChart = () => {
  if (!chartRef.value) return // 确保 DOM 已渲染

  const ctx = chartRef.value.getContext('2d')
  if (chartInstance) chartInstance.destroy() // 销毁旧图表

  chartInstance = new ChartJS(ctx, {
    type: 'bar', // 图表类型（柱状图）
    data: {
      labels: ['0-59', '60-69', '70-79', '80-89', '90-100'], // x 轴标签
      datasets: [
        {
          label: '学生人数', // 数据集标签
          data: [2, 5, 12, 11, 5], // 数据值
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(255, 205, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(54, 162, 235, 0.7)'
          ],
          borderColor: [
            'rgb(255, 99, 132)',
            'rgb(255, 159, 64)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(54, 162, 235)'
          ],
          borderWidth: 1 // 边框宽度
        }
      ]
    },
    options: {
      responsive: true, // 响应式适配
      maintainAspectRatio: false, // 禁止保持宽高比（避免图表拉伸变形）
      scales: {
        y: {
          beginAtZero: true, // y 轴从 0 开始
          title: {
            display: true,
            text: '学生数量'
          }
        },
        x: {
          title: {
            display: true,
            text: '分数段'
          }
        }
      },
      plugins: {
        legend: {
          display: true, // 显示图例
          position: 'top' // 图例位置
        }
      }
    }
  })
}

// 组件卸载时销毁图表，避免内存泄漏
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取进度条颜色
const getProgressColor = (rate) => {
  if (rate < 0.6) return '#f56c6c' // 红色（不及格）
  if (rate < 0.8) return '#e6a23c' // 橙色（中等）
  return '#67c23a' // 绿色（优秀）
}

// 查看学生答案详情
const viewStudentAnswer = (studentId) => {
  router.push({
    name: 'studentAnswer',
    params: { examId: exam.value.id, studentId }
  })
}
</script>

<style scoped>
.exam-result {
  padding: 24px;
  background-color: #fff;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.summary-row {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
  margin: 24px 0;
}

.summary-item {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.item-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.item-value {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.score-distribution,
.question-analysis,
.student-list {
  margin-top: 24px;
}

.chart-placeholder {
  width: 100%;
  height: 400px;
}

.el-progress {
  width: 100%;
  margin-right: 8px;
}

.rate-text {
  display: inline-block;
  width: 60px;
  text-align: right;
}

.high-score {
  color: #42b983;
  font-weight: 500;
}

.low-score {
  color: #f56c6c;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .summary-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-placeholder {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .summary-row {
    grid-template-columns: 1fr;
  }
}
</style>