<template>
  <div class="exam-list-page">
    <div class="page-header">
      <h1 class="page-title">课程考试</h1>
      <div class="header-actions">
        <el-select v-model="selectedCourse" placeholder="选择课程" @change="fetchExams">
          <el-option
            v-for="course in myCourses"
            :key="course.id"
            :label="course.title"
            :value="course.id"
          />
        </el-select>
        <el-button @click="refreshExams" :icon="Refresh">刷新</el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 错误提示 -->
    <el-alert v-else-if="error" :title="error" type="error" :closable="false" show-icon />

    <!-- 空数据提示 -->
    <div v-else-if="exams.length === 0" class="no-exams-container">
      <el-empty description="该课程暂无考试">
        <template #image>
          <el-icon size="100" color="#909399">
            <Document />
          </el-icon>
        </template>
        <template #description>
          <div class="empty-description">
            <h3>暂无考试安排</h3>
            <p v-if="selectedCourse">{{ getCurrentCourseName() }} 课程暂时没有安排考试</p>
            <p v-else>请先选择一个课程查看考试安排</p>
            <p class="tip-text">如有疑问，请联系任课老师</p>
          </div>
        </template>
        <div class="empty-actions">
          <el-button @click="refreshExams" :icon="Refresh">刷新</el-button>
          <el-button type="primary" @click="goBackToCourses">返回课程</el-button>
        </div>
      </el-empty>
    </div>

    <!-- 考试列表 -->
    <div v-else class="exams-container">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-for="exam in exams" :key="exam.id">
          <el-card class="exam-card" :class="getExamCardClass(exam.status)">
            <template #header>
              <div class="exam-header">
                <h3 class="exam-title">{{ exam.title }}</h3>
                <el-tag :type="getStatusType(exam.status)">{{ getStatusText(exam.status) }}</el-tag>
              </div>
            </template>

            <div class="exam-info">
              <div class="info-item">
                <el-icon><Clock /></el-icon>
                <span>考试时长: {{ exam.duration_minutes }} 分钟</span>
              </div>
              <div class="info-item">
                <el-icon><Trophy /></el-icon>
                <span>总分: {{ exam.total_score }} 分</span>
              </div>
              <div class="info-item">
                <el-icon><Star /></el-icon>
                <span>及格分: {{ exam.pass_score }} 分</span>
              </div>
              <div class="info-item">
                <el-icon><Calendar /></el-icon>
                <span>开始时间: {{ formatDateTime(exam.start_time) }}</span>
              </div>
              <div class="info-item">
                <el-icon><Calendar /></el-icon>
                <span>结束时间: {{ formatDateTime(exam.end_time) }}</span>
              </div>
            </div>

            <div class="exam-description" v-if="exam.description">
              <p>{{ exam.description }}</p>
            </div>

            <template #footer>
              <div class="exam-actions">
                <el-button
                  v-if="exam.can_take"
                  type="primary"
                  @click="startExam(exam)"
                  :loading="startingExam === exam.id"
                >
                  开始考试
                </el-button>
                <el-button v-else-if="exam.status === 1" type="info" disabled> 未开始 </el-button>
                <el-button v-else-if="exam.status === 3" type="success" @click="viewResult(exam)">
                  查看结果
                </el-button>
                <el-button type="default" @click="viewDetails(exam)">详情</el-button>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 考试详情对话框 -->
    <el-dialog v-model="showDetailsDialog" :title="currentExam?.title || '考试详情'" width="600px">
      <div v-if="currentExam" class="exam-details">
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="考试名称">{{ currentExam.title }}</el-descriptions-item>
            <el-descriptions-item label="所属课程">{{
              currentExam.course_title
            }}</el-descriptions-item>
            <el-descriptions-item label="试卷名称">{{
              currentExam.paper_title
            }}</el-descriptions-item>
            <el-descriptions-item label="任课教师">{{
              currentExam.teacher_name
            }}</el-descriptions-item>
            <el-descriptions-item label="考试时长"
              >{{ currentExam.duration_minutes }} 分钟</el-descriptions-item
            >
            <el-descriptions-item label="总分"
              >{{ currentExam.total_score }} 分</el-descriptions-item
            >
            <el-descriptions-item label="及格分"
              >{{ currentExam.pass_score }} 分</el-descriptions-item
            >
            <el-descriptions-item label="考试状态">
              <el-tag :type="getStatusType(currentExam.status)">{{
                getStatusText(currentExam.status)
              }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section" v-if="currentExam.description">
          <h4>考试说明</h4>
          <p>{{ currentExam.description }}</p>
        </div>

        <div class="detail-section">
          <h4>时间安排</h4>
          <el-timeline>
            <el-timeline-item timestamp="开始时间" placement="top">
              {{ formatDateTime(currentExam.start_time) }}
            </el-timeline-item>
            <el-timeline-item timestamp="结束时间" placement="top">
              {{ formatDateTime(currentExam.end_time) }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, Trophy, Star, Calendar, Refresh, Document } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import axios from 'axios'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const selectedCourse = ref('')
const myCourses = ref([])
const exams = ref([])
const loading = ref(false)
const error = ref(null)
const startingExam = ref(null)
const showDetailsDialog = ref(false)
const currentExam = ref(null)

// 获取我的课程列表
const fetchMyCourses = async () => {
  try {
    const response = await axios.get('/api/student/courses/', {
      params: { student_id: userStore.username },
      headers: { Authorization: `Bearer ${userStore.token}` },
    })

    if (response.data.success) {
      myCourses.value = response.data.data

      // 检查URL参数中是否有指定的课程ID
      const courseIdFromUrl = route.query.course_id
      if (courseIdFromUrl && myCourses.value.some((course) => course.id == courseIdFromUrl)) {
        selectedCourse.value = courseIdFromUrl
        await fetchExams()
      } else if (myCourses.value.length > 0) {
        // 默认选择第一个课程
        selectedCourse.value = myCourses.value[0].id
        await fetchExams()
      }
    }
  } catch (err) {
    console.error('获取课程列表失败:', err)
    ElMessage.error('获取课程列表失败')
  }
}

// 获取考试列表
const fetchExams = async () => {
  if (!selectedCourse.value) return

  loading.value = true
  error.value = null

  try {
    const response = await axios.get('/api/student/exams/', {
      params: { course_id: selectedCourse.value },
      headers: { Authorization: `Bearer ${userStore.token}` },
    })

    if (response.data.success) {
      exams.value = response.data.data
    } else {
      error.value = response.data.message || '获取考试列表失败'
    }
  } catch (err) {
    console.error('获取考试列表失败:', err)
    error.value = err.response?.data?.message || '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 刷新考试列表
const refreshExams = () => {
  fetchExams()
}

// 开始考试
const startExam = async (exam) => {
  try {
    await ElMessageBox.confirm(
      `确定要开始考试"${exam.title}"吗？考试开始后将开始计时。`,
      '开始考试',
      {
        confirmButtonText: '开始',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    startingExam.value = exam.id

    // 跳转到考试页面
    router.push({
      path: '/exam',
      query: { exam_id: exam.id },
    })
  } catch {
    // 用户取消
  } finally {
    startingExam.value = null
  }
}

// 查看考试详情
const viewDetails = (exam) => {
  currentExam.value = exam
  showDetailsDialog.value = true
}

// 查看考试结果
const viewResult = (exam) => {
  ElMessage.info('查看考试结果功能待开发')
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    1: '未开始',
    2: '进行中',
    3: '已结束',
  }
  return statusMap[status] || '未知'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    1: 'info',
    2: 'success',
    3: 'warning',
  }
  return typeMap[status] || 'info'
}

// 获取考试卡片样式
const getExamCardClass = (status) => {
  return {
    'exam-ongoing': status === 2,
    'exam-ended': status === 3,
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取当前选中课程的名称
const getCurrentCourseName = () => {
  const course = myCourses.value.find((c) => c.id == selectedCourse.value)
  return course ? course.title : '未知课程'
}

// 返回课程页面
const goBackToCourses = () => {
  router.push('/my-courses')
}

// 页面加载时获取数据
onMounted(() => {
  fetchMyCourses()

  // 如果URL中有course_id参数，自动选择该课程
  const courseIdFromUrl = route.query.course_id
  if (courseIdFromUrl) {
    selectedCourse.value = courseIdFromUrl
  }
})
</script>

<style scoped>
.exam-list-page {
  padding: 0 40px 40px 40px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.exam-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.exam-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.exam-ongoing {
  border-left: 4px solid #67c23a;
}

.exam-ended {
  border-left: 4px solid #909399;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-title {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.exam-info {
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-regular);
}

.info-item .el-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.exam-description {
  margin-bottom: 15px;
  padding: 10px;
  background-color: var(--bg-hover);
  border-radius: 4px;
  font-size: 14px;
  color: var(--text-regular);
}

.exam-actions {
  display: flex;
  justify-content: space-between;
}

.exam-details .detail-section {
  margin-bottom: 20px;
}

.exam-details h4 {
  margin-bottom: 10px;
  color: var(--text-primary);
}

.no-exams-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.empty-description h3 {
  margin: 10px 0;
  font-size: 18px;
  color: var(--text-primary);
}

.empty-description p {
  margin: 8px 0;
  color: var(--text-regular);
  font-size: 14px;
}

.tip-text {
  color: var(--text-secondary);
  font-size: 12px !important;
}

.empty-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}
</style>
