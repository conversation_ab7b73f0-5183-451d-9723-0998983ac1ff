<template>
    <div class="course-create">
        <el-card>
            <template #header>
                <div class="card-header">
                    <h1>创建新课程</h1>
                    <el-button @click="router.back()">返回</el-button>
                </div>
            </template>

            <CourseForm ref="courseForm" :teacher-id="currentTeacherId" @submit="handleSubmit"
                @cancel="router.back()" />
        </el-card>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import CourseForm from '@/views/Teacher/components/courseForm.vue'

const router = useRouter()
const currentTeacherId = ref('2022150024')

const handleSubmit = () => {
    router.push('/course-manage')
}
</script>

<style scoped>
.course-create {
    padding: 0 60px 40px 60px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>