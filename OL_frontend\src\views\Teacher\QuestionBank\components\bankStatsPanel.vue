<template>
  <div class="stats-panel">
    <el-row :gutter="20">
      <!-- 题目总数 -->
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-item">
            <div class="stat-value">{{ stats.question_count || 0 }}</div>
            <div class="stat-label">题目总数</div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 使用次数 -->
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-item">
            <div class="stat-value">{{ stats.use_count || 0 }}</div>
            <div class="stat-label">被引用次数</div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 平均难度 -->
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-item">
            <div class="stat-value">{{ stats.average_difficulty || '-' }}</div>
            <div class="stat-label">平均难度(1-5)</div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最后更新 -->
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-item">
            <div class="stat-value">{{ stats.last_updated || '未知' }}</div>
            <div class="stat-label">最后更新时间</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 题型分布 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card shadow="hover">
          <div slot="header">题型分布</div>
          <div v-if="stats.type_distribution">
            <el-table :data="formatTypeDistribution" style="width: 100%">
              <el-table-column prop="type" label="题型"></el-table-column>
              <el-table-column prop="count" label="数量"></el-table-column>
              <el-table-column prop="percentage" label="占比"></el-table-column>
            </el-table>
          </div>
          <div v-else>暂无题型分布数据</div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps(['stats'])

// 格式化题型分布数据
const formatTypeDistribution = computed(() => {
  if (!props.stats?.type_distribution || !props.stats?.question_count) return []
  
  return Object.entries(props.stats.type_distribution).map(([type, count]) => ({
    type,
    count,
    percentage: ((count / props.stats.question_count) * 100).toFixed(1) + '%'
  }))
})
</script>

<style scoped>
.stat-item {
  text-align: center;
  padding: 20px 0;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}
.stat-label {
  color: #888;
  font-size: 14px;
}
.mt-20 {
  margin-top: 20px;
}
</style>