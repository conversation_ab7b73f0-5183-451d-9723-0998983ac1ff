import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
// 导入UserRole类型
import type { UserRole } from '@/stores/user'

// 课程管理
import CourseManagement from '@/views/Teacher/Course/courseManagement.vue'
import CourseEdit from '@/views/Teacher/Course/courseEdit.vue'
import CourseCreate from '@/views/Teacher/Course/courseCreate.vue'

// 题考管理中心
import TiKaoCenter from '@/views/Teacher/TiKaoCenter.vue'
import questionBankList from '@/views/Teacher/components/QuestionBankList.vue'

// 子页面
import QuestionBankList from '@/views/Teacher/components/QuestionBankList.vue';
import MyQuestionBank from '@/views/Teacher/components/MyQuestionBank.vue';
import MyPaper from '@/views/Teacher/Paper/components/myPaper.vue'
import MyExam from '@/views/Teacher/Exam/components/myExam.vue';

// 题库
import questionBankDetail from '@/views/Teacher/QuestionBank/QuestionBankDetail.vue'
import questionBankEdit from '@/views/Teacher/QuestionBank/questionBankEdit.vue'


// 考试
import examEdit from '@/views/Teacher/Exam/examEdit.vue'
import examCreate from '../views/Teacher/Exam/examCreate.vue'
import examResult from '@/views/Teacher/Exam/examResult.vue'

import loginVue from '@/views/Share/Login/login.vue'
import registerVue from '@/views/Share/Login/register.vue'
import courseDetailVue from '@/views/Share/courseDetail.vue'

import PersonalCenter from '../views/Share/PersonalCenter.vue'
import Home from '../views/Share/Home.vue'
import MyCourses from '../views/Student/MyCourses.vue'
import TodoList from '../views/Student/TodoList.vue'
import CourseRegistration from '../views/Student/CourseRegistration.vue'
import OnlineExam from '../views/Student/OnlineExam.vue'
import OnlineStudy from '../views/Student/OnlineStudy.vue'
import SearchResult from '../views/Share/searchResult.vue'
import manageVue from '@/views/Admin/manage.vue'
import PersonalCenterTeacherVue from '@/views/Share/PersonalCenterTeacher.vue'

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            name: '首页',
            component: Home,
            // meta: {
            //     // 所有用户都可以访问
            //     accessibleRoles: ['student', 'teacher', 'admin', 'visitor']
            // }
        },
        {
            path: '/login',
            name: '登录',
            component: loginVue,
            meta: {
                hideInMenu: true, // 在菜单中隐藏此路由
                hideLayout: true, // 在布局中隐藏此路由
                hideFooter: true, // 在页脚中隐藏此路由
                // 仅未登录用户可访问
                accessibleRoles: ['guest']
            }
        },
        {
            path: '/register',
            name: '注册',
            component: registerVue,
            meta: {
                hideInMenu: true, // 在菜单中隐藏此路由
                hideLayout: true, // 在布局中隐藏此路由
                hideFooter: true, // 在页脚中隐藏此路由
                accessibleRoles: ['guest']
            }
        },
        {
            path: '/course/detail',
            name: '课程详情',
            component: courseDetailVue,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            },
            props: (route) => ({ id: route.query.id }) // 将query参数转为props
        },
        {
            path: '/search',
            name: '搜索',
            component: SearchResult,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/course-manage',
            name: '课程管理',
            component: CourseManagement,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['teacher']
            }
        },
        {
            path: '/course-edit/:courseId',
            name: '课程编辑',
            component: CourseEdit,
            props: true,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        // 教师专属路由
        {
            path: '/course-create',
            name: '课程创建',
            component: CourseCreate,
            meta: {
                title: '创建新课程',
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/tikao',
            name: '题考中心',
            component: TiKaoCenter,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['teacher']
            }
        },
        {
            path: '/question-bank-list', // path: '/bank/public',
            name: 'routeNameFor1',
            component: QuestionBankList,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/my-question-bank',
            name: 'routeNameFor2',
            component: MyQuestionBank,
            meta: {
                hideInMenu: true
            }
        },
        {
            path: '/my-paper',
            name: 'routeNameFor3',
            component: MyPaper,
            meta: {
                hideInMenu: true
            }
        },
        {
            path: '/my-exam',
            name: 'routeNameFor4',
            component: MyExam,
            meta: {
                hideInMenu: true
            }
        },
        {
            path: '/exam-edit',
            name: '考试编辑',
            component: examEdit,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['teacher']
            }
        },
        {
            path: '/questionbank-edit/:id?', // 支持可选参数
            name: '题库编辑',
            component: questionBankEdit,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/bank/detail/:id',
            name: '题库详情',
            component: questionBankDetail,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/exam-create',
            name: '新建考试',
            component: examCreate,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/exam-result',
            name: '考试结果',
            component: examResult,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/personal',
            name: '个人中心',
            component: PersonalCenter,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/courses',
            name: '课程注册',
            component: CourseRegistration,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/study',
            name: '在线学习',
            component: OnlineStudy,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/exam',
            name: '在线考试',
            component: OnlineExam,
            meta: {
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        // 学生专属路由
        {
            path: '/my-courses',
            name: '我的课程',
            component: MyCourses,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['student']
            }
        },
        {
            path: '/todo',
            name: '待办事项',
            component: TodoList,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['student']
            }
        },
        {
            path: '/manage',
            name: '管理系统',
            component: manageVue,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['admin'],
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },
        {
            path: '/teacherCenter',
            name: '教师个人中心',
            component: PersonalCenterTeacherVue,
            meta: {
                requiresAuth: true,
                accessibleRoles: ['teacher'],
                hideInMenu: true // 在菜单中隐藏此路由
            }
        },

    ],
})

// 路由守卫 - 权限控制
router.beforeEach((to, from, next) => {
    const userStore = useUserStore() // 获取用户状态

    // 确保从localStorage恢复状态
    const storedToken = localStorage.getItem('token')
    const storedRole = localStorage.getItem('role')
    const storedUsername = localStorage.getItem('username')

    // 如果本地存储有token但状态中没有，重新初始化状态
    if (storedToken && !userStore.token) {
        console.log('路由守卫: 从本地存储恢复用户状态')
        if (storedRole) userStore.$patch({ role: storedRole as UserRole })
        if (storedUsername) userStore.$patch({ username: storedUsername })
        userStore.$patch({ token: storedToken, isLoggedIn: true })
    }

    const currentRole = userStore.isLoggedIn ? userStore.role : 'guest'

    // 检查路由是否需要特定权限
    if (to.meta.accessibleRoles) {
        const allowedRoles = to.meta.accessibleRoles as string[]
        if (!allowedRoles.includes(currentRole)) {
            // 游客尝试访问需要登录的页面
            if (currentRole === 'guest' && to.meta.requiresAuth) {
                showLoginPrompt()
                return next(false) // 取消导航
            }

            // 已登录但角色权限不足
            alert('您没有访问此页面的权限')
            return next('/') // 重定向到首页
        }
    }

    next()
})

// 显示登录提示
function showLoginPrompt() {
    // 这里可以使用您喜欢的UI库弹窗，如Element Plus/Ant Design
    if (confirm('请先登录才能访问此功能，是否立即登录？')) {
        router.push('/login')
    }
}

export default router
