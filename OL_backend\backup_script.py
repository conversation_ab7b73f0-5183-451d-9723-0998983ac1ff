import os
import sys
import django
from django.core.management import call_command
from io import open

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OL_backend.settings')  # 替换为您的实际settings路径
django.setup()

def run():
    with open('backup.json', 'w', encoding='utf-8') as f:
        # 保存原始stdout
        original_stdout = sys.stdout
        try:
            sys.stdout = f  # 重定向标准输出
            call_command(
                'dumpdata',
                exclude=[
                    'contenttypes',
                    'auth.permission',
                    'sessions.session'
                ],
                indent=2,
                format='json'
            )
        finally:
            sys.stdout = original_stdout  # 恢复标准输出

if __name__ == '__main__':
    run()