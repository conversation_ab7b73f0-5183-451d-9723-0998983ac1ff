<template>
    <div class="auth-container">
        <el-card class="auth-card">
            <h2>用户注册</h2>
            <el-form :model="form" :rules="rules" ref="registerForm" @submit.prevent="handleRegister">
                <el-form-item prop="username">
                    <el-input v-model="form.username" placeholder="请输入8位数字账号" prefix-icon="User" />
                </el-form-item>

                <el-form-item prop="password">
                    <el-input v-model="form.password" type="password" placeholder="请输入密码（8位以上英文数字组合）" prefix-icon="Lock"
                        show-password />
                </el-form-item>

                <el-form-item prop="confirmPassword">
                    <el-input v-model="form.confirmPassword" type="password" placeholder="请再次输入密码" prefix-icon="Lock"
                        show-password />
                </el-form-item>

                <el-form-item prop="agreed">
                    <el-checkbox v-model="form.agreed">
                        我已阅读并同意<el-link type="primary">《隐私条款》</el-link>
                    </el-checkbox>
                </el-form-item>

                <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading">
                    注册
                </el-button>
            </el-form>

            <div class="auth-footer">
                <span>已有账号？</span>
                <el-link type="primary" @click="gotoLogin">立即登录</el-link>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()

const form = ref({
    username: '',
    password: '',
    confirmPassword: '',
    agreed: false
})

const validateUsername = (rule, value, callback) => {
    if (!/^\d{10}$/.test(value)) {
        callback(new Error('账号必须为10位数字'))
    } else {
        callback()
    }
}

const validatePassword = (rule, value, callback) => {
    if (!/(?=.*[a-zA-Z])(?=.*\d).{8,}/.test(value)) {
        callback(new Error('密码需8位以上英文和数字组合'))
    } else {
        callback()
    }
}

const validateConfirmPassword = (rule, value, callback) => {
    if (value !== form.value.password) {
        callback(new Error('两次输入密码不一致'))
    } else {
        callback()
    }
}

const validateAgreement = (rule, value, callback) => {
    if (!value) {
        callback(new Error('请阅读并同意隐私条款'))
    } else {
        callback()
    }
}

const rules = {
    username: [
        { required: true, message: '请输入账号', trigger: 'blur' },
        { validator: validateUsername, trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { validator: validatePassword, trigger: 'blur' }
    ],
    confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        { validator: validateConfirmPassword, trigger: 'blur' }
    ],
    agreed: [
        { validator: validateAgreement, trigger: 'change' }
    ]
}

const registerForm = ref(null)
const loading = ref(false)

const handleRegister = () => {
    registerForm.value.validate(async valid => {
        if (!valid) {
            if (!form.value.agreed) {
                ElMessage.warning('请阅读并同意隐私条款')
            }
            return
        }

        try {
            loading.value = true
            // 发送注册请求
            const response = await axios.post(
                'http://localhost:8000/api/register/',
                {
                    username: form.value.username,
                    password: form.value.password,
                },
                {
                    headers: { 'Content-Type': 'application/json' },
                }
            );

            // 注册成功处理
            if (response.data.message === '注册成功') {
                ElMessage.success('注册成功！即将跳转到登录页');
                setTimeout(() => {
                    router.push('/login'); // 跳转到登录页
                }, 1500);
            }
        } catch (error) {
            // 错误处理
            console.error('注册失败 error.response:', error.response)  // <-- debug用
            if (error.response) {
                const { data } = error.response;

                // 如果是 serializer.errors 格式
                let errorMessage = '';
                if (typeof data === 'object') {
                    const firstKey = Object.keys(data)[0];
                    errorMessage = data[firstKey] || '注册失败';
                } else {
                    errorMessage = data.detail || data.message || '注册失败';
                }
                // 处理特定错误信息
                if (errorMessage.includes('已存在')) {
                    ElMessage.error('用户名已被占用，请更换');
                } else {
                    ElMessage.error(errorMessage);
                }
            } else {
                ElMessage.error('网络错误，请检查连接');
            }
        } finally {
            loading.value = false;
        }
    })
}

const gotoLogin = () => {
    router.push('/login')
}
</script>

<style scoped>
/* 复用登录页的样式 */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f7fa;
}

.auth-card {
    width: 400px;
    padding: 30px;
}

h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.submit-btn {
    width: 100%;
    margin-top: 10px;
}

.auth-footer {
    margin-top: 20px;
    text-align: center;
    color: #666;
}

:deep(.el-checkbox) {
    display: flex;
    align-items: center;
}

:deep(.el-checkbox__label) {
    display: flex;
    align-items: center;
}
</style>