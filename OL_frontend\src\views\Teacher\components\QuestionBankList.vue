<template>
  <div class="question-bank-list">
    <div class="page-header">
      <h2 class="page-title">查阅题库</h2>
      <div class="search-box">
        <el-input v-model="filterText" placeholder="搜索题库名称/出题老师" 
          prefix-icon="el-icon-search" size="default" clearable
          @keyup.enter="handleSearch" @clear="handleSearch" />
        <el-button type="primary" size="medium" @click="handleSearch">
          搜索
        </el-button>
      </div>
    </div>

    <el-table :data="tableData" stripe border v-loading="loading"
      style="width: 100%; margin-top: 20px;">
      <el-table-column prop="id" label="ID" width="80" align="center" />
      <el-table-column prop="name" label="题库名称" min-width="200" />
      <el-table-column label="出题老师" width="120">
        <template #default="{row}">
          {{ getTeacherName(row.teacher_info) }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160">
        <template #default="{row}">
          {{ formatDate(row.create_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="question_count" label="题数" width="80" align="center" />
      <el-table-column prop="use_count" label="引用次数" width="100" align="center" />
      <el-table-column label="操作" width="120" align="center">
        <template #default="scope">
          <el-button type="primary" size="small" 
            @click="goToDetail(scope.row.id)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.current"
      :page-sizes="[10, 20, 50]"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      style="margin-top: 20px;"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'

const router = useRouter()
const filterText = ref('')
const loading = ref(false)
const tableData = ref([])
const allData = ref([]) // 新增：存储所有数据用于前端筛选

// 分页参数
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取老师姓名
const getTeacherName = (teacherInfo) => {
  // 这里假设后端返回的teacher_info中有name字段
  // 如果没有，你需要根据实际情况调整
  return teacherInfo?.name || teacherInfo?.username || '未知'
}

// 获取题库列表
const fetchBanks = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.value.current,
      page_size: pagination.value.size,
      search: filterText.value
    }
    const res = await axios.get('/question-banks/public/', { params })
    allData.value = res.data.results // 保存所有数据
    tableData.value = filterData(res.data.results) // 初始时显示全部数据
    pagination.value.total = res.data.count
  } catch (error) {
    console.error('获取题库失败:', error)
    ElMessage.error('获取题库列表失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 前端筛选数据
const filterData = (data) => {
  if (!filterText.value) return data
  
  const searchText = filterText.value.toLowerCase()
  return data.filter(item => 
    item.name.toLowerCase().includes(searchText) ||
    (item.teacher_info?.username?.toLowerCase().includes(searchText)) ||
    (item.teacher_info?.name?.toLowerCase().includes(searchText)))
}

// 搜索功能
const handleSearch = () => {
  // 如果是前端筛选
  tableData.value = filterData(allData.value)
  
  // 如果是后端筛选（取消下面这行的注释，并注释掉上面那行）
  // fetchBanks()
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.value.size = size
  fetchBanks()
}

const handleCurrentChange = (current) => {
  pagination.value.current = current
  fetchBanks()
}

// 跳转详情页
const goToDetail = (id) => {
  router.push({ name: '题库详情', params: { id } })
}

onMounted(() => {
  fetchBanks()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.search-box {
  display: flex;
  gap: 10px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}
</style>