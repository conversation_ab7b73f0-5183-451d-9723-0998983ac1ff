// stores/user.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

export type UserRole = 'student' | 'teacher' | 'admin' | 'guest'

interface UserInfo {
    username: string
    role: UserRole
    // 可以根据后端返回的其他字段扩展
    avatar?: string
    email?: string
}

export const useUserStore = defineStore('user', () => {
    const router = useRouter()

    // 状态
    const token = ref<string | null>(null)
    const role = ref<UserRole>('guest')
    const username = ref<string>('')
    const isLoggedIn = ref<boolean>(false)

    // 初始化从本地存储加载
    const initFromStorage = () => {
        const storedToken = localStorage.getItem('token')
        const storedRole = localStorage.getItem('role') as UserRole | null
        const storedUsername = localStorage.getItem('username')

        if (storedToken) token.value = storedToken
        if (storedRole) role.value = storedRole
        if (storedUsername) username.value = storedUsername
        isLoggedIn.value = !!token.value

        // 添加调试信息
        console.log('从本地存储恢复用户状态:', {
            token: token.value ? '存在' : '不存在',
            role: role.value,
            username: username.value,
            isLoggedIn: isLoggedIn.value,
        })
    }

    // 立即初始化
    initFromStorage()

    // 登录方法（与您的login.vue完美配合）
    const login = async (credentials: { username: string; password: string; role?: UserRole }) => {
        try {
            const response = await axios.post(
                'http://localhost:8000/api/login/',
                {
                    username: credentials.username,
                    password: credentials.password,
                    role: credentials.role || 'student', // 默认角色为学生
                },
                {
                    headers: { 'Content-Type': 'application/json' },
                },
            )

            // 假设后端返回：{ role: string, username: string, token: string }
            const { role: userRole, username: responseUsername, token: responseToken } = response.data

            // 更新状态
            role.value = userRole
            username.value = responseUsername
            token.value = responseToken || null
            isLoggedIn.value = true

            // 存储到本地
            if (responseToken) localStorage.setItem('token', responseToken)
            localStorage.setItem('role', userRole)
            localStorage.setItem('username', responseUsername)

            console.log('登录成功，保存状态:', {
                token: token.value ? '存在' : '不存在',
                role: role.value,
                username: username.value,
                isLoggedIn: isLoggedIn.value,
            })

            ElMessage.success(`登录成功，身份：${userRole}`)

            // 统一跳转到首页（您可以根据需要修改）
            router.push('/')

            return response.data
        } catch (error: any) {
            if (error.response) {
                ElMessage.error(error.response.data.message+"，即将跳转回首页..." || '登录失败，即将跳转回首页...')
            } else {
                ElMessage.error('网络错误')
            }
            setTimeout(() => {
                console.error('登录错误:', error)
                logout()
            }, 3000)
            throw error // 继续抛出错误以便组件可以处理
        }
    }

    // 注销方法
    const logout = () => {
        token.value = null
        role.value = 'guest'
        username.value = ''
        isLoggedIn.value = false

        // 清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('role')
        localStorage.removeItem('username')

        // 跳转到首页
        router.push('/')
    }

    // 权限检查方法
    const hasPermission = (requiredRole: UserRole | UserRole[]) => {
        if (!isLoggedIn.value) return false
        if (Array.isArray(requiredRole)) {
            return requiredRole.includes(role.value)
        }
        return role.value === requiredRole
    }

    return {
        token,
        role,
        username,
        isLoggedIn,
        login,
        logout,
        hasPermission,
        initFromStorage,
    }
})
