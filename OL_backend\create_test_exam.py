#!/usr/bin/env python
"""
创建测试考试数据的脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OL_backend.settings')
django.setup()

from app.models import User, Course, QuestionBank, Question, ExamPaper, PaperQuestion, Exam

def create_test_exam():
    """创建一个简单的测试考试"""
    
    # 获取或创建教师用户
    teacher, created = User.objects.get_or_create(
        username='teacher_test',
        defaults={
            'password': 'password123',
            'role': 'teacher',
            'status': 1
        }
    )
    
    # 获取或创建学生用户
    student, created = User.objects.get_or_create(
        username='student1',
        defaults={
            'password': 'password123',
            'role': 'student',
            'status': 1
        }
    )
    
    # 获取或创建课程
    course, created = Course.objects.get_or_create(
        title='计算机网络基础',
        defaults={
            'teacher_id': teacher.username,
            'category_id': 1,  # 假设分类ID为1
            'description': '计算机网络基础课程',
            'status': 'published'
        }
    )
    
    # 获取或创建题库
    question_bank, created = QuestionBank.objects.get_or_create(
        name='计算机网络题库',
        defaults={
            'description': '计算机网络基础题库',
            'teacher_id': teacher.username,
            'visibility': QuestionBank.PUBLIC
        }
    )
    
    # 创建题目
    questions_data = [
        {
            'type': 'single_choice',
            'content': 'TCP/IP协议中，下列哪个协议工作在网络层？',
            'options': ['HTTP', 'IP', 'TCP', 'UDP'],
            'answer': 'B',
            'score': 5
        },
        {
            'type': 'single_choice', 
            'content': '以下关于OSI七层模型的描述，错误的是：',
            'options': [
                '物理层负责比特流的传输',
                '数据链路层负责MAC地址的寻址',
                '网络层负责逻辑地址寻址',
                '传输层负责路由选择'
            ],
            'answer': 'D',
            'score': 5
        },
        {
            'type': 'multiple_choice',
            'content': '以下哪些协议工作在应用层？（多选）',
            'options': ['HTTP', 'FTP', 'IP', 'SMTP'],
            'answer': ['A', 'B', 'D'],
            'score': 10
        },
        {
            'type': 'judgment',
            'content': 'UDP协议提供可靠的数据传输服务。',
            'options': [],
            'answer': 'F',
            'score': 5
        },
        {
            'type': 'judgment',
            'content': 'HTTP是无状态的协议。',
            'options': [],
            'answer': 'T',
            'score': 5
        }
    ]
    
    questions = []
    for q_data in questions_data:
        question, created = Question.objects.get_or_create(
            bank=question_bank,
            content=q_data['content'],
            defaults={
                'type': q_data['type'],
                'options': q_data['options'],
                'answer': q_data['answer'],
                'score': q_data['score'],
                'difficulty': 2
            }
        )
        questions.append(question)
    
    # 创建试卷
    exam_paper, created = ExamPaper.objects.get_or_create(
        title='计算机网络基础测试',
        defaults={
            'description': '计算机网络基础知识测试',
            'teacher': teacher,
            'status': 'published',
            'total_score': 30,
            'difficulty': 2.0
        }
    )
    
    # 添加题目到试卷
    for i, question in enumerate(questions):
        PaperQuestion.objects.get_or_create(
            paper=exam_paper,
            question=question,
            defaults={
                'order_num': i + 1,
                'score': question.score
            }
        )
    
    # 创建考试
    now = timezone.now()
    start_time = now - timedelta(hours=1)  # 1小时前开始
    end_time = now + timedelta(hours=2)    # 2小时后结束
    
    exam, created = Exam.objects.get_or_create(
        title='计算机网络基础期中考试',
        course=course,
        defaults={
            'teacher': teacher,
            'paper': exam_paper,
            'description': '计算机网络基础期中考试，测试基础概念',
            'start_time': start_time,
            'end_time': end_time,
            'duration_minutes': 60,
            'pass_score': 18,
            'total_score': 30,
            'status': Exam.ONGOING,  # 设置为进行中
            'shuffle_questions': False,
            'shuffle_options': False,
            'allow_break': True
        }
    )
    
    print(f"测试考试创建成功！")
    print(f"考试ID: {exam.id}")
    print(f"考试标题: {exam.title}")
    print(f"课程ID: {course.id}")
    print(f"课程标题: {course.title}")
    print(f"教师: {teacher.username}")
    print(f"学生: {student.username}")
    print(f"考试状态: {exam.get_status_display()}")
    print(f"开始时间: {exam.start_time}")
    print(f"结束时间: {exam.end_time}")

if __name__ == '__main__':
    create_test_exam()
