<template>
    <div class="course-management">
        <div class="button-container">
            <el-button type="primary" @click="uploadNewCourse" size="large">上传新课程</el-button>
        </div>
        <hr />

        <div class="course-section">
            <h3>已发布课程</h3>
            <el-scrollbar>
                <div class="scrollbar-flex-content">
                    <el-row :gutter="20">
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="course in publishedCourses" :key="course.id">
                            <CourseCard :course="course" :is-published="true" @edit="editCourse"
                                @unpublish="unpublishCourse" />
                        </el-col>
                    </el-row>
                </div>
            </el-scrollbar>
            <el-empty v-if="publishedCourses.length === 0" description="暂无已发布课程" />
        </div>

        <div class="course-section">
            <h3>待发布课程</h3>
            <el-scrollbar>
                <div class="scrollbar-flex-content">
                    <el-row :gutter="20">
                        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="course in unpublishedCourses" :key="course.id">
                            <CourseCard :course="course" :is-published="false" @edit="editCourse"
                                @publish="publishCourse" @delete="deleteCourse" />
                        </el-col>
                    </el-row>
                </div>
            </el-scrollbar>
            <el-empty v-if="unpublishedCourses.length === 0" description="暂无待发布课程" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from '@/utils/axios'
import CourseCard from '@/views/Teacher/components/courseCard.vue'

const router = useRouter()
const publishedCourses = ref([])
const unpublishedCourses = ref([])

const fetchCourses = async () => {
    try {
        const response = await axios.get('/teacher/courses/')
        const allCourses = response.data.data || []

        publishedCourses.value = allCourses.filter(c => c.status === 'published')
        unpublishedCourses.value = allCourses.filter(c => c.status === 'draft')
    } catch (error) {
        ElMessage.error(`课程加载失败: ${error.response?.data?.message || error.message}`)
    }
}

const uploadNewCourse = () => {
    router.push('/course-create')
}

const editCourse = (course) => {
    router.push(`/course-edit/${course.id}`)
}

const publishCourse = async (course) => {
    try {
        await ElMessageBox.confirm(`确定要发布课程《${course.title}》吗？`, '提示')
        await axios.patch(`/courses/${course.id}/status/`, { status: 'published' })
        ElMessage.success('发布成功')
        await fetchCourses()
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('发布失败: ' + (error.response?.data?.message || error.message))
        }
    }
}

const unpublishCourse = async (course) => {
    try {
        await ElMessageBox.confirm(`确定要取消发布《${course.title}》吗？`, '提示')
        await axios.patch(`/courses/${course.id}/status/`, { status: 'draft' })
        ElMessage.success('已取消发布')
        await fetchCourses()
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
        }
    }
}

const deleteCourse = async (course) => {
    try {
        await ElMessageBox.confirm(`确定要删除《${course.title}》吗？`, '警告', {
            type: 'warning',
            confirmButtonText: '确认删除',
            cancelButtonText: '取消'
        })
        await axios.delete(`/courses/${course.id}/`)
        ElMessage.success('删除成功')
        await fetchCourses()
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
        }
    }
}

onMounted(fetchCourses)
</script>
<style scoped>
.course-management {
    padding: 20px 60px;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.course-section {
    margin-bottom: 50px;
}

.scrollbar-flex-content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 10px;
    margin-top: 20px;
}
</style>