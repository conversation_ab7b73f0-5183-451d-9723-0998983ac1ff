<template>
  <div class="personal-center">
    <!-- 个人信息区域 -->
    <div class="user-info-section">
      <div class="user-avatar" @click="showAvatarDialog = true">
        <el-avatar :size="100" :icon="UserFilled" :src="userInfo.avatar" />
        <div class="avatar-overlay">
          <el-icon><EditPen /></el-icon>
        </div>
      </div>
      <div class="user-details">
        <div class="user-info-item">
          <span class="label">昵称：</span>
          <span v-if="!isEditingNickname" class="value" @click="startEditNickname">{{
            userInfo.nickname
          }}</span>
          <el-input
            v-else
            v-model="userInfo.nickname"
            size="small"
            @blur="finishEditNickname"
            @keyup.enter="finishEditNickname"
          />
        </div>
        <div class="user-info-item">
          <span class="label">学号：</span>
          <span class="value">{{ userInfo.studentId }}</span>
        </div>
        <div class="user-info-item">
          <span class="label">个人签名：</span>
          <span v-if="!isEditingSignature" class="value" @click="startEditSignature">{{
            userInfo.signature
          }}</span>
          <el-input
            v-else
            v-model="userInfo.signature"
            size="small"
            @blur="finishEditSignature"
            @keyup.enter="finishEditSignature"
          />
        </div>
      </div>
      <div class="exam-status">
        <div class="exam-label">待完成考试</div>
        <div class="exam-count">{{ pendingExams }}</div>
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="nav-tabs">
      <el-button
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-btn', activeTab === tab.key ? 'active' : '']"
        @click="activeTab = tab.key"
      >
        {{ tab.label }}
      </el-button>
    </div>

    <!-- 内容区域 -->
    <div class="tab-content">
      <!-- 我的课程 -->
      <div v-if="activeTab === 'courses'" class="courses-container">
        <el-row :gutter="20">
          <el-col :span="8" v-for="course in courses" :key="course.id">
            <courseCard :course="course" />
          </el-col>
        </el-row>
      </div>

      <!-- 学习历史 -->
      <div v-else-if="activeTab === 'history'" class="history-container">
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in studyHistory"
            :key="index"
            :timestamp="activity.time"
            :type="activity.type"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 学习笔记 -->
      <div v-else-if="activeTab === 'notes'" class="notes-container">
        <el-empty v-if="notes.length === 0" description="暂无学习笔记" />
        <div v-else class="notes-list">
          <el-card v-for="(note, index) in notes" :key="index" class="note-card">
            <template #header>
              <div class="note-header">
                <span>{{ note.title }}</span>
                <div class="note-actions">
                  <el-button type="primary" text 
                    ><el-icon><EditPen /></el-icon
                  ></el-button>
                  <el-button type="danger" text @click="removeNote(index)"
                    ><el-icon><Delete /></el-icon
                  ></el-button>
                </div>
              </div>
            </template>
            <div class="note-content">{{ note.content }}</div>
            <div class="note-footer">
              <span class="note-time">{{ note.time }}</span>
              <span class="note-course">{{ note.course }}</span>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 我的收藏 -->
      <div v-else-if="activeTab === 'favorites'" class="favorites-container">
        <el-empty v-if="favorites.length === 0" description="暂无收藏内容" />
        <el-table v-else :data="favorites" style="width: 100%">
          <el-table-column prop="title" label="标题" />
          <el-table-column prop="type" label="类型" width="100" />
          <el-table-column prop="course" label="所属课程" width="180" />
          <el-table-column prop="time" label="收藏时间" width="180" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" link
                ><el-icon><View /></el-icon
              ></el-button>
              <el-button type="danger" link @click="removeFavorite(scope.$index)"
                ><el-icon><Delete /></el-icon
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 我的错题 -->
      <div v-else-if="activeTab === 'mistakes'" class="mistakes-container">
        <el-collapse v-if="mistakes.length > 0">
          <el-collapse-item
            v-for="(mistake, index) in mistakes"
            :key="index"
            :title="mistake.question"
          >
            <div class="mistake-detail">
              <div class="mistake-answer">
                <p><strong>你的答案：</strong> {{ mistake.userAnswer }}</p>
                <p><strong>正确答案：</strong> {{ mistake.correctAnswer }}</p>
              </div>
              <div class="mistake-explanation">
                <p><strong>解析：</strong> {{ mistake.explanation }}</p>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-empty v-else description="暂无错题记录" />
      </div>

      <!-- 待办事项 -->
      <div v-else-if="activeTab === 'todos'" class="todos-container">
        <div class="todos-header">
          <el-input v-model="newTodo" placeholder="添加新待办事项..." class="todo-input">
            <template #append>
              <el-button @click="addTodo">添加</el-button>
            </template>
          </el-input>
        </div>
        <el-tabs>
          <el-tab-pane label="待完成">
            <el-empty v-if="pendingTodos.length === 0" description="暂无待办事项" />
            <el-checkbox-group v-else v-model="checkedTodos">
              <div v-for="(todo, index) in pendingTodos" :key="index" class="todo-item">
                <el-checkbox :label="todo.id" @change="toggleTodo(todo)">{{
                  todo.content
                }}</el-checkbox>
                <span class="todo-time">{{ todo.time }}</span>
              </div>
            </el-checkbox-group>
          </el-tab-pane>
          <el-tab-pane label="已完成">
            <el-empty v-if="completedTodos.length === 0" description="暂无已完成事项" />
            <div v-else>
              <div v-for="(todo, index) in completedTodos" :key="index" class="todo-item completed">
                <span class="todo-content">{{ todo.content }}</span>
                <span class="todo-time">{{ todo.time }}</span>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 讨论记录 -->
      <div v-else-if="activeTab === 'discussions'" class="discussions-container">
        <el-empty v-if="discussions.length === 0" description="暂无讨论记录" />
        <div v-else class="discussions-list">
          <el-card v-for="(discussion, index) in discussions" :key="index" class="discussion-card">
            <template #header>
              <div class="discussion-header">
                <span>{{ discussion.title }}</span>
                <el-tag size="small">{{ discussion.course }}</el-tag>
              </div>
            </template>
            <div class="discussion-content">{{ discussion.content }}</div>
            <div class="discussion-footer">
              <span class="discussion-time">{{ discussion.time }}</span>
              <div class="discussion-stats">
                <span
                  ><el-icon><View /></el-icon> {{ discussion.views }}</span
                >
                <span
                  ><el-icon><ChatDotRound /></el-icon> {{ discussion.replies }}</span
                >
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarDialog" title="更换头像" width="400px">
      <div class="avatar-upload">
        <el-upload
          class="avatar-uploader"
          action="/api/upload/avatar/"
          :headers="{ Authorization: `Bearer ${userStore.token}` }"
          :show-file-list="false"
          :on-change="handleAvatarChange"
          :before-upload="beforeAvatarUpload"
          :http-request="customUploadAvatar"
        >
          <img v-if="avatarUrl" :src="avatarUrl" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAvatarDialog = false">取消</el-button>
          <el-button type="primary" @click="updateAvatar">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  UserFilled,
  Collection,
  EditPen,
  Delete,
  View,
  Plus,
  ChatDotRound,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import courseCard from '@/HomePage/courseCard.vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = ref({
  nickname: 'XXX',
  studentId: 'XXXXXX',
  signature: 'XXXXXX',
  avatar: '',
})

// 编辑状态
const isEditingNickname = ref(false)
const isEditingSignature = ref(false)

// 待完成考试数量
const pendingExams = ref(0)

// 头像上传
const showAvatarDialog = ref(false)
const avatarUrl = ref('')
const avatarFile = ref(null)

// 标签页配置
const activeTab = ref('courses')
const tabs = [
  { key: 'courses', label: '我的课程' },
  { key: 'history', label: '学习历史' },
  { key: 'notes', label: '学习笔记' },
  { key: 'favorites', label: '我的收藏' },
  { key: 'mistakes', label: '我的错题' },
  { key: 'todos', label: '待办事项' },
  { key: 'discussions', label: '讨论记录' },
]

// 课程数据
const courses = ref([
  {
    id: 1,
    title: '高等数学',
    description:
      '本课程主要讲授微积分、线性代数等高等数学知识，培养学生的数学思维和解决问题的能力。',
    progress: 75,
  },
  {
    id: 2,
    title: '计算机网络',
    description: '本课程介绍计算机网络的基本原理、协议和应用，包括网络架构、TCP/IP协议族等内容。',
    progress: 30,
  },
  {
    id: 3,
    title: '数据结构与算法',
    description: '本课程讲解常用数据结构和算法设计方法，培养学生的编程能力和算法思维。',
    progress: 50,
  },
])

// 学习历史
const studyHistory = ref([
  { time: '2023-06-15 14:30', content: '完成了《高等数学》第三章测验', type: 'success' },
  { time: '2023-06-14 10:20', content: '观看了《计算机网络》视频课程', type: 'primary' },
  { time: '2023-06-13 16:45', content: '提交了《数据结构与算法》作业', type: 'warning' },
  { time: '2023-06-12 09:30', content: '参加了线上讨论课', type: 'info' },
])

// 学习笔记
const notes = ref([
  {
    title: '高等数学笔记 - 微分方程',
    content:
      '微分方程是描述函数与其导数之间关系的方程。常见类型有：一阶微分方程、二阶微分方程、线性微分方程等...',
    time: '2023-06-10',
    course: '高等数学',
  },
  {
    title: 'TCP/IP协议详解',
    content: 'TCP/IP是互联网的基础协议，由网络层的IP协议和传输层的TCP协议组成...',
    time: '2023-06-05',
    course: '计算机网络',
  },
])

// 收藏内容
const favorites = ref([
  { title: '排序算法比较', type: '文章', course: '数据结构与算法', time: '2023-06-08' },
  { title: 'OSI七层模型', type: '笔记', course: '计算机网络', time: '2023-06-03' },
  { title: '微积分基本定理', type: '视频', course: '高等数学', time: '2023-05-28' },
])

// 错题记录
const mistakes = ref([
  {
    question: '下列关于TCP协议的描述，错误的是？',
    userAnswer: 'TCP是无连接的协议',
    correctAnswer: 'TCP是面向连接的协议',
    explanation: 'TCP是面向连接的协议，在传输数据前必须先建立连接，传输完成后再释放连接。',
  },
  {
    question: '求函数 f(x) = x² + 2x + 1 的导数',
    userAnswer: "f'(x) = x + 2",
    correctAnswer: "f'(x) = 2x + 2",
    explanation:
      "对于函数 f(x) = x² + 2x + 1，应用导数公式：(x^n)′ = n·x^(n-1)，得到 f'(x) = 2x + 2",
  },
])

// 待办事项
const newTodo = ref('')
const checkedTodos = ref([])
const todos = ref([
  { id: 1, content: '完成高数作业', time: '2023-06-16', completed: false },
  { id: 2, content: '观看计算机网络视频', time: '2023-06-17', completed: false },
  { id: 3, content: '准备数据结构期末考试', time: '2023-06-18', completed: false },
  { id: 4, content: '提交实验报告', time: '2023-06-15', completed: true },
])

// 讨论记录
const discussions = ref([
  {
    title: '关于网络层协议的讨论',
    content: '我对IP协议的工作原理有些疑问，特别是关于IP分片和重组的过程...',
    time: '2023-06-12',
    course: '计算机网络',
    views: 56,
    replies: 8,
  },
  {
    title: '排序算法效率比较',
    content: '在处理大数据集时，快速排序和归并排序哪个更高效？我做了一些测试...',
    time: '2023-06-07',
    course: '数据结构与算法',
    views: 42,
    replies: 5,
  },
])

// 计算属性
const pendingTodos = computed(() => todos.value.filter((todo) => !todo.completed))
const completedTodos = computed(() => todos.value.filter((todo) => todo.completed))

// 获取用户个人信息
const fetchUserProfile = async () => {
  try {
    const response = await axios.get('http://localhost:8000/api/user/profile/', {
      params: { username: userStore.username },
      headers: { Authorization: `Bearer ${userStore.token}` },
    })

    if (response.data.success) {
      const profileData = response.data.data
      userInfo.value = {
        nickname: profileData.nickname || userStore.username,
        studentId: profileData.studentId || userStore.username,
        signature: profileData.signature || '这个人很懒，什么都没留下',
        avatar: profileData.avatar || '',
      }
      pendingExams.value = profileData.pendingExams || 0
    } else {
      ElMessage.error(response.data.message || '获取个人信息失败')
    }
  } catch (error) {
    console.error('获取个人信息出错:', error)
    ElMessage.error('网络错误，请稍后重试')
  }
}

// 获取我的课程
const fetchMyCourses = async () => {
  try {
    const response = await axios.get('http://localhost:8000/api/student/courses/', {
      params: { student_id: userStore.username },
      headers: { Authorization: `Bearer ${userStore.token}` },
    })

    if (response.data.success) {
      courses.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取课程失败')
    }
  } catch (error) {
    console.error('获取课程出错:', error)
    ElMessage.error('网络错误，请稍后重试')
  }
}

// 开始编辑昵称
const startEditNickname = () => {
  isEditingNickname.value = true
}

// 结束编辑昵称并保存
const finishEditNickname = async () => {
  try {
    const response = await axios.put(
      'http://localhost:8000/api/user/profile/',
      {
        username: userStore.username,
        nickname: userInfo.value.nickname,
      },
      {
        headers: { Authorization: `Bearer ${userStore.token}` },
      },
    )

    if (response.data.success) {
      ElMessage.success('昵称更新成功')
    } else {
      ElMessage.error(response.data.message || '昵称更新失败')
    }
  } catch (error) {
    console.error('更新昵称出错:', error)
    ElMessage.error('网络错误，请稍后重试')
  }

  isEditingNickname.value = false
}

// 开始编辑签名
const startEditSignature = () => {
  isEditingSignature.value = true
}

// 结束编辑签名并保存
const finishEditSignature = async () => {
  try {
    const response = await axios.put(
      'http://localhost:8000/api/user/profile/',
      {
        username: userStore.username,
        signature: userInfo.value.signature,
      },
      {
        headers: { Authorization: `Bearer ${userStore.token}` },
      },
    )

    if (response.data.success) {
      ElMessage.success('个人签名更新成功')
    } else {
      ElMessage.error(response.data.message || '个人签名更新失败')
    }
  } catch (error) {
    console.error('更新个人签名出错:', error)
    ElMessage.error('网络错误，请稍后重试')
  }

  isEditingSignature.value = false
}

// 头像处理
const handleAvatarChange = (file) => {
  avatarFile.value = file.raw
  avatarUrl.value = URL.createObjectURL(file.raw)
}

// 头像上传前的验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('上传头像图片只能是图片格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isImage && isLt2M
}

// 自定义上传方法
const customUploadAvatar = () => {
  // 阻止默认上传行为，使用updateAvatar方法处理
  return false
}

// 更新头像
const updateAvatar = async () => {
  try {
    if (!avatarUrl.value || !avatarFile.value) {
      ElMessage.warning('请先选择头像图片')
      return
    }

    // 创建FormData对象，用于文件上传
    const formData = new FormData()
    formData.append('file', avatarFile.value)
    formData.append('username', userStore.username)

    // 上传头像文件
    const uploadResponse = await axios.post('http://localhost:8000/api/upload/avatar/', formData, {
      headers: {
        Authorization: `Bearer ${userStore.token}`,
        'Content-Type': 'multipart/form-data',
      },
    })

    if (!uploadResponse.data.success) {
      ElMessage.error(uploadResponse.data.message || '头像上传失败')
      return
    }

    // 获取上传后的URL
    const avatarPath = uploadResponse.data.url

    // 更新用户信息中的头像
    const response = await axios.put(
      'http://localhost:8000/api/user/profile/',
      {
        username: userStore.username,
        avatar: avatarPath,
      },
      {
        headers: { Authorization: `Bearer ${userStore.token}` },
      },
    )

    if (response.data.success) {
      showAvatarDialog.value = false
      ElMessage.success('头像更新成功')
      // 更新本地头像显示
      await fetchUserProfile()
    } else {
      ElMessage.error(response.data.message || '头像更新失败')
    }
  } catch (error) {
    console.error('更新头像出错:', error)
    ElMessage.error('网络错误，请稍后重试')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchUserProfile()
  fetchMyCourses()
})

// 方法
const addTodo = () => {
  if (newTodo.value.trim()) {
    const now = new Date()
    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`

    todos.value.push({
      id: todos.value.length + 1,
      content: newTodo.value,
      time: dateStr,
      completed: false,
    })
    newTodo.value = ''
  }
}

const toggleTodo = (todo) => {
  todo.completed = !todo.completed
}

//删除收藏
const removeFavorite = (index) => {
  favorites.value.splice(index, 1)
  ElMessage.success('已删除收藏项')
}

//删除错题
const removeNote = (index) => {
  notes.value.splice(index, 1)
  ElMessage.success('已删除笔记')
}

</script>



<style scoped>
.personal-center {
  width: 100%;
  padding: 0 80px 40px 80px;
}

.user-info-section {
  background-color: var(--primary-color);
  color: white;
  padding: 30px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  position: relative;
}

.user-avatar {
  margin-right: 30px;
  position: relative;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.user-avatar:hover .avatar-overlay {
  opacity: 1;
}

.user-details {
  flex: 1;
}

.user-info-item {
  margin-bottom: 10px;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 5px;
  white-space: nowrap;
}

.value {
  cursor: pointer;
  padding: 2px 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.value:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.exam-status {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 15px 30px;
  border-radius: 8px;
  text-align: center;
}

.exam-label {
  font-size: 16px;
  margin-bottom: 5px;
}

.exam-count {
  font-size: 36px;
  font-weight: bold;
}

.nav-tabs {
  margin: 30px 0;
  display: flex;
  flex-wrap: wrap;
}

.tab-btn {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 12px 24px;
  font-size: 16px;
  border: none;
  background-color: #e9e9e9;
  color: var(--text-regular);
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.courses-container {
  margin-top: 20px;
}

.course-card {
  background-color: var(--bg-card);
  border: 1px solid var(--course-card-border);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  cursor: pointer;
}

.course-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.course-image {
  width: 100%;
  height: 160px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-light-9);
}

.course-image .el-icon {
  color: var(--primary-color);
}

.course-info {
  padding: 15px;
}

.course-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: var(--text-primary);
}

.course-desc {
  margin: 0 0 15px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  height: 63px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.course-progress {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.history-container {
  padding: 20px;
  background-color: var(--bg-container);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.notes-list,
.discussions-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.note-card,
.discussion-card {
  height: 100%;
}

.note-header,
.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.note-content,
.discussion-content {
  margin-bottom: 15px;
  color: var(--text-regular);
  line-height: 1.5;
}

.note-footer,
.discussion-footer {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-size: 12px;
}

.discussion-stats {
  display: flex;
  gap: 10px;
}

.todos-container {
  padding: 20px;
  background-color: var(--bg-container);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.todos-header {
  margin-bottom: 20px;
}

.todo-input {
  max-width: 500px;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-lighter);
}

.todo-item.completed {
  color: var(--text-secondary);
  text-decoration: line-through;
}

.todo-time {
  color: var(--text-secondary);
  font-size: 12px;
}

.mistakes-container {
  padding: 20px;
  background-color: var(--bg-container);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.mistake-detail {
  padding: 10px;
  background-color: var(--bg-page);
  border-radius: 4px;
}

.mistake-answer {
  margin-bottom: 10px;
}

.avatar-upload {
  display: flex;
  justify-content: center;
}

.avatar-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed var(--border-base);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-uploader:hover {
  border-color: var(--primary-color);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-preview {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
