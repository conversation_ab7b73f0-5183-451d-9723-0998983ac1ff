<template>
    <el-dialog :title="dialogTitle" v-model="visible" width="50%">
        <el-form :model="question" label-width="100px">
            <!-- 题目类型（只读，由添加按钮决定） -->
            <el-form-item label="题目类型">
                <el-tag>{{ questionTypeName }}</el-tag>
            </el-form-item>

            <!-- 题目内容 -->
            <el-form-item label="题目内容" required>
                <el-input v-model="question.content" type="textarea" :rows="3" placeholder="请输入题目内容" />
            </el-form-item>

            <!-- 选择题选项（仅当类型为选择题时显示） -->
            <el-form-item v-if="question.type === 'single_choice' || question.type === 'multiple_choice'" label="选项">
                <div v-for="(option, index) in question.options" :key="index" class="option-item">
                    <el-input v-model="question.options[index]" :placeholder="'选项 ' + (index + 1)"
                        style="width: 80%; margin-right: 10px" />
                    <el-button type="danger" size="small" circle @click="removeOption(index)" v-if="question.options.length > 2">
                        <el-icon>
                            <Minus />
                        </el-icon>
                    </el-button>
                </div>
                <el-button type="primary" @click="addOption">添加选项</el-button>
            </el-form-item>

            <!-- 答案 -->
            <el-form-item label="正确答案" required>
                <template v-if="question.type === 'single_choice' || question.type === 'multiple_choice'">
                    <el-select v-model="question.answer" multiple v-if="question.type === 'multiple_choice'"
                        placeholder="请选择正确答案">
                        <el-option v-for="(option, index) in question.options" :key="index"
                            :label="'选项' + (index + 1) + ': ' + option" :value="index" />
                    </el-select>
                    <el-select v-model="question.answer" v-else placeholder="请选择正确答案">
                        <el-option v-for="(option, index) in question.options" :key="index"
                            :label="'选项' + (index + 1) + ': ' + option" :value="index" />
                    </el-select>
                </template>

                <el-input v-else-if="question.type === 'fill_blank'" v-model="question.answer"
                    placeholder="请输入填空答案，多个答案用|分隔" />

                <el-input v-else v-model="question.answer" type="textarea" :rows="3" placeholder="请输入答案" />
            </el-form-item>

            <!-- 解析 -->
            <el-form-item label="题目解析">
                <el-input v-model="question.analysis" type="textarea" :rows="3" placeholder="请输入题目解析" />
            </el-form-item>

            <!-- 难度 -->
            <el-form-item label="题目难度">
                <el-rate v-model="question.difficulty" :max="5" show-text :texts="['简单', '较易', '中等', '较难', '困难']" />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    question: {
        type: Object,
        required: true
    },
    dialogTitle: {
        type: String,
        default: '编辑试题'
    }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = defineModel()

// 题目类型名称映射
const questionTypeName = computed(() => {
    const typeMap = {
        'single_choice': '单选题',
        'multiple_choice': '多选题',
        'judgment': '判断题',
        'fill_blank': '填空题',
        'essay': '问答题'
    }
    return typeMap[props.question.type] || '未知题型'
})

// 添加选项
const addOption = () => {
    props.question.options.push('')
}

// 移除选项
const removeOption = (index) => {
    props.question.options.splice(index, 1)
}

// 提交表单
const submitForm = () => {
    emit('submit', props.question)
    visible.value = false
}
</script>

<style scoped>
.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
</style>