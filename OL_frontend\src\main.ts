import './assets/base.css'
import 'element-plus/dist/index.css' // 全局应用样式

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

import router from './router/index.ts'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css' // 全局应用样式

// 引入 Element Plus 图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 创建并配置Pinia
const pinia = createPinia()

const app = createApp(App)

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 确保在挂载应用之前已初始化Pinia状态
import { useUserStore } from './stores/user'
const userStore = useUserStore()

app.mount('#app')
export default router
