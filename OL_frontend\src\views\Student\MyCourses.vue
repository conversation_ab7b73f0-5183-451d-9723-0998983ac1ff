<template>
  <div class="my-courses-page">
    <!-- <Header /> -->
    <div class="page-header">
      <h1 class="page-title">
        我的课程
        <el-tooltip content="刷新课程列表" placement="right">
          <el-icon class="refresh-btn" @click="fetchCourses"><Refresh /></el-icon>
        </el-tooltip>
      </h1>
      <div class="header-actions">
        <el-input
          placeholder="搜索我的课程"
          v-model="searchQuery"
          class="search-input"
          :prefix-icon="Search"
          clearable
        />
        <el-radio-group v-model="courseStatus" size="small">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="inProgress">进行中</el-radio-button>
          <el-radio-button label="completed">已完成</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="courses-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 错误提示 -->
      <el-alert v-else-if="error" :title="error" type="error" :closable="false" show-icon />

      <!-- 空数据提示 -->
      <el-empty v-else-if="filteredCourses.length === 0" description="暂无相关课程" />

      <!-- 课程列表 -->
      <el-row :gutter="20" v-else>
        <el-col :xs="24" :sm="12" :md="8" v-for="course in filteredCourses" :key="course.id">
          <div class="course-card">
            <div class="course-header">
              <div class="course-image">
                <img
                  v-if="course.cover_url"
                  :src="course.cover_url"
                  alt="课程封面"
                  class="course-cover"
                />
                <el-icon v-else :size="60"><Collection /></el-icon>
              </div>
              <div class="course-info">
                <h3 class="course-title">{{ course.title }}</h3>
                <div class="course-meta">
                  <span
                    ><el-icon><User /></el-icon> {{ course.teacher }}</span
                  >
                  <el-tag :type="getStatusType(course.status)" size="small">{{
                    getStatusText(course.status)
                  }}</el-tag>
                </div>
              </div>
            </div>

            <div class="course-progress">
              <div class="progress-text">
                <span>学习进度: {{ course.progress }}%</span>
                <span>{{ course.completedTasks }}/{{ course.totalTasks }} 任务</span>
              </div>
              <el-progress
                :percentage="course.progress"
                :status="getProgressStatus(course.progress)"
              ></el-progress>
            </div>

            <div class="course-actions">
              <el-button type="primary" @click="continueLearning(course)">继续学习</el-button>
              <el-dropdown trigger="click">
                <el-button type="default">
                  更多 <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="viewTaskList(course)">任务清单</el-dropdown-item>
                    <el-dropdown-item @click="viewResources(course)">课程资料</el-dropdown-item>
                    <el-dropdown-item @click="viewDiscussions(course)">课程讨论</el-dropdown-item>
                    <el-dropdown-item divided @click="unenrollCourse(course)"
                      >退出课程</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 任务清单对话框 -->
    <el-dialog
      v-model="showTasksDialog"
      :title="currentCourse ? currentCourse.title + ' - 学习任务清单' : '学习任务清单'"
      width="700px"
    >
      <div class="tasks-container">
        <el-tabs v-model="taskTabActive">
          <el-tab-pane label="待完成任务" name="pending">
            <el-table :data="pendingTasks" style="width: 100%">
              <el-table-column prop="title" label="任务名称" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="scope">
                  <el-tag :type="getTaskTypeTag(scope.row.type)">{{ scope.row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="deadline" label="截止日期" width="120" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="primary" link @click="startTask(scope.row)">开始</el-button>
                  <el-button type="info" link @click="viewTaskDetail(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已完成任务" name="completed">
            <el-table :data="completedTasks" style="width: 100%">
              <el-table-column prop="title" label="任务名称" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="scope">
                  <el-tag :type="getTaskTypeTag(scope.row.type)">{{ scope.row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="completedDate" label="完成日期" width="120" />
              <el-table-column prop="score" label="得分" width="80" />
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="info" link @click="viewTaskDetail(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 课程资料对话框 -->
    <el-dialog
      v-model="showResourcesDialog"
      :title="currentCourse ? currentCourse.title + ' - 课程资料' : '课程资料'"
      width="700px"
    >
      <div class="resources-container">
        <el-collapse>
          <el-collapse-item
            v-for="(section, index) in courseResources"
            :key="index"
            :title="section.title"
          >
            <div class="resource-list">
              <div
                v-for="(resource, rIndex) in section.resources"
                :key="rIndex"
                class="resource-item"
              >
                <div class="resource-info">
                  <el-icon class="resource-icon" :class="getResourceIconClass(resource.type)">
                    <Document v-if="resource.type === 'pdf'" />
                    <VideoPlay v-else-if="resource.type === 'video'" />
                    <Reading v-else-if="resource.type === 'article'" />
                    <Files v-else />
                  </el-icon>
                  <div class="resource-details">
                    <div class="resource-title">{{ resource.title }}</div>
                    <div class="resource-meta">
                      <span>{{ getResourceTypeText(resource.type) }}</span>
                      <span>{{ resource.size }}</span>
                    </div>
                  </div>
                </div>
                <div class="resource-actions">
                  <el-button type="primary" link @click="viewResource(resource)">查看</el-button>
                  <el-button type="success" link @click="downloadResource(resource)"
                    >下载</el-button
                  >
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-dialog>

    <!-- 课程讨论对话框 -->
    <el-dialog
      v-model="showDiscussionsDialog"
      :title="currentCourse ? currentCourse.title + ' - 课程讨论' : '课程讨论'"
      width="700px"
    >
      <div class="discussions-container">
        <div class="discussion-header">
          <el-input v-model="newDiscussion" placeholder="发表新讨论..." class="discussion-input">
            <template #append>
              <el-button type="primary" @click="postDiscussion">发布</el-button>
            </template>
          </el-input>
        </div>

        <div class="discussion-list">
          <div
            v-for="(discussion, index) in courseDiscussions"
            :key="index"
            class="discussion-item"
          >
            <div class="discussion-user">
              <el-avatar :size="40" :icon="UserFilled" />
              <div class="user-info">
                <div class="username">{{ discussion.username }}</div>
                <div class="post-time">{{ discussion.time }}</div>
              </div>
            </div>
            <div class="discussion-content">{{ discussion.content }}</div>
            <div class="discussion-actions">
              <el-button type="text" @click="likeDiscussion(discussion)">
                <el-icon><Star /></el-icon> 点赞 ({{ discussion.likes }})
              </el-button>
              <el-button type="text" @click="replyDiscussion(discussion)">
                <el-icon><ChatDotRound /></el-icon> 回复 ({{ discussion.replies.length }})
              </el-button>
            </div>
            <div v-if="discussion.showReplies" class="discussion-replies">
              <div v-for="(reply, rIndex) in discussion.replies" :key="rIndex" class="reply-item">
                <div class="reply-user">
                  <el-avatar :size="30" :icon="UserFilled" />
                  <div class="user-info">
                    <div class="username">{{ reply.username }}</div>
                    <div class="post-time">{{ reply.time }}</div>
                  </div>
                </div>
                <div class="reply-content">{{ reply.content }}</div>
              </div>
              <div class="reply-input-container">
                <el-input v-model="newReply" placeholder="回复..." size="small" class="reply-input">
                  <template #append>
                    <el-button size="small" @click="submitReply(discussion)">回复</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Search,
  Collection,
  User,
  ArrowDown,
  Document,
  VideoPlay,
  Reading,
  Files,
  UserFilled,
  Star,
  ChatDotRound,
  Refresh,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import axios from 'axios'
import Header from '@/components/header.vue'

const router = useRouter()
const userStore = useUserStore()

// 搜索和过滤
const searchQuery = ref('')
const courseStatus = ref('all')

// 对话框控制
const showTasksDialog = ref(false)
const showResourcesDialog = ref(false)
const showDiscussionsDialog = ref(false)
const taskTabActive = ref('pending')
const currentCourse = ref(null)

// 讨论相关
const newDiscussion = ref('')
const newReply = ref('')

// 我的课程数据
const myCourses = ref([])
const loading = ref(false)
const error = ref(null)

// 从后端获取课程数据
const fetchCourses = async () => {
  loading.value = true
  error.value = null

  try {
    // 获取当前用户名作为学生ID
    const studentId = userStore.username

    const response = await axios.get(`http://localhost:8000/api/student/courses/`, {
      params: { student_id: studentId },
      headers: {
        Authorization: `Bearer ${userStore.token}`,
      },
    })

    if (response.data.success) {
      myCourses.value = response.data.data
      console.log('获取到课程数据:', myCourses.value)
    } else {
      error.value = response.data.message || '获取课程列表失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    console.error('获取课程列表出错:', err)
    error.value = err.response?.data?.message || '网络错误，请稍后重试'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCourses()
})

// 计算属性：过滤后的课程
const filteredCourses = computed(() => {
  return myCourses.value.filter((course) => {
    // 状态过滤
    if (courseStatus.value !== 'all' && course.status !== courseStatus.value) {
      return false
    }

    // 搜索过滤
    if (
      searchQuery.value &&
      !course.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    ) {
      return false
    }

    return true
  })
})

// 任务数据
const courseTasks = {
  1: [
    { id: 1, title: '观看第一章视频', type: '视频', deadline: '2023-07-10', completed: false },
    { id: 2, title: '完成第一章习题', type: '作业', deadline: '2023-07-12', completed: false },
    {
      id: 3,
      title: '参与讨论：极限的概念',
      type: '讨论',
      deadline: '2023-07-15',
      completed: false,
    },
    {
      id: 4,
      title: '观看第二章视频',
      type: '视频',
      deadline: '2023-07-20',
      completed: true,
      completedDate: '2023-06-25',
      score: 100,
    },
    {
      id: 5,
      title: '期中测验',
      type: '考试',
      deadline: '2023-07-30',
      completed: true,
      completedDate: '2023-06-28',
      score: 92,
    },
  ],
  2: [
    { id: 1, title: '观看网络基础视频', type: '视频', deadline: '2023-07-15', completed: false },
    { id: 2, title: '完成TCP/IP协议分析', type: '作业', deadline: '2023-07-18', completed: false },
    { id: 3, title: '网络安全实验', type: '实验', deadline: '2023-07-25', completed: false },
  ],
}

// 课程资料数据
const courseResources = [
  {
    title: '第一章：函数与极限',
    resources: [
      { id: 1, title: '函数与极限讲义', type: 'pdf', size: '2.5MB' },
      { id: 2, title: '极限计算方法视频', type: 'video', size: '150MB' },
      { id: 3, title: '函数连续性补充材料', type: 'pdf', size: '1.8MB' },
    ],
  },
  {
    title: '第二章：导数与微分',
    resources: [
      { id: 4, title: '导数概念与计算', type: 'pdf', size: '3.2MB' },
      { id: 5, title: '微分应用案例', type: 'video', size: '180MB' },
      { id: 6, title: '导数应用专题', type: 'article', size: '1.2MB' },
    ],
  },
]

// 课程讨论数据
const courseDiscussions = ref([
  {
    id: 1,
    username: '学生A',
    time: '2023-06-28 14:30',
    content: '关于极限的概念，我有一些疑问，特别是在处理无穷小量时，如何判断它们的阶数？',
    likes: 5,
    showReplies: false,
    replies: [
      {
        username: '张教授',
        time: '2023-06-28 15:20',
        content: '无穷小量的阶数比较是通过极限计算的，具体可以参考课本第15页的例题。',
      },
      {
        username: '学生B',
        time: '2023-06-28 16:05',
        content: '我也有类似的问题，谢谢教授的解答！',
      },
    ],
  },
  {
    id: 2,
    username: '学生C',
    time: '2023-06-27 09:45',
    content: '大家对于第二章的微分应用有什么好的学习方法吗？我感觉例题有点难理解。',
    likes: 3,
    showReplies: false,
    replies: [
      {
        username: '学生D',
        time: '2023-06-27 10:30',
        content: '我觉得多做一些实际应用题会有帮助，课本后面的习题很有针对性。',
      },
    ],
  },
])

// 计算属性：当前课程的待完成任务
const pendingTasks = computed(() => {
  if (!currentCourse.value) return []
  const tasks = courseTasks[currentCourse.value.id] || []
  return tasks.filter((task) => !task.completed)
})

// 计算属性：当前课程的已完成任务
const completedTasks = computed(() => {
  if (!currentCourse.value) return []
  const tasks = courseTasks[currentCourse.value.id] || []
  return tasks.filter((task) => task.completed)
})

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'inProgress':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'notStarted':
      return '未开始'
    default:
      return '未知状态'
  }
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'inProgress':
      return 'primary'
    case 'completed':
      return 'success'
    case 'notStarted':
      return 'info'
    default:
      return 'info'
  }
}

// 获取进度状态
const getProgressStatus = (progress) => {
  if (progress >= 100) return 'success'
  if (progress >= 50) return ''
  return 'warning'
}

// 获取任务类型标签
const getTaskTypeTag = (type) => {
  switch (type) {
    case '视频':
      return 'info'
    case '作业':
      return 'primary'
    case '讨论':
      return 'warning'
    case '考试':
      return 'danger'
    case '实验':
      return 'success'
    default:
      return 'info'
  }
}

// 获取资源图标类
const getResourceIconClass = (type) => {
  return `resource-icon-${type}`
}

// 获取资源类型文本
const getResourceTypeText = (type) => {
  switch (type) {
    case 'pdf':
      return 'PDF文档'
    case 'video':
      return '视频'
    case 'article':
      return '文章'
    default:
      return '文件'
  }
}

// 继续学习
const continueLearning = (course) => {
  console.log('继续学习课程:', course.title)
  router.push({
    path: '/study',
    query: { course_id: course.id },
  })
}

// 查看任务清单
const viewTaskList = (course) => {
  currentCourse.value = course
  showTasksDialog.value = true
}

// 查看课程资料
const viewResources = (course) => {
  currentCourse.value = course
  showResourcesDialog.value = true
}

// 查看课程讨论
const viewDiscussions = (course) => {
  currentCourse.value = course
  showDiscussionsDialog.value = true
}

// 退出课程
const unenrollCourse = (course) => {
  ElMessageBox.confirm(`确定要退出课程"${course.title}"吗？`, '退出课程', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 这里添加退出课程的逻辑
      myCourses.value = myCourses.value.filter((c) => c.id !== course.id)
      ElMessage({
        type: 'success',
        message: '已成功退出课程',
      })
    })
    .catch(() => {
      // 取消操作
    })
}

// 开始任务
const startTask = (task) => {
  console.log('开始任务:', task.title)
  // 根据任务类型跳转到不同页面
  switch (task.type) {
    case '视频':
      // 跳转到视频播放页面
      router.push('/study')
      break
    case '作业':
      // 跳转到作业提交页面
      router.push('/todo')
      break
    case '讨论':
      // 跳转到讨论页面
      router.push('/study')
      break
    case '考试':
      // 跳转到考试列表页面
      router.push('/exam-list')
      break
    case '实验':
      // 跳转到实验页面
      router.push('/study')
      break
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  console.log('查看任务详情:', task.title)
  // 显示任务详情
}

// 查看资源
const viewResource = (resource) => {
  console.log('查看资源:', resource.title)
  // 根据资源类型执行不同操作
  if (resource.type === 'video') {
    router.push('/study')
  } else {
    // 对于其他类型的资源，可以直接打开或下载
    ElMessage.success(`正在打开资源: ${resource.title}`)
  }
}

// 下载资源
const downloadResource = (resource) => {
  console.log('下载资源:', resource.title)
  // 下载资源
}

// 发布讨论
const postDiscussion = () => {
  if (!newDiscussion.value.trim()) return

  courseDiscussions.value.unshift({
    id: courseDiscussions.value.length + 1,
    username: '我',
    time: new Date().toLocaleString(),
    content: newDiscussion.value,
    likes: 0,
    showReplies: false,
    replies: [],
  })

  newDiscussion.value = ''
}

// 点赞讨论
const likeDiscussion = (discussion) => {
  discussion.likes++
}

// 回复讨论
const replyDiscussion = (discussion) => {
  discussion.showReplies = !discussion.showReplies
}

// 提交回复
const submitReply = (discussion) => {
  if (!newReply.value.trim()) return

  discussion.replies.push({
    username: '我',
    time: new Date().toLocaleString(),
    content: newReply.value,
  })

  newReply.value = ''
}
</script>

<style scoped>
.my-courses-page {
  padding: 0 40px 40px 40px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-input {
  width: 250px;
}

.course-card {
  background-color: var(--bg-card);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.course-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.course-header {
  display: flex;
  margin-bottom: 20px;
}

.course-image {
  width: 80px;
  height: 80px;
  background-color: var(--primary-light-9);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  overflow: hidden;
}

.course-image .el-icon {
  color: var(--primary-color);
}

.course-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info {
  flex: 1;
}

.course-title {
  font-size: 18px;
  color: var(--text-primary);
  margin: 0 0 10px 0;
}

.course-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-secondary);
  font-size: 14px;
}

.course-meta span {
  display: flex;
  align-items: center;
}

.course-meta .el-icon {
  margin-right: 5px;
}

.course-progress {
  margin-bottom: 20px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--text-secondary);
}

.course-actions {
  display: flex;
  justify-content: space-between;
}

/* 任务清单样式 */
.tasks-container {
  max-height: 500px;
  overflow-y: auto;
}

/* 资源列表样式 */
.resources-container {
  max-height: 500px;
  overflow-y: auto;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  background-color: var(--bg-hover);
}

.resource-info {
  display: flex;
  align-items: center;
}

.resource-icon {
  font-size: 24px;
  margin-right: 10px;
}

.resource-icon-pdf {
  color: #f56c6c;
}

.resource-icon-video {
  color: #409eff;
}

.resource-icon-article {
  color: #67c23a;
}

.resource-details {
  display: flex;
  flex-direction: column;
}

.resource-title {
  font-size: 14px;
  color: var(--text-primary);
}

.resource-meta {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  gap: 10px;
}

/* 讨论样式 */
.discussions-container {
  max-height: 500px;
  overflow-y: auto;
}

.discussion-header {
  margin-bottom: 20px;
}

.discussion-input {
  width: 100%;
}

.discussion-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.discussion-item {
  padding: 15px;
  border-radius: 8px;
  background-color: var(--bg-hover);
}

.discussion-user {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.user-info {
  margin-left: 10px;
}

.username {
  font-size: 14px;
  font-weight: bold;
  color: var(--text-primary);
}

.post-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.discussion-content {
  margin-bottom: 10px;
  line-height: 1.5;
  color: var(--text-regular);
}

.discussion-actions {
  display: flex;
  gap: 15px;
}

.discussion-replies {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: var(--bg-container);
}

.reply-item {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-lighter);
}

.reply-user {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.reply-content {
  margin-left: 40px;
  font-size: 14px;
  color: var(--text-regular);
}

.reply-input-container {
  margin-top: 10px;
  margin-left: 40px;
}

.reply-input {
  width: 100%;
}

.refresh-btn {
  margin-left: 10px;
  cursor: pointer;
  font-size: 20px;
  color: var(--primary-color);
}
</style>
