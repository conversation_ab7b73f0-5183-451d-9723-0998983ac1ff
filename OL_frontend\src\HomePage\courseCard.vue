<template>
  <div class="course-card" @click="gotoDetail">
    <div class="course-image">
      <img v-if="course.cover_url" :src="course.cover_url" alt="课程封面" class="course-cover" />
      <el-icon v-else :size="60"><Collection /></el-icon>
    </div>
    <div class="course-info">
      <h3 class="course-title">{{ course.title }}</h3>
      <p class="course-desc">{{ course.description }}</p>
      <div class="course-meta">
        <span
          ><el-icon>
            <User />
          </el-icon>
          {{ course.enroll_count }}</span
        >
        <span
          ><el-icon>
            <Star />
          </el-icon>
          {{ course.favorite_count }}</span
        >
        <span width="20px"
          ><el-icon>
            <Calendar />
          </el-icon>
          {{ course.update_time }}</span
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Collection, User, Star, Calendar } from '@element-plus/icons-vue'

const router = useRouter()

// 定义 props
const props = defineProps({
  course: {
    type: Object,
    required: true,
  },
})

const gotoDetail = () => {
  router.push({
    path: '/course/detail',
    query: {
      id: props.course.id, // 传递课程ID作为参数
    },
  })
}
</script>

<style scoped>
.course-grid {
  margin-top: 20px;
}

/* 课程卡片样式 */
.course-card {
  background-color: var(--bg-card);
  border: 1px solid var(--course-card-border);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  cursor: pointer;
}

.course-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.course-image {
  width: 100%;
  height: 160px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-light-9);
  position: relative;
}

.course-image img {
  width: 100%;
  height: 100%;
}

.course-info {
  padding: 15px;
}

.course-tags {
  margin-bottom: 10px;
}

.course-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: var(--text-primary);
}

.course-desc {
  margin: 0 0 15px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  height: 63px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.course-meta {
  display: flex;
  font-size: 13px;
  color: var(--text-secondary);
}

.course-meta span {
  display: flex;
  align-items: center;
  margin-right: 15px;
  white-space: nowrap;
}

.course-meta .el-icon {
  margin-right: 5px;
}

/* 徽章样式 */
.new-badge,
.hot-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.new-badge {
  background-color: var(--warning-color);
}

.hot-badge {
  background-color: var(--danger-color);
}

.course-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
