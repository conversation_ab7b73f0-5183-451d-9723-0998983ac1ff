<template>
    <div v-if="loading">加载中...</div>
    <div v-else-if="course" class="course-detail-container">
        <!-- 第一部分：课程基本信息卡片 -->
        <el-card class="course-info-card">
            <div class="course-info-wrapper">
                <div class="course-image">
                    <el-carousel height="280px" indicator-position="none" autoplay>
                        <el-carousel-item v-for="(item, index) in course.data.carousels" :key="index">
                            <div class="carousel-wrapper">
                                <img :src="item.image_url" alt="课程图片" class="carousel-img" />
                                <div class="custom-indicators">
                                    <span v-for="(dot, idx) in course.data.carousels" :key="idx"
                                        :class="{ dot: true, active: idx === currentSlide }"></span>
                                </div>
                            </div>
                        </el-carousel-item>
                    </el-carousel>
                </div>
                <div class="course-content">
                    <h1 class="course-title">{{ course.data.title }}</h1>
                    <div class="course-meta">
                        <span class="instructor">{{ course.data.category.name }}</span>
                    </div>
                    <p class="course-description">{{ course.data.description }}</p>
                    <div class="course-stats">
                        <span><i class="el-icon-user"></i> 注册人数：{{ course.data.enroll_count }}</span>
                        <span><i class="el-icon-star-off"></i> 喜欢人数：{{ course.data.favorite_count }}</span>
                    </div>
                    <div class="course-actions">
                        <el-button type="primary" size="large" @click="handleEnrollCourse" :loading="enrollLoading">
                            {{ isEnrolled ? '进入学习' : '注册课程' }}
                        </el-button>
                        <el-button type="primary" size="large" @click="goToExam(course.data.id)">
                            进入考试
                        </el-button>
                        <el-button :type="isFavorite ? 'danger' : 'default'" size="large" @click="toggleFavorite"
                            :loading="favoriteLoading">
                            <el-icon>
                                <component :is="isFavorite ? 'Star' : 'StarFilled'" />
                            </el-icon>
                            {{ isFavorite ? '取消收藏' : '收藏课程' }}
                        </el-button>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 第二部分：选项卡卡片 -->
        <el-card class="tab-card">
            <el-tabs v-model="activeTab" class="custom-tabs">
                <el-tab-pane label="课程简介" name="intro">
                    <div class="tab-content">
                        <h3>课程详细介绍</h3>
                        <p>{{ course.data.description }}</p>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="讨论区" name="discussion">
                    <div class="tab-content">
                        <h3>课程讨论区</h3>
                        <div v-if="comments.length > 0" class="comments-section">
                            <div v-for="comment in comments" :key="comment.id" class="comment-item">
                                <div class="comment-header">
                                    <span class="comment-author">{{ comment.username }}</span>
                                    <span class="comment-time">{{ formatDate(comment.time) }}</span>
                                </div>
                                <div class="comment-content">{{ comment.content }}</div>
                            </div>
                        </div>
                        <div v-else class="no-comments">
                            <p>暂无评论</p>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="课件列表" name="materials">
                    <div class="tab-content">
                        <h3>课程资料</h3>
                        <el-table :data="courseMaterials" style="width: 100%">
                            <el-table-column prop="name" label="课件名称" />
                            <el-table-column prop="type" label="类型" />
                            <el-table-column label="大小">
                                <template #default="scope">
                                    {{ scope.row.size }} KB
                                    <!-- 直接在值后面添加 KB 单位 -->
                                </template>
                            </el-table-column>
                            <el-table-column label="操作">
                                <template #default="scope">
                                    <el-button type="text" @click="downloadFile(scope.row)"> 下载 </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>

    <div v-else>
        <p>课程未找到。</p>
    </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { ref, onMounted, defineComponent } from 'vue'
import axios from 'axios'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Star, StarFilled } from '@element-plus/icons-vue'

const userStore = useUserStore()
const { isLoggedIn } = storeToRefs(userStore)

const route = useRoute()
const router = useRouter()
const course = ref(null)
const activeTab = ref('intro')
const currentSlide = ref(0)
const courseId = parseInt(route.query.id)
const comments = ref([])
const loading = ref(false)
const error = ref(null)
const courseChapters = ref([])
const courseMaterials = ref([])
const isEnrolled = ref(false)
const enrollLoading = ref(false)
const isFavorite = ref(false)
const favoriteLoading = ref(false)

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i])
}

// 格式化日期
const formatDate = (dateString) => {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    }
    return new Date(dateString).toLocaleDateString('zh-CN', options)
}

// 下载文件
const downloadFile = (material) => {
    if (material.url) {
        window.open(material.url, '_blank')
    } else {
        ElMessage.warning('该文件暂无下载链接')
    }
}

// 获取课程基本信息
const fetchCourseData = async () => {
    loading.value = true
    try {
        const response = await axios.get(`http://localhost:8000/api/courses/${courseId}/`)
        if (response.data) {
            course.value = response.data
            // 获取课程章节数据
            await fetchCourseChapters()
            // 获取课程课件数据
            await fetchCourseMaterials()
            // 获取课程评论数据
            await fetchCourseCommentData()
        } else {
            error.value = '获取课程数据失败'
            ElMessage.error(error.value)
        }
    } catch (err) {
        console.error('获取课程数据出错:', err)
        error.value = err.message || '网络错误，请稍后重试'
        ElMessage.error(error.value)
    } finally {
        loading.value = false
    }
}

// 获取课程章节数据
const fetchCourseChapters = async () => {
    try {
        const response = await axios.get(`http://localhost:8000/api/courses/${courseId}/chapters/`)
        if (response.data && response.data.success) {
            courseChapters.value = response.data.data
            console.log('获取到课程章节:', courseChapters.value)
        }
    } catch (err) {
        console.error('获取课程章节出错:', err)
    }
}

// 从后端获取课程评论数据
const fetchCourseCommentData = async () => {
    if (!isLoggedIn.value) return

    loading.value = true
    try {
        const response = await axios.get(
            `http://localhost:8000/api/student/courses/${courseId}/study/`,
            {
                headers: {
                    Authorization: `Bearer ${userStore.token}`,
                },
            },
        )

        if (response.data.success) {
            const data = response.data.data
            comments.value = data.comments || []
            console.log('获取到课程评论数据:', comments.value)
        } else {
            error.value = response.data.message || '获取课程评论数据失败'
            ElMessage.error(error.value)
        }
    } catch (err) {
        console.error('获取课程评论数据出错:', err)
        error.value = err.message || '网络错误，请稍后重试'
        ElMessage.error(error.value)
    } finally {
        loading.value = false
    }
}

// 获取课件资料
const fetchCourseMaterials = async () => {
    if (!isLoggedIn.value) return

    try {
        let allMaterials = []

        for (const chapter of courseChapters.value) {
            if (chapter.id) {
                const response = await axios.get(
                    `http://localhost:8000/api/chapters/${chapter.id}/materials/`,
                    {
                        headers: {
                            Authorization: `Bearer ${userStore.token}`,
                        },
                    },
                )

                if (response.data.success && response.data.data) {
                    const materials = response.data.data.map((material) => ({
                        id: material.id,
                        name: material.title,
                        type: material.type,
                        size: formatFileSize(material.size || 0),
                        chapter: chapter.title,
                        url: material.url,
                    }))

                    allMaterials = [...allMaterials, ...materials]
                }
            }
        }

        courseMaterials.value = allMaterials
        console.log('获取到课件资料:', courseMaterials.value)
    } catch (err) {
        console.error('获取课件资料出错:', err)
        ElMessage.error('获取课件资料失败，请稍后重试')
    }
}

const goToExam = (courseId) => {
    router.push({
        path: '/exam',
        query: { courseId },
    })
}

// 检查用户是否已注册课程
const checkEnrollmentStatus = async () => {
    if (!isLoggedIn.value || !userStore.token) {
        return
    }

    try {
        const response = await axios.get(`http://localhost:8000/api/student/courses/`, {
            headers: {
                Authorization: `Bearer ${userStore.token}`,
            },
            params: {
                username: userStore.username,
            },
        })

        if (response.data.success && response.data.data) {
            // 查找当前课程是否在已注册课程列表中
            isEnrolled.value = response.data.data.some((course) => course.id === courseId)
            console.log('用户是否已注册此课程:', isEnrolled.value)
        }
    } catch (err) {
        console.error('检查课程注册状态出错:', err)
    }
}

// 处理课程注册
const handleEnrollCourse = async () => {
    // 如果用户已注册课程，则跳转到学习页面
    if (isEnrolled.value) {
        router.push(`/study?course_id=${courseId}`)
        return
    }

    // 如果用户未登录，则提示登录
    if (!isLoggedIn.value) {
        ElMessage.warning('请先登录')
        router.push({
            path: '/login',
            query: { redirect: router.currentRoute.value.fullPath },
        })
        return
    }

    enrollLoading.value = true
    console.log(userStore.username)
    try {
        const response = await axios.post(
            `http://localhost:8000/api/student/courses/${courseId}/enroll/`,
            {
                username: userStore.username, // 确保发送用户名
            },
            {
                headers: {
                    Authorization: `Bearer ${userStore.token}`,
                },
            },
        )

        if (response.data.success) {
            ElMessage.success('课程注册成功!')
            isEnrolled.value = true
        }
    } catch (err) {
        console.error('注册课程出错:', err)
        ElMessage.error(err.response?.data?.message || '注册课程失败，请稍后重试')
    } finally {
        enrollLoading.value = false
    }
}

// 检查课程是否已收藏
const checkFavoriteStatus = async () => {
    if (!isLoggedIn.value || !userStore.token) {
        return
    }

    try {
        const response = await axios.get(`http://localhost:8000/api/favorites/`, {
            headers: {
                Authorization: `Bearer ${userStore.token}`,
            },
            params: {
                username: userStore.username,
            },
        })

        if (response.data.success && response.data.data) {
            // 查找当前课程是否在已收藏课程列表中
            isFavorite.value = response.data.data.some((item) => item.course.id === courseId)
            console.log('用户是否已收藏此课程:', isFavorite.value)
        }
    } catch (err) {
        console.error('检查课程收藏状态出错:', err)
    }
}

// 收藏/取消收藏课程
const toggleFavorite = async () => {
    // 如果用户未登录，则提示登录
    if (!isLoggedIn.value) {
        ElMessage.warning('请先登录')
        router.push({
            path: '/login',
            query: { redirect: router.currentRoute.value.fullPath },
        })
        return
    }

    favoriteLoading.value = true
    try {
        let response
        if (isFavorite.value) {
            // 取消收藏
            response = await axios.delete(`http://localhost:8000/api/courses/${courseId}/favorite/`, {
                headers: {
                    Authorization: `Bearer ${userStore.token}`,
                },
                params: {
                    username: userStore.username,
                },
            })
        } else {
            // 添加收藏
            response = await axios.post(
                `http://localhost:8000/api/courses/${courseId}/favorite/`,
                {
                    username: userStore.username,
                },
                {
                    headers: {
                        Authorization: `Bearer ${userStore.token}`,
                    },
                },
            )
        }

        if (response.data.success) {
            isFavorite.value = !isFavorite.value
            ElMessage.success(isFavorite.value ? '收藏成功!' : '已取消收藏')

            // 更新收藏数量
            if (course.value && course.value.data) {
                course.value.data.favorite_count = isFavorite.value
                    ? (course.value.data.favorite_count || 0) + 1
                    : Math.max((course.value.data.favorite_count || 0) - 1, 0)
            }
        } else {
            ElMessage.error(response.data.message || (isFavorite.value ? '取消收藏失败' : '收藏失败'))
        }
    } catch (err) {
        console.error('收藏/取消收藏课程出错:', err)
        ElMessage.error(err.response?.data?.message || '操作失败，请稍后重试')
    } finally {
        favoriteLoading.value = false
    }
}

onMounted(async () => {
    await fetchCourseData()
    await checkEnrollmentStatus()
    await checkFavoriteStatus()
})
</script>

<style scoped>
.course-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.course-info-card {
    margin-bottom: 24px;
}

.course-info-wrapper {
    display: flex;
    gap: 30px;
}

.course-image {
    flex: 0 0 400px;
}

.carousel-img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    border-radius: 4px;
}

.course-content {
    flex: 1;
}

.course-title {
    font-size: 28px;
    margin: 0 0 12px 0;
    color: #333;
}

.instructor {
    display: block;
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
}

.course-description {
    line-height: 1.6;
    color: #666;
    margin-bottom: 24px;
}

.course-stats {
    margin: 24px 0;
    display: flex;
    gap: 20px;
    color: #888;
}

.course-stats i {
    margin-right: 5px;
}

.course-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
}

.tab-card {
    margin-top: 24px;
    padding: 0 20px;
}

/* 自定义选项卡样式 */
:deep(.custom-tabs .el-tabs__item) {
    font-size: 16px;
    padding: 0 24px;
    height: 50px;
    line-height: 50px;
}

:deep(.custom-tabs .el-tabs__active-bar) {
    background-color: #409eff;
    height: 3px;
}

:deep(.custom-tabs .el-tabs__item.is-active) {
    color: #409eff;
    font-weight: 500;
}

.tab-content {
    padding: 20px 0;
    min-height: 300px;
}

@media (max-width: 768px) {
    .course-info-wrapper {
        flex-direction: column;
    }

    .course-image {
        flex: 1 1 auto;
    }
}

.comments-section {
    margin-top: 20px;
}

.comment-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.comment-content {
    font-size: 15px;
    line-height: 1.6;
}

.no-comments {
    text-align: center;
    padding: 30px;
    color: #999;
}
</style>
