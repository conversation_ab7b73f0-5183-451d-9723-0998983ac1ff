# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class AdminInfo(models.Model):
    user = models.OneToOneField('User', models.DO_NOTHING, primary_key=True)
    level = models.IntegerField(blank=True, null=True, db_comment='管理级别')

    class Meta:
        managed = False
        db_table = 'admin_info'
        db_table_comment = '管理员信息表'


class AppCourse(models.Model):
    id = models.BigAutoField(primary_key=True)
    teacher_id = models.CharField(max_length=50)
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    cover_url = models.CharField(max_length=200, blank=True, null=True)
    status = models.CharField(max_length=20)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    category = models.ForeignKey('AppCoursecategory', models.DO_NOTHING)
    enroll_count = models.IntegerField()
    favorite_count = models.IntegerField()
    recommend_level = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'app_course'


class AppCoursecarousel(models.Model):
    id = models.BigAutoField(primary_key=True)
    image_url = models.CharField(max_length=200)
    sort_order = models.IntegerField()
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'app_coursecarousel'


class AppCoursecategory(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=50)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    status = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'app_coursecategory'


class AppCoursechapter(models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    sort_order = models.IntegerField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_coursechapter'


class AppCoursecomment(models.Model):
    id = models.BigAutoField(primary_key=True)
    content = models.TextField()
    likes = models.IntegerField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)
    parent = models.ForeignKey('self', models.DO_NOTHING, blank=True, null=True)
    user = models.ForeignKey('AppUser', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_coursecomment'


class AppCoursefavorite(models.Model):
    id = models.BigAutoField(primary_key=True)
    create_time = models.DateTimeField()
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)
    user = models.ForeignKey('AppUser', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_coursefavorite'
        unique_together = (('user', 'course'),)


class AppCoursematerial(models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=50)
    type = models.CharField(max_length=10)
    url = models.CharField(max_length=255)
    duration = models.IntegerField()
    size = models.BigIntegerField()
    sort_order = models.IntegerField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    chapter = models.ForeignKey(AppCoursechapter, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_coursematerial'


class AppExam(models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    duration_minutes = models.IntegerField()
    pass_score = models.SmallIntegerField()
    total_score = models.IntegerField()
    status = models.SmallIntegerField()
    shuffle_questions = models.IntegerField()
    shuffle_options = models.IntegerField()
    allow_break = models.IntegerField()
    break_affect_time = models.IntegerField()
    allow_multiple = models.IntegerField()
    show_analysis = models.IntegerField()
    add_to_wrong = models.IntegerField()
    show_score_type = models.SmallIntegerField()
    anti_cheat_options = models.JSONField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    teacher = models.ForeignKey('AppUser', models.DO_NOTHING, to_field='username')
    paper = models.ForeignKey('AppExampaper', models.DO_NOTHING)
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_exam'


class AppExampaper(models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=20)
    total_score = models.IntegerField()
    difficulty = models.FloatField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    teacher = models.ForeignKey('AppUser', models.DO_NOTHING, to_field='username')

    class Meta:
        managed = False
        db_table = 'app_exampaper'


class AppHomework(models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100)
    content = models.TextField()
    attachment_url = models.CharField(max_length=255, blank=True, null=True)
    deadline = models.DateTimeField()
    total_score = models.SmallIntegerField()
    status = models.SmallIntegerField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)
    teacher = models.ForeignKey('AppUser', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_homework'


class AppNotification(models.Model):
    notification_text = models.TextField()
    publish_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'app_notification'


class AppPaperquestion(models.Model):
    id = models.BigAutoField(primary_key=True)
    order_num = models.IntegerField()
    score = models.SmallIntegerField()
    paper = models.ForeignKey(AppExampaper, models.DO_NOTHING)
    question = models.ForeignKey('AppQuestion', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_paperquestion'
        unique_together = (('paper', 'question'),)


class AppQuestion(models.Model):
    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=20)
    content = models.TextField()
    content_attachment = models.CharField(max_length=200, blank=True, null=True)
    options = models.JSONField()
    answer = models.TextField()
    analysis = models.TextField(blank=True, null=True)
    analysis_attachment = models.CharField(max_length=200, blank=True, null=True)
    difficulty = models.SmallIntegerField()
    score = models.SmallIntegerField()
    blank_count = models.SmallIntegerField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    bank = models.ForeignKey('AppQuestionbank', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_question'


class AppQuestionbank(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    visibility = models.SmallIntegerField()
    tags = models.JSONField()
    question_count = models.IntegerField()
    use_count = models.IntegerField()
    accuracy_rate = models.FloatField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    teacher_id = models.CharField(max_length=150)

    class Meta:
        managed = False
        db_table = 'app_questionbank'


class AppStudentenrollcourse(models.Model):
    id = models.BigAutoField(primary_key=True)
    enroll_time = models.DateTimeField()
    progress = models.IntegerField()
    status = models.IntegerField()
    course = models.ForeignKey(AppCourse, models.DO_NOTHING)
    student = models.ForeignKey('AppUser', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'app_studentenrollcourse'
        unique_together = (('student', 'course'),)


class AppStudentinfo(models.Model):
    user = models.OneToOneField('AppUser', models.DO_NOTHING, primary_key=True)
    nickname = models.CharField(max_length=50, blank=True, null=True)
    signature = models.CharField(max_length=200, blank=True, null=True)
    avatar = models.CharField(max_length=200, blank=True, null=True)
    gender = models.CharField(max_length=10, blank=True, null=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'app_studentinfo'


class AppUser(models.Model):
    id = models.BigAutoField(primary_key=True)
    last_login = models.DateTimeField(blank=True, null=True)
    username = models.CharField(unique=True, max_length=50)
    password = models.CharField(max_length=100)
    role = models.CharField(max_length=10)
    create_time = models.DateTimeField()
    status = models.SmallIntegerField()
    update_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'app_user'


class AuthGroup(models.Model):
    name = models.CharField(unique=True, max_length=150)

    class Meta:
        managed = False
        db_table = 'auth_group'


class AuthGroupPermissions(models.Model):
    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey(AuthGroup, models.DO_NOTHING)
    permission = models.ForeignKey('AuthPermission', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'auth_group_permissions'
        unique_together = (('group', 'permission'),)


class AuthPermission(models.Model):
    name = models.CharField(max_length=255)
    content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING)
    codename = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'auth_permission'
        unique_together = (('content_type', 'codename'),)


class BankCourseRelation(models.Model):
    pk = models.CompositePrimaryKey('bank_id', 'course_id')
    bank = models.ForeignKey('QuestionBank', models.DO_NOTHING, db_comment='题库ID')
    course = models.ForeignKey('Course', models.DO_NOTHING, db_comment='课程ID')

    class Meta:
        managed = False
        db_table = 'bank_course_relation'
        db_table_comment = '题库课程关联表'


class CommentLike(models.Model):
    user = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    comment = models.ForeignKey('CourseComment', models.DO_NOTHING, db_comment='评论ID')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='点赞时间')

    class Meta:
        managed = False
        db_table = 'comment_like'
        unique_together = (('user', 'comment'),)
        db_table_comment = '评论点赞表'


class Course(models.Model):
    teacher = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    category = models.ForeignKey('CourseCategory', models.DO_NOTHING, db_comment='分类ID')
    title = models.CharField(max_length=100, db_comment='课程标题')
    description = models.TextField(blank=True, null=True, db_comment='课程简介')
    cover_url = models.CharField(max_length=200, blank=True, null=True, db_comment='封面图URL')
    status = models.CharField(max_length=9, db_comment='课程状态')
    recommend_level = models.IntegerField(blank=True, null=True, db_comment='推荐等级')
    favorite_count = models.IntegerField(blank=True, null=True, db_comment='收藏人数')
    enroll_count = models.IntegerField(blank=True, null=True, db_comment='注册人数')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'course'
        db_table_comment = '课程表'


class CourseCarousel(models.Model):
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    image_url = models.CharField(max_length=200, db_comment='图片URL')
    sort_order = models.IntegerField(blank=True, null=True, db_comment='排序')

    class Meta:
        managed = False
        db_table = 'course_carousel'
        db_table_comment = '课程轮播图表'


class CourseCategory(models.Model):
    name = models.CharField(unique=True, max_length=50, db_comment='分类名称')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=启用，0=禁用')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'course_category'
        db_table_comment = '课程分类表'


class CourseChapter(models.Model):
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    title = models.CharField(max_length=100, db_comment='章节标题')
    description = models.TextField(blank=True, null=True, db_comment='章节描述')
    sort_order = models.IntegerField(blank=True, null=True, db_comment='排序')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'course_chapter'
        db_table_comment = '课程章节表'


class CourseComment(models.Model):
    user = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    parent_id = models.IntegerField(blank=True, null=True, db_comment='父评论ID（0表示顶级）')
    content = models.TextField(db_comment='评论内容')
    likes = models.IntegerField(blank=True, null=True, db_comment='点赞数')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=正常，0=已删除')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'course_comment'
        db_table_comment = '课程评论表'


class CourseMaterial(models.Model):
    chapter = models.ForeignKey(CourseChapter, models.DO_NOTHING, db_comment='章节ID')
    title = models.CharField(max_length=50, db_comment='课件标题')
    type = models.CharField(max_length=5, db_comment='类型')
    url = models.CharField(max_length=200, db_comment='资源地址')
    duration = models.IntegerField(blank=True, null=True, db_comment='时长（秒，视频/音频用）')
    size = models.IntegerField(blank=True, null=True, db_comment='文件大小（字节）')
    sort_order = models.IntegerField(blank=True, null=True, db_comment='排序')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'course_material'
        db_table_comment = '课程课件表'


class DjangoAdminLog(models.Model):
    action_time = models.DateTimeField()
    object_id = models.TextField(blank=True, null=True)
    object_repr = models.CharField(max_length=200)
    action_flag = models.PositiveSmallIntegerField()
    change_message = models.TextField()
    content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING, blank=True, null=True)
    user = models.ForeignKey(AppUser, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'django_admin_log'


class DjangoContentType(models.Model):
    app_label = models.CharField(max_length=100)
    model = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'django_content_type'
        unique_together = (('app_label', 'model'),)


class DjangoMigrations(models.Model):
    id = models.BigAutoField(primary_key=True)
    app = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    applied = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_migrations'


class DjangoSession(models.Model):
    session_key = models.CharField(primary_key=True, max_length=40)
    session_data = models.TextField()
    expire_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_session'


class Exam(models.Model):
    teacher = models.ForeignKey('User', models.DO_NOTHING, db_comment='教师ID')
    paper = models.ForeignKey('ExamPaper', models.DO_NOTHING, db_comment='关联试卷ID')
    title = models.CharField(max_length=100, db_comment='考试名称')
    description = models.TextField(blank=True, null=True, db_comment='考试描述')
    start_time = models.DateTimeField(db_comment='开始时间')
    end_time = models.DateTimeField(db_comment='结束时间')
    duration_minutes = models.IntegerField(blank=True, null=True, db_comment='考试时长（分钟）')
    pass_score = models.IntegerField(blank=True, null=True, db_comment='及格分')
    total_score = models.IntegerField(blank=True, null=True, db_comment='总分')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=未开始，2=进行中，3=已结束')
    shuffle_questions = models.IntegerField(blank=True, null=True, db_comment='是否随机试题顺序：0=否，1=是')
    shuffle_options = models.IntegerField(blank=True, null=True, db_comment='是否随机选项顺序：0=否，1=是')
    allow_break = models.IntegerField(blank=True, null=True, db_comment='是否允许中断：0=否，1=是')
    break_affect_time = models.IntegerField(blank=True, null=True, db_comment='中断是否计时：0=否，1=是')
    allow_multiple = models.IntegerField(blank=True, null=True, db_comment='允许多次考试：0=否，1=是')
    show_analysis = models.IntegerField(blank=True, null=True, db_comment='是否显示解析：0=否，1=是')
    add_to_wrong = models.IntegerField(blank=True, null=True, db_comment='错题入错题本：0=否，1=是')
    show_score_type = models.IntegerField(blank=True, null=True, db_comment='成绩显示方式：1=立即显示，2=考试后，3=不显示')
    anti_cheat_options = models.JSONField(blank=True, null=True, db_comment='防作弊设置（JSON格式）')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'exam'
        db_table_comment = '考试表'


class ExamPaper(models.Model):
    teacher = models.ForeignKey('User', models.DO_NOTHING, db_comment='创建教师ID')
    title = models.CharField(max_length=100, db_comment='试卷名称')
    description = models.TextField(blank=True, null=True, db_comment='试卷描述')
    total_score = models.IntegerField(blank=True, null=True, db_comment='总分')
    difficulty = models.IntegerField(blank=True, null=True, db_comment='难度：1=简单，2=中等，3=困难')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'exam_paper'
        db_table_comment = '试卷表'


class Homework(models.Model):
    teacher = models.ForeignKey('User', models.DO_NOTHING, db_comment='教师ID')
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    title = models.CharField(max_length=100, db_comment='作业标题')
    content = models.TextField(db_comment='作业内容')
    attachment_url = models.CharField(max_length=200, blank=True, null=True, db_comment='附件URL')
    deadline = models.DateTimeField(db_comment='截止时间')
    total_score = models.IntegerField(blank=True, null=True, db_comment='总分')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=未发布，2=已发布，3=已截止')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'homework'
        db_table_comment = '作业表'


class OperationLog(models.Model):
    user = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    module = models.CharField(max_length=50, db_comment='操作模块')
    action = models.CharField(max_length=50, db_comment='操作类型')
    content = models.TextField(blank=True, null=True, db_comment='操作内容')
    ip = models.CharField(max_length=50, blank=True, null=True, db_comment='操作IP')
    user_agent = models.CharField(max_length=255, blank=True, null=True, db_comment='用户代理')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='操作时间')

    class Meta:
        managed = False
        db_table = 'operation_log'
        db_table_comment = '操作日志表'


class PaperQuestion(models.Model):
    pk = models.CompositePrimaryKey('paper_id', 'question_id')
    paper = models.ForeignKey(ExamPaper, models.DO_NOTHING, db_comment='试卷ID')
    question = models.ForeignKey('Question', models.DO_NOTHING, db_comment='试题ID')
    order_num = models.IntegerField(blank=True, null=True, db_comment='题目顺序')
    score = models.IntegerField(db_comment='题目分值')

    class Meta:
        managed = False
        db_table = 'paper_question'
        db_table_comment = '试卷试题关联表'


class Question(models.Model):
    bank = models.ForeignKey('QuestionBank', models.DO_NOTHING, db_comment='题库ID')
    type = models.CharField(max_length=15, db_comment='题型')
    content = models.TextField(db_comment='题目内容')
    content_attachment = models.CharField(max_length=200, blank=True, null=True, db_comment='题干附件URL')
    options = models.TextField(blank=True, null=True, db_comment='选项（JSON格式）')
    answer = models.TextField(db_comment='答案（JSON或文本）')
    analysis = models.TextField(blank=True, null=True, db_comment='解析')
    analysis_attachment = models.CharField(max_length=200, blank=True, null=True, db_comment='解析附件URL')
    difficulty = models.IntegerField(blank=True, null=True, db_comment='难度：1=简单，2=中等，3=困难')
    score = models.IntegerField(blank=True, null=True, db_comment='默认分值')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'question'
        db_table_comment = '试题表'


class QuestionBank(models.Model):
    teacher = models.ForeignKey('User', models.DO_NOTHING, db_comment='教师ID')
    name = models.CharField(max_length=100, db_comment='题库名称')
    description = models.TextField(blank=True, null=True, db_comment='题库描述')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=启用，0=禁用')
    is_public = models.IntegerField(blank=True, null=True, db_comment='是否公开：1=公开，0=保密')
    tags = models.TextField(blank=True, null=True, db_comment='标签列表（JSON格式）')
    question_count = models.IntegerField(blank=True, null=True, db_comment='试题数量（自动更新）')
    use_count = models.IntegerField(blank=True, null=True, db_comment='试卷使用次数')
    accuracy_rate = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, db_comment='平均正确率')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='最后更新时间')

    class Meta:
        managed = False
        db_table = 'question_bank'
        db_table_comment = '题库表'


class StudentEnrollCourse(models.Model):
    pk = models.CompositePrimaryKey('student_id', 'course_id')
    student = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    enroll_time = models.DateTimeField(blank=True, null=True, db_comment='注册时间')
    progress = models.IntegerField(blank=True, null=True, db_comment='学习进度(%)')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=在学，2=已结课，3=已退课')

    class Meta:
        managed = False
        db_table = 'student_enroll_course'
        db_table_comment = '学生注册课程表'


class StudentExam(models.Model):
    pk = models.CompositePrimaryKey('student_id', 'exam_id')
    student = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    exam = models.ForeignKey(Exam, models.DO_NOTHING, db_comment='考试ID')
    start_time = models.DateTimeField(blank=True, null=True, db_comment='开始时间')
    submit_time = models.DateTimeField(blank=True, null=True, db_comment='交卷时间')
    score = models.IntegerField(blank=True, null=True, db_comment='得分')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=未开始，2=进行中，3=已交卷，4=已批改')
    answer_json = models.TextField(blank=True, null=True, db_comment='答题内容（JSON格式）')
    break_times = models.IntegerField(blank=True, null=True, db_comment='中断次数')
    break_duration = models.IntegerField(blank=True, null=True, db_comment='中断时长（秒）')

    class Meta:
        managed = False
        db_table = 'student_exam'
        db_table_comment = '学生考试记录表'


class StudentFavoriteCourse(models.Model):
    pk = models.CompositePrimaryKey('student_id', 'course_id')
    student = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='收藏时间')

    class Meta:
        managed = False
        db_table = 'student_favorite_course'
        db_table_comment = '学生收藏课程表'


class StudentHomework(models.Model):
    pk = models.CompositePrimaryKey('student_id', 'homework_id')
    student = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    homework = models.ForeignKey(Homework, models.DO_NOTHING, db_comment='作业ID')
    content = models.TextField(blank=True, null=True, db_comment='作业内容')
    attachment_url = models.CharField(max_length=200, blank=True, null=True, db_comment='附件URL')
    submit_time = models.DateTimeField(blank=True, null=True, db_comment='提交时间')
    status = models.CharField(max_length=7, blank=True, null=True, db_comment='状态')
    score = models.IntegerField(blank=True, null=True, db_comment='得分')
    feedback = models.TextField(blank=True, null=True, db_comment='教师反馈')
    feedback_time = models.DateTimeField(blank=True, null=True, db_comment='反馈时间')

    class Meta:
        managed = False
        db_table = 'student_homework'
        db_table_comment = '学生作业提交表'


class StudentInfo(models.Model):
    user = models.OneToOneField('User', models.DO_NOTHING, primary_key=True)
    nickname = models.CharField(max_length=50, blank=True, null=True, db_comment='昵称')
    signature = models.CharField(max_length=200, blank=True, null=True, db_comment='签名')
    avatar = models.CharField(max_length=200, blank=True, null=True, db_comment='头像地址')
    gender = models.CharField(max_length=10, blank=True, null=True, db_comment='性别')

    class Meta:
        managed = False
        db_table = 'student_info'
        db_table_comment = '学生信息表'


class StudyNote(models.Model):
    student = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    material = models.ForeignKey(CourseMaterial, models.DO_NOTHING, blank=True, null=True, db_comment='课件ID')
    title = models.CharField(max_length=100, db_comment='笔记标题')
    content = models.TextField(blank=True, null=True, db_comment='笔记内容')
    attachment = models.CharField(max_length=200, blank=True, null=True, db_comment='附件地址')
    is_public = models.IntegerField(blank=True, null=True, db_comment='是否公开：0=私有，1=公开')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'study_note'
        db_table_comment = '学习笔记表'


class SystemNotice(models.Model):
    title = models.CharField(max_length=100, db_comment='公告标题')
    content = models.TextField(db_comment='公告内容')
    publish_time = models.DateTimeField(blank=True, null=True, db_comment='发布时间')
    expire_time = models.DateTimeField(blank=True, null=True, db_comment='过期时间（NULL表示永不过期）')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=已发布，0=已下架')
    publisher = models.ForeignKey('User', models.DO_NOTHING, to_field='username')

    class Meta:
        managed = False
        db_table = 'system_notice'
        db_table_comment = '系统公告表'


class TeacherCreateBank(models.Model):
    pk = models.CompositePrimaryKey('teacher_id', 'bank_id')
    teacher = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    bank = models.ForeignKey(QuestionBank, models.DO_NOTHING, db_comment='题库ID')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')

    class Meta:
        managed = False
        db_table = 'teacher_create_bank'
        db_table_comment = '教师创建题库表'


class TeacherCreateCourse(models.Model):
    pk = models.CompositePrimaryKey('teacher_id', 'course_id')
    teacher = models.ForeignKey('User', models.DO_NOTHING, to_field='username')
    course = models.ForeignKey(Course, models.DO_NOTHING, db_comment='课程ID')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')

    class Meta:
        managed = False
        db_table = 'teacher_create_course'
        db_table_comment = '教师创建课程表'


class TeacherInfo(models.Model):
    user = models.OneToOneField('User', models.DO_NOTHING, primary_key=True)
    introduction = models.TextField(blank=True, null=True, db_comment='教师简介')
    gender = models.CharField(max_length=10, blank=True, null=True, db_comment='性别')
    hire_date = models.DateField(blank=True, null=True, db_comment='入职时间')
    department = models.CharField(max_length=100, blank=True, null=True, db_comment='所属部门')

    class Meta:
        managed = False
        db_table = 'teacher_info'
        db_table_comment = '教师信息表'


class User(models.Model):
    username = models.CharField(unique=True, max_length=50, db_comment='账号（学号）')
    password = models.CharField(max_length=100, db_comment='密码（加密存储）')
    role = models.CharField(max_length=7, db_comment='用户角色')
    status = models.IntegerField(blank=True, null=True, db_comment='状态：1=正常，0=禁用')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    last_login = models.DateTimeField(blank=True, null=True, db_comment='最后登录时间')
    is_superuser = models.IntegerField(blank=True, null=True, db_comment='是否为超级用户')
    is_active = models.IntegerField(blank=True, null=True, db_comment='是否活跃')
    is_staff = models.IntegerField(blank=True, null=True, db_comment='是否为成员')

    class Meta:
        managed = False
        db_table = 'user'
        db_table_comment = '用户表'
