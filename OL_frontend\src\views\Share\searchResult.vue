<template>
    <div class="search-results-page">
        <div class="search-header">
            <h2>搜索结果</h2>
            <p v-if="searchKeyword">搜索关键词: "{{ searchKeyword }}"</p>
        </div>

        <div v-if="loading" class="loading-container">
            <el-icon class="is-loading" :size="30">
                <Loading />
            </el-icon>
            <span>搜索中...</span>
        </div>

        <div v-else>
            <div v-if="hasResults" class="results-container">
                <el-row :gutter="20">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="course in results" :key="course.id">
                        <courseCard :course="course" />
                    </el-col>
                </el-row>
            </div>

            <div v-else class="no-results">
                <el-empty description="未搜索到相关课程 TT">
                    <el-button type="primary" @click="goBack">返回首页</el-button>
                </el-empty>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Loading } from '@element-plus/icons-vue'
import courseCard from '@/HomePage/courseCard.vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 搜索相关状态
const searchKeyword = ref(route.query.q || '')
const results = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(8)
const totalResults = ref(0)

// 计算属性：是否有搜索结果
const hasResults = computed(() => results.value.length > 0)

// 执行搜索
const performSearch = async () => {
    if (!searchKeyword.value.trim()) {
        results.value = []
        totalResults.value = 0
        return
    }

    loading.value = true
    try {
        const response = await axios.get('/api/courses/search', {
            params: {
                keyword: searchKeyword.value
            }
        })
        results.value = response.data.map(c => ({
            id: c.id,
            title: c.title,
            description: c.description,
            coverUrl: c.cover_url,
            updateTime: c.update_time,
            categoryId: c.category.id,
            students: c.enroll_count,
            likes: c.favorite_count,
            isRecommended: c.recommend_level >= 1,
            isPopular: c.enroll_count >= 1,
            isNew: Date.now() - new Date(c.update_time).getTime() < 30 * 24 * 60 * 60 * 1000
        }))
        console.log('搜索结果:', results.value)
        totalResults.value = response.data.count
    } catch (error) {
        ElMessage.error('搜索失败，请稍后重试')
        console.error(error)
        results.value = []
        totalResults.value = 0
    } finally {
        loading.value = false
    }
}

// 初始化
onMounted(() => {
    if (route.query.q) {
        searchKeyword.value = route.query.q
        performSearch()
    }
})

// 监听路由变化
watch(() => route.query.q, (newQuery) => {
    if (newQuery !== searchKeyword.value) {
        searchKeyword.value = newQuery || ''
        currentPage.value = 1
        performSearch()
    }
})

// 分页处理
const handleSizeChange = (val) => {
    pageSize.value = val
    performSearch()
}

const handleCurrentChange = (val) => {
    currentPage.value = val
    performSearch()
}

const goBack = () => {
    router.push('/')
}
</script>

<style scoped>
.search-results-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.search-header {
    text-align: center;
    margin-bottom: 30px;
}

.search-header h2 {
    font-size: 24px;
    margin-bottom: 10px;
}

.search-header p {
    color: #666;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
}

.loading-container span {
    margin-top: 10px;
}

.no-results {
    text-align: center;
    margin-top: 50px;
}

.results-container {
    margin-top: 20px;
}
</style>