<template>
  <div class="course-registration-page">
    <!-- <Header /> -->
    <div class="page-header">
      <h1 class="page-title">课程注册</h1>
      <div class="header-actions">
        <el-input placeholder="搜索课程" v-model="searchQuery" class="search-input" :prefix-icon="Search" clearable />
        <el-select v-model="categoryFilter" placeholder="课程分类" clearable>
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>

    <!-- 未注册课程列表 -->
    <div class="section-title">
      <h2>未注册课程:</h2>
    </div>
    
    <div class="courses-container">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-for="course in filteredCourses" :key="course.id">
          <div class="course-card">
            <div class="course-image">
              <div class="course-icon">
                <el-icon :size="80">
                  <component :is="getCourseIcon(course.category)"></component>
                </el-icon>
              </div>
              <div class="course-actions">
                <el-button type="primary" @click="enrollCourse(course)">注册</el-button>
                <el-button type="default" @click="toggleFavorite(course)">
                  <el-icon>
                    <Star v-if="course.isFavorite" />
                    <StarFilled v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <div class="course-info">
              <h3 class="course-title">{{ course.title }}</h3>
              <p class="course-description">{{ course.description }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="totalCourses"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 课程注册流程对话框 -->
    <el-dialog
      v-model="showEnrollDialog"
      :title="selectedCourse ? selectedCourse.title + ' - 课程注册' : '课程注册'"
      width="700px"
    >
      <div v-if="selectedCourse" class="enrollment-process">
        <!-- 步骤指示器 -->
        <el-steps :active="activeStep" finish-status="success" align-center process-status="process">
          <el-step title="基本信息" />
          <el-step title="选择课程班级" />
          <el-step title="确认" />
        </el-steps>

        <!-- 步骤内容 -->
        <div class="step-content">
          <!-- 步骤1: 基本信息 -->
          <div v-if="activeStep === 0" class="step-form">
            <div class="step-title">课程XXXXXX</div>
            <el-form :model="enrollForm" ref="enrollFormRef" :rules="enrollRules" label-width="100px">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="enrollForm.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="学号" prop="studentId">
                <el-input v-model="enrollForm.studentId" placeholder="请输入学号" />
              </el-form-item>
              <!-- <el-form-item label="班级" prop="class">
                <el-select v-model="enrollForm.class" placeholder="请选择班级" class="full-width">
                  <el-option label="班级1" value="class1" />
                  <el-option label="班级2" value="class2" />
                  <el-option label="班级3" value="class3" />
                </el-select>
              </el-form-item> -->
            </el-form>
          </div>

          <!-- 步骤2: 选择课程班级 -->
          <div v-if="activeStep === 1" class="step-form">
            <div class="step-title">选择课程班级</div>
            <div class="class-selection-tip">选择任意一项</div>
            
            <div class="class-cards-container">
              <el-row :gutter="20">
                <el-col :span="8" v-for="(classItem, index) in classList" :key="index">
                  <div 
                    class="class-card" 
                    :class="{ 
                      'selected': enrollForm.class === classItem.id,
                      'error-border': showClassError && !enrollForm.class
                    }"
                    @click="selectClass(classItem)"
                  >
                    <div class="selected-mark" v-if="enrollForm.class === classItem.id">
                      <el-icon><Check /></el-icon>
                    </div>
                    <div class="class-info">
                      <div class="class-name">班级-{{ classItem.id }}</div>
                      <div class="class-teacher">任课老师-{{ classItem.teacher }}</div>
                      <div class="class-location">上课地点-{{ classItem.location }}</div>
                      <div class="class-time">上课时间-{{ classItem.time }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div v-if="showClassError && !enrollForm.class" class="class-error-tip">
              请选择一个课程班级
            </div>
          </div>

          <!-- 步骤3: 确认 -->
          <div v-if="activeStep === 2" class="step-form confirmation">
            <div class="step-title">确认信息</div>
            
            <div class="confirm-section">
              <div class="confirm-row">
                <div class="confirm-label">姓名:</div>
                <div class="confirm-value">{{ enrollForm.name || 'XXXXXXXXX' }}</div>
              </div>
              
              <div class="confirm-row">
                <div class="confirm-label">学号:</div>
                <div class="confirm-value">{{ enrollForm.studentId || 'XXXXXXXXX' }}</div>
              </div>
              
              <div class="confirm-row">
                <div class="confirm-label">班级:</div>
                <div class="confirm-value">{{ getSelectedClassName() || 'XXXXXXXXX' }}</div>
              </div>
            </div>
            
            <div class="confirm-section">
              <div class="confirm-title">所选课程</div>
              
              <div class="confirm-row">
                <div class="confirm-label">课程序号:</div>
                <div class="confirm-value">{{ selectedCourse?.id || 'XXX' }}</div>
              </div>
              
              <div class="confirm-row">
                <div class="confirm-label">课程班级:</div>
                <div class="confirm-value">{{ getSelectedClassName() || 'XXXX' }}</div>
              </div>
              
              <div class="confirm-row">
                <div class="confirm-label">任课老师:</div>
                <div class="confirm-value">{{ getSelectedClassTeacher() || 'XXXX' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤按钮 -->
        <div class="step-actions">
          <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
          <el-button 
            v-if="activeStep < 2" 
            type="primary" 
            @click="nextStep"
          >
            下一步
          </el-button>
          <el-button 
            v-else 
            type="primary" 
            @click="submitEnrollment"
          >
            确认注册
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Star, StarFilled, 
  Notebook, Monitor, ChatRound, 
  Compass, Cpu, Histogram, Check 
} from '@element-plus/icons-vue'
import Header from '@/components/header.vue'


// 搜索和过滤
const searchQuery = ref('')
const categoryFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(6)

// 课程分类选项
const categoryOptions = [
  { value: 'math', label: '数学' },
  { value: 'computer', label: '计算机' },
  { value: 'language', label: '语言' },
  { value: 'physics', label: '物理' },
  { value: 'chemistry', label: '化学' },
  { value: 'biology', label: '生物' }
]

// 课程数据
const courses = ref([
  {
    id: 1,
    title: '高等数学',
    description: '本课程涵盖微积分、线性代数等高等数学基础知识，适合理工科学生学习。',
    image: 'https://via.placeholder.com/300x200',
    teacher: '张教授',
    credits: 4,
    category: 'math',
    isFavorite: false
  },
  {
    id: 2,
    title: '计算机网络',
    description: '介绍计算机网络基本原理、协议和应用，包括TCP/IP协议栈、网络安全等内容。',
    image: 'https://via.placeholder.com/300x200',
    teacher: '李教授',
    credits: 3,
    category: 'computer',
    isFavorite: false
  },
  {
    id: 3,
    title: '数据结构与算法',
    description: '学习常用数据结构和算法设计，培养算法思维和问题解决能力。',
    image: 'https://via.placeholder.com/300x200',
    teacher: '王教授',
    credits: 4,
    category: 'computer',
    isFavorite: false
  },
  {
    id: 4,
    title: '大学英语',
    description: '提高英语听说读写能力，培养跨文化交际能力和学术英语技能。',
    image: 'https://via.placeholder.com/300x200',
    teacher: '刘教授',
    credits: 3,
    category: 'language',
    isFavorite: false
  },
  {
    id: 5,
    title: '大学物理',
    description: '学习力学、电磁学、热学、光学等物理基础知识，包含实验部分。',
    image: 'https://via.placeholder.com/300x200',
    teacher: '赵教授',
    credits: 4,
    category: 'physics',
    isFavorite: false
  },
  {
    id: 6,
    title: '有机化学',
    description: '学习有机化合物的结构、性质、反应和合成方法，培养实验技能。',
    image: 'https://via.placeholder.com/300x200',
    teacher: '钱教授',
    credits: 3,
    category: 'chemistry',
    isFavorite: false
  }
])

// 注册对话框控制
const showEnrollDialog = ref(false)
const selectedCourse = ref(null)
const activeStep = ref(0)

// 表单引用
const enrollFormRef = ref(null)

// 注册表单
const enrollForm = ref({
  name: '',
  studentId: '',
  class: ''
})

// 表单校验规则
const enrollRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应为2-20个字符', trigger: 'blur' }
  ],
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { pattern: /^\d{8,12}$/, message: '学号必须为8-12位数字', trigger: 'blur' }
  ],
  class: [
    { required: true, message: '请选择班级', trigger: 'change' }
  ]
}

// 计算属性：过滤后的课程
const filteredCourses = computed(() => {
  let result = courses.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(course => 
      course.title.toLowerCase().includes(query) || 
      course.description.toLowerCase().includes(query) ||
      course.teacher.toLowerCase().includes(query)
    )
  }

  // 分类过滤
  if (categoryFilter.value) {
    result = result.filter(course => course.category === categoryFilter.value)
  }

  return result
})

// 计算总课程数
const totalCourses = computed(() => filteredCourses.value.length)

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 切换收藏状态
const toggleFavorite = (course) => {
  course.isFavorite = !course.isFavorite
  ElMessage({
    message: course.isFavorite ? '已添加到收藏' : '已取消收藏',
    type: 'success'
  })
}

// 注册课程
const enrollCourse = (course) => {
  selectedCourse.value = course
  activeStep.value = 0
  resetEnrollForm()
  showEnrollDialog.value = true
}

// 重置注册表单
const resetEnrollForm = () => {
  enrollForm.value = {
    name: '',
    studentId: '',
    class: ''
  }
  // 如果表单引用存在，重置验证状态
  if (enrollFormRef.value) {
    enrollFormRef.value.resetFields()
  }
}

// 下一步
const nextStep = () => {
  if (activeStep.value === 0) {
    // 第一步需要验证表单
    enrollFormRef.value.validate((valid) => {
      if (valid) {
        activeStep.value++
        showClassError.value = false // 重置班级选择错误状态
      } else {
        ElMessage.warning('请完成表单的必填项')
      }
    })
  } else if (activeStep.value === 1) {
    // 第二步验证是否选择了班级
    if (!enrollForm.value.class) {
      showClassError.value = true
      ElMessage.warning('请选择一个课程班级')
      return
    }
    showClassError.value = false
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  activeStep.value--
}

// 获取选择的班级名称
const getSelectedClassName = () => {
  const selectedClass = classList.value.find(item => item.id === enrollForm.value.class)
  return selectedClass ? selectedClass.id : '未选择'
}

// 获取选择的班级教师
const getSelectedClassTeacher = () => {
  const selectedClass = classList.value.find(item => item.id === enrollForm.value.class)
  return selectedClass ? selectedClass.teacher : '未选择'
}

// 提交注册
const submitEnrollment = () => {
  // 最后确认所有信息是否完整
  if (!enrollForm.value.name || !enrollForm.value.studentId || !enrollForm.value.class) {
    ElMessage.warning('信息不完整，请返回填写')
    return
  }
  
  // 提交注册信息
  ElMessage.success(`成功注册课程: ${selectedCourse.value.title}`)
  showEnrollDialog.value = false
  
  // 这里可以添加实际的注册逻辑
  // 例如将课程添加到已注册课程列表中
}

// 获取课程图标
const getCourseIcon = (category) => {
  switch (category) {
    case 'math': return Histogram
    case 'computer': return Monitor
    case 'language': return ChatRound
    case 'physics': return Compass
    case 'chemistry': return Cpu
    case 'biology': return Notebook
    default: return Notebook
  }
}

// 选择班级
const selectClass = (classItem) => {
  enrollForm.value.class = classItem.id
  showClassError.value = false // 选择后清除错误提示
}

// 班级列表
const classList = ref([
  { id: '101', teacher: 'XXXX', location: 'XXX', time: 'XXXXX' },
  { id: '102', teacher: 'XXXX', location: 'XXX', time: 'XXXXX' },
  { id: '103', teacher: 'XXXX', location: 'XXX', time: 'XXXXX' },
  { id: '104', teacher: 'XXXX', location: 'XXX', time: 'XXXXX' },
  { id: '105', teacher: 'XXXX', location: 'XXX', time: 'XXXXX' },
  { id: '106', teacher: 'XXXX', location: 'XXX', time: 'XXXXX' }
])

// 添加班级选择验证逻辑
const showClassError = ref(false)
</script>

<style scoped>
.course-registration-page {
    padding: 0 80px 40px 80px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-input {
  width: 250px;
}

.section-title {
  margin-bottom: 20px;
  color: var(--primary-color);
}

.section-title h2 {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
}

.courses-container {
  margin-bottom: 30px;
}

.course-card {
  background-color: var(--bg-card);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.course-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.course-image {
  position: relative;
  height: 160px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-hover);
}

.course-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary-color);
  width: 120px;
  height: 120px;
  background-color: #ecf5ff;
  border-radius: 50%;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.course-card:hover .course-image img {
  transform: scale(1.05);
}

.course-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.course-card:hover .course-actions {
  opacity: 1;
}

.course-info {
  padding: 15px;
}

.course-title {
  font-size: 18px;
  color: var(--text-primary);
  margin: 0 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 注册流程样式 */
.enrollment-process {
  padding: 20px 0;
}

.step-content {
  margin: 30px 0;
  min-height: 250px;
}

.step-form {
  max-width: 500px;
  margin: 0 auto;
}

.step-title {
  font-size: 24px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 30px;
  color: var(--text-primary);
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.full-width {
  width: 100%;
}

.confirmation {
  max-width: 600px;
  margin: 0 auto;
}

.confirm-section {
  margin-bottom: 30px;
}

.confirm-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #333;
}

.confirm-row {
  display: flex;
  margin-bottom: 20px;
}

.confirm-label {
  width: 100px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.confirm-value {
  flex: 1;
  font-size: 16px;
  color: #666;
}

.class-selection-tip {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.class-cards-container {
  margin-bottom: 20px;
}

.class-card {
  position: relative;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  transition: all 0.3s;
  cursor: pointer;
  text-align: center;
}

.class-card.selected {
  border: 2px solid #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
}

.class-card.error-border {
  border: 2px solid #f56c6c;
  box-shadow: 0 0 8px rgba(245, 108, 108, 0.2);
}

.selected-mark {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 30px 30px 0;
  border-color: transparent #409EFF transparent transparent;
  color: white;
}

.selected-mark .el-icon {
  position: absolute;
  top: 2px;
  right: -25px;
  font-size: 14px;
}

.class-info {
  padding: 5px 0;
}

.class-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.class-teacher,
.class-location,
.class-time {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.class-error-tip {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 10px;
}
</style> 