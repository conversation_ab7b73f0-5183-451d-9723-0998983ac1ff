<template>
  <div class="exam-list">
    <div v-if="exams.length === 0" class="empty-state">
      <el-image src="@/assets/empty-exam.svg" alt="暂无考试" class="empty-image" />
      <p class="empty-text">暂无{{ typeText }}的考试</p>
      <el-button v-if="type === 'upcoming'" type="primary" size="small" @click="createExam">
        创建考试
      </el-button>
    </div>

    <el-card
      v-else
      v-for="exam in exams"
      :key="exam.id"
      class="exam-card"
      :shadow="'hover'"
    >
      <template #header>
        <div class="card-header">
          <span>{{ exam.name }}</span>
          <el-tag :type="getStatusType(exam.status)">{{ getStatusText(exam.status) }}</el-tag>
        </div>
      </template>
      <div class="card-content">
        <div class="exam-info">
          <div class="info-row">
            <span class="label">考试时间：</span>
            <span class="value">{{ exam.timeRange }}</span>
          </div>
          <div class="info-row">
            <span class="label">考试时长：</span>
            <span class="value">{{ exam.duration }} 分钟</span>
          </div>
          <div class="info-row">
            <span class="label">试卷：</span>
            <span class="value">{{ exam.paperName }}</span>
          </div>
          <div class="info-row">
            <span class="label">参与人数：</span>
            <span class="value">{{ exam.participantCount || 0 }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="card-actions">
          <el-button
            v-if="exam.status === 'ongoing'"
            type="primary"
            size="small"
            @click="viewExam(exam.id)"
          >
            查看详情
          </el-button>
          <el-button
            v-else-if="exam.status === 'upcoming'"
            type="warning"
            size="small"
            @click="prepareExam(exam.id)"
          >
            准备考试
          </el-button>
          <el-button
            v-else
            type="success"
            size="small"
            @click="reviewExam(exam.id)"
          >
            查看结果
          </el-button>
        </div>
      </template>
    </el-card>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  exams: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'ongoing'
  }
})

const router = useRouter()

const typeText = computed(() => {
  const map = {
    ongoing: '进行中',
    upcoming: '未开始',
    completed: '已结束'
  }
  return map[props.type] || '进行中'
})

const getStatusText = (status) => {
  const map = {
    ongoing: '进行中',
    upcoming: '未开始',
    completed: '已结束'
  }
  return map[status] || status
}

const getStatusType = (status) => {
  const map = {
    ongoing: 'primary',
    upcoming: 'warning',
    completed: 'success'
  }
  return map[status] || 'info'
}

const viewExam = (id) => {
  router.push({ name: 'examDetail', params: { id } })
}

const prepareExam = (id) => {
  router.push({ name: 'examPreparation', params: { id } })
}

const reviewExam = (id) => {
  router.push({ name: 'examResult', params: { id } })
}

const createExam = () => {
  router.push({ name: 'createExam' })
}
</script>

<style scoped>
.exam-list {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  color: #909399;
  margin-bottom: 16px;
}

.exam-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-row {
  display: flex;
  align-items: center;
}

.label {
  color: #606266;
  width: 80px;
}

.value {
  color: #303133;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}
</style>