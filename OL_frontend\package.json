{"name": "ol-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "chart.js": "^4.4.9", "echarts": "^5.6.0", "element-plus": "^2.9.11", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-draggable-next": "^2.2.1", "vue-router": "^4.5.0", "vuedraggable": "^2.24.3", "vuex": "^4.0.2"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}