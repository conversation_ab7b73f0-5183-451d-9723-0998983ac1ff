<template>
  <div class="todo-list-page">
    <!-- <Header /> -->
    <div class="page-header">
      <h1 class="page-title">我的待办事项</h1>
      <div class="header-actions">
        <el-input
          placeholder="搜索待办事项"
          v-model="searchQuery"
          class="search-input"
          :prefix-icon="Search"
          clearable
        />
        <el-radio-group v-model="todoStatus" size="small">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="pending">待完成</el-radio-button>
          <el-radio-button label="completed">已完成</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 任务类型筛选 -->
    <div class="filter-container">
      <el-tag
        v-for="tag in taskTags"
        :key="tag.value"
        :type="activeTaskTags.includes(tag.value) ? tag.type : ''"
        effect="plain"
        class="task-tag"
        @click="toggleTaskTag(tag.value)"
      >
        {{ tag.label }}
      </el-tag>
    </div>

    <!-- 任务列表 -->
    <div class="todo-container">
      <el-empty v-if="filteredTasks.length === 0" description="暂无相关待办事项" />
      <el-card
        v-else
        v-for="task in filteredTasks"
        :key="task.id"
        class="task-card"
        :class="{ 'completed-task': task.status === 'completed' }"
      >
        <div class="task-header">
          <div class="task-info">
            <el-tag :type="getTaskTypeTag(task.type)" size="small">{{ task.type }}</el-tag>
            <h3 class="task-title">{{ task.title }}</h3>
          </div>
          <div class="task-meta">
            <span class="course-name">{{ task.courseName }}</span>
            <span class="deadline" :class="{ overdue: isOverdue(task) }">
              <el-icon><Clock /></el-icon>
              {{ task.status === 'completed' ? '已完成' : getDeadlineText(task.deadline) }}
            </span>
          </div>
        </div>

        <div class="task-content">
          <p>{{ task.description }}</p>
        </div>

        <div class="task-footer">
          <div class="task-status">
            <el-tag :type="getStatusType(task.status)" size="small">{{
              getStatusText(task.status)
            }}</el-tag>
            <span v-if="task.status === 'completed' && task.score !== undefined" class="task-score">
              <el-icon><Trophy /></el-icon> 得分: {{ task.score }}
            </span>
          </div>
          <div class="task-actions">
            <el-button
              v-if="task.status !== 'completed'"
              type="primary"
              size="small"
              @click="handleTaskAction(task)"
            >
              {{ getActionText(task) }}
            </el-button>
            <el-button type="info" size="small" @click="viewTaskDetail(task)">详情</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="showTaskDetailDialog"
      :title="currentTask ? currentTask.title : '任务详情'"
      width="700px"
    >
      <div v-if="currentTask" class="task-detail">
        <div class="detail-header">
          <div class="detail-meta">
            <el-tag :type="getTaskTypeTag(currentTask.type)">{{ currentTask.type }}</el-tag>
            <span class="course-name">{{ currentTask.courseName }}</span>
            <span class="teacher-name">教师: {{ currentTask.teacher }}</span>
          </div>
          <div class="detail-deadline">
            <el-icon><Clock /></el-icon> 截止日期: {{ formatDate(currentTask.deadline) }}
          </div>
        </div>

        <div class="detail-content">
          <h4>任务描述</h4>
          <p>{{ currentTask.description }}</p>

          <template v-if="currentTask.attachments && currentTask.attachments.length > 0">
            <h4>附件资料</h4>
            <div class="attachment-list">
              <div
                v-for="(file, index) in currentTask.attachments"
                :key="index"
                class="attachment-item"
              >
                <el-icon class="file-icon" :class="getFileIconClass(file.type)">
                  <Document v-if="file.type === 'pdf'" />
                  <Files v-else />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
                <div class="file-actions">
                  <el-button type="primary" link @click="downloadFile(file)">下载</el-button>
                  <el-button type="success" link @click="previewFile(file)">预览</el-button>
                </div>
              </div>
            </div>
          </template>

          <template v-if="currentTask.status === 'completed'">
            <h4>批改结果</h4>
            <div class="feedback-container">
              <div class="score-display">
                <el-icon><Trophy /></el-icon>
                <span class="score">{{ currentTask.score }}</span>
                <span class="total-score">/ {{ currentTask.totalScore }}</span>
              </div>
              <div class="feedback-content">
                <h5>教师评语</h5>
                <p>{{ currentTask.feedback || '暂无评语' }}</p>
              </div>
            </div>
          </template>

          <template v-if="currentTask.type === '作业' && currentTask.status !== 'completed'">
            <h4>提交作业</h4>
            <div class="submission-form">
              <el-input
                v-model="submissionContent"
                type="textarea"
                :rows="4"
                placeholder="请输入作业内容..."
              />
              <div class="upload-container">
                <el-upload
                  action="#"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :file-list="uploadFiles"
                  multiple
                >
                  <el-button type="primary">选择文件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">支持 .doc, .docx, .pdf, .zip 等格式文件</div>
                  </template>
                </el-upload>
              </div>
              <div class="submission-actions">
                <el-button type="primary" @click="submitAssignment">提交作业</el-button>
                <el-button @click="saveDraft">保存草稿</el-button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </el-dialog>

    <!-- 考试准备对话框 -->
    <el-dialog
      v-model="showExamPrepDialog"
      :title="currentTask ? currentTask.title + ' - 考试准备' : '考试准备'"
      width="500px"
    >
      <div v-if="currentTask" class="exam-prep">
        <div class="exam-info">
          <p><strong>课程:</strong> {{ currentTask.courseName }}</p>
          <p><strong>考试时长:</strong> {{ currentTask.duration }} 分钟</p>
          <p><strong>题目数量:</strong> {{ currentTask.questionCount }} 题</p>
          <p><strong>总分:</strong> {{ currentTask.totalScore }} 分</p>
          <p><strong>考试说明:</strong> {{ currentTask.examDescription }}</p>
        </div>
        <div class="exam-rules">
          <h4>考试须知</h4>
          <ul>
            <li>考试开始后计时，不可暂停</li>
            <li>请确保网络稳定，避免中途断网</li>
            <li>考试过程中切换窗口可能会被记录</li>
            <li>提交后不可重新作答</li>
          </ul>
        </div>
        <div class="exam-actions">
          <el-button type="primary" @click="enterExam">开始考试</el-button>
          <el-button @click="showExamPrepDialog = false">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Clock,
  Trophy,
  Document,
  Files,
  Calendar,
  User,
  Delete,
  MoreFilled,
  Upload,
  Download,
  View,
  Picture,
  Tickets,
  VideoCamera,
} from '@element-plus/icons-vue'
import Header from '@/components/header.vue'

const router = useRouter()

// 搜索和过滤
const searchQuery = ref('')
const todoStatus = ref('all')
const activeTaskTags = ref(['作业', '考试', '学习任务'])

// 对话框控制
const showTaskDetailDialog = ref(false)
const showExamPrepDialog = ref(false)
const currentTask = ref(null)

// 提交作业相关
const submissionContent = ref('')
const uploadFiles = ref([])

// 任务标签
const taskTags = [
  { label: '作业', value: '作业', type: 'primary' },
  { label: '考试', value: '考试', type: 'danger' },
  { label: '学习任务', value: '学习任务', type: 'success' },
  { label: '讨论', value: '讨论', type: 'warning' },
  { label: '实验', value: '实验', type: 'info' },
]

// 待办事项数据
const todoTasks = ref([
  {
    id: 1,
    title: '高等数学第一章作业',
    type: '作业',
    courseName: '高等数学基础',
    teacher: '张教授',
    description: '完成教材P15-P20的习题1-10，要求手写并拍照上传。',
    deadline: '2023-07-10 23:59',
    status: 'pending',
    totalScore: 100,
    attachments: [
      { name: '作业要求说明.pdf', type: 'pdf', size: '1.2MB' },
      { name: '参考资料.docx', type: 'docx', size: '0.8MB' },
    ],
  },
  {
    id: 2,
    title: '计算机网络期中考试',
    type: '考试',
    courseName: '计算机网络',
    teacher: '李教授',
    description: '本次考试内容包括TCP/IP协议、网络安全等内容，请提前做好准备。',
    deadline: '2023-07-15 10:00',
    status: 'pending',
    duration: 120,
    questionCount: 50,
    totalScore: 100,
    examDescription: '包含单选题、多选题和简答题',
  },
  {
    id: 3,
    title: '数据结构与算法视频学习',
    type: '学习任务',
    courseName: '数据结构与算法',
    teacher: '王教授',
    description: '观看"排序算法"相关视频，完成在线测验。',
    deadline: '2023-07-08 23:59',
    status: 'pending',
  },
  {
    id: 4,
    title: '大学英语口语练习',
    type: '作业',
    courseName: '大学英语',
    teacher: '刘教授',
    description: '根据给定主题录制3分钟英语口语视频并上传。',
    deadline: '2023-06-30 23:59',
    status: 'completed',
    completedDate: '2023-06-28',
    score: 92,
    totalScore: 100,
    feedback: '发音清晰，表达流畅，但语法方面还有些小问题需要注意。',
  },
  {
    id: 5,
    title: '计算机网络实验一',
    type: '实验',
    courseName: '计算机网络',
    teacher: '李教授',
    description: '完成网络配置实验，并提交实验报告。',
    deadline: '2023-06-25 23:59',
    status: 'completed',
    completedDate: '2023-06-24',
    score: 98,
    totalScore: 100,
    feedback: '实验完成度高，报告详细，表现优秀！',
  },
  {
    id: 6,
    title: '参与"算法优化"主题讨论',
    type: '讨论',
    courseName: '数据结构与算法',
    teacher: '王教授',
    description: '在课程讨论区参与关于"常见排序算法优化"的讨论，发表自己的见解。',
    deadline: '2023-07-20 23:59',
    status: 'pending',
  },
])

// 计算属性：过滤后的任务
const filteredTasks = computed(() => {
  return todoTasks.value
    .filter((task) => {
      // 状态过滤
      if (todoStatus.value === 'pending' && task.status !== 'pending') {
        return false
      }
      if (todoStatus.value === 'completed' && task.status !== 'completed') {
        return false
      }

      // 类型过滤
      if (!activeTaskTags.value.includes(task.type)) {
        return false
      }

      // 搜索过滤
      if (
        searchQuery.value &&
        !task.title.toLowerCase().includes(searchQuery.value.toLowerCase()) &&
        !task.courseName.toLowerCase().includes(searchQuery.value.toLowerCase())
      ) {
        return false
      }

      return true
    })
    .sort((a, b) => {
      // 优先显示未完成且临近截止日期的任务
      if (a.status === 'pending' && b.status === 'completed') {
        return -1
      }
      if (a.status === 'completed' && b.status === 'pending') {
        return 1
      }

      // 对于待完成任务，按截止日期排序
      if (a.status === 'pending' && b.status === 'pending') {
        return new Date(a.deadline) - new Date(b.deadline)
      }

      // 对于已完成任务，按完成日期倒序
      return new Date(b.completedDate || b.deadline) - new Date(a.completedDate || a.deadline)
    })
})

// 切换任务标签
const toggleTaskTag = (tag) => {
  const index = activeTaskTags.value.indexOf(tag)
  if (index > -1) {
    activeTaskTags.value.splice(index, 1)
  } else {
    activeTaskTags.value.push(tag)
  }
}

// 获取任务类型标签样式
const getTaskTypeTag = (type) => {
  switch (type) {
    case '作业':
      return 'primary'
    case '考试':
      return 'danger'
    case '学习任务':
      return 'success'
    case '讨论':
      return 'warning'
    case '实验':
      return 'info'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待完成'
    case 'completed':
      return '已完成'
    case 'overdue':
      return '已逾期'
    default:
      return '未知状态'
  }
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'completed':
      return 'success'
    case 'overdue':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取操作按钮文本
const getActionText = (task) => {
  switch (task.type) {
    case '作业':
      return '提交作业'
    case '考试':
      return '进入考试'
    case '学习任务':
      return '开始学习'
    case '讨论':
      return '参与讨论'
    case '实验':
      return '进入实验'
    default:
      return '查看'
  }
}

// 检查是否逾期
const isOverdue = (task) => {
  if (task.status === 'completed') return false
  return new Date(task.deadline) < new Date()
}

// 获取截止日期显示文本
const getDeadlineText = (deadline) => {
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diffTime = deadlineDate - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffTime < 0) {
    return '已逾期'
  } else if (diffDays === 0) {
    return '今天截止'
  } else if (diffDays === 1) {
    return '明天截止'
  } else if (diffDays <= 7) {
    return `${diffDays}天后截止`
  } else {
    return formatDate(deadline)
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取文件图标样式
const getFileIconClass = (type) => {
  return `file-icon-${type}`
}

// 处理任务操作
const handleTaskAction = (task) => {
  currentTask.value = task

  switch (task.type) {
    case '作业':
      showTaskDetailDialog.value = true
      break
    case '考试':
      showExamPrepDialog.value = true
      break
    case '学习任务':
      // 跳转到学习页面
      router.push('/study')
      break
    case '讨论':
      // 跳转到讨论页面
      router.push('/study')
      break
    case '实验':
      // 跳转到实验页面
      router.push('/study')
      break
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  currentTask.value = task
  showTaskDetailDialog.value = true
}

// 下载文件
const downloadFile = (file) => {
  ElMessage.success(`正在下载文件：${file.name}`)
  // 这里添加文件下载逻辑
}

// 预览文件
const previewFile = (file) => {
  ElMessage.info(`正在预览文件：${file.name}`)
  // 这里添加文件预览逻辑
}

// 处理文件上传变化
const handleFileChange = (file, fileList) => {
  uploadFiles.value = fileList
}

// 提交作业
const submitAssignment = () => {
  if (!submissionContent.value.trim() && uploadFiles.value.length === 0) {
    ElMessage.warning('请输入作业内容或上传文件')
    return
  }

  ElMessageBox.confirm('确定要提交作业吗？提交后将不能再修改。', '提交确认', {
    confirmButtonText: '确定提交',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 这里添加提交作业的逻辑
      ElMessage.success('作业提交成功！')

      // 更新任务状态
      if (currentTask.value) {
        const index = todoTasks.value.findIndex((task) => task.id === currentTask.value.id)
        if (index !== -1) {
          todoTasks.value[index].status = 'completed'
          todoTasks.value[index].completedDate = new Date().toISOString().split('T')[0]
        }
      }

      showTaskDetailDialog.value = false
    })
    .catch(() => {
      // 取消操作
    })
}

// 保存草稿
const saveDraft = () => {
  ElMessage.success('草稿保存成功')
}

// 进入考试
const enterExam = () => {
  ElMessage.success('正在进入考试')
  showExamPrepDialog.value = false
  router.push('/exam')
}
</script>

<style scoped>
.todo-list-page {
  padding: 0 80px 40px 80px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-input {
  width: 250px;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.task-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.task-tag:hover {
  transform: translateY(-2px);
}

.todo-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-card {
  transition: all 0.3s;
  border-left: 4px solid var(--primary-color);
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-card.completed-task {
  border-left-color: var(--success-color);
  opacity: 0.8;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.task-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  font-size: 14px;
}

.course-name {
  color: var(--text-secondary);
}

.deadline {
  display: flex;
  align-items: center;
  gap: 5px;
}

.deadline.overdue {
  color: var(--danger-color);
}

.task-content {
  margin-bottom: 15px;
  color: var(--text-regular);
  font-size: 14px;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 15px;
}

.task-score {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--success-color);
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 10px;
}

/* 任务详情样式 */
.task-detail {
  padding: 10px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-light);
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.detail-deadline {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 5px;
}

.detail-content h4 {
  margin: 20px 0 10px;
  color: var(--text-primary);
  font-size: 16px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--bg-hover);
  border-radius: 4px;
}

.file-icon {
  font-size: 20px;
  margin-right: 10px;
}

.file-icon-pdf {
  color: #f56c6c;
}

.file-icon-docx {
  color: #409eff;
}

.file-name {
  flex: 1;
  font-size: 14px;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.feedback-container {
  background-color: var(--bg-hover);
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.score-display {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.score {
  font-size: 24px;
  font-weight: bold;
  color: var(--success-color);
  margin: 0 5px;
}

.total-score {
  color: var(--text-secondary);
}

.feedback-content h5 {
  margin: 0 0 10px;
  font-size: 14px;
  color: var(--text-primary);
}

.submission-form {
  margin-top: 15px;
}

.upload-container {
  margin: 15px 0;
}

.submission-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

/* 考试准备样式 */
.exam-prep {
  padding: 10px;
}

.exam-info {
  margin-bottom: 20px;
}

.exam-info p {
  margin: 10px 0;
  font-size: 14px;
}

.exam-rules {
  background-color: var(--bg-hover);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.exam-rules h4 {
  margin: 0 0 10px;
  font-size: 16px;
  color: var(--danger-color);
}

.exam-rules ul {
  padding-left: 20px;
  margin: 0;
}

.exam-rules li {
  margin-bottom: 5px;
  font-size: 14px;
}

.exam-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
