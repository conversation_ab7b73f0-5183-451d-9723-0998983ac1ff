import os
import sys
import subprocess
from io import StringIO

# 设置环境变量
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "OL_backend.settings")

# 捕获命令输出
original_stdout = sys.stdout
sys.stdout = mystdout = StringIO()

# 导入并执行inspectdb命令
from django.core.management import call_command
call_command('inspectdb')

# 恢复原始输出
sys.stdout = original_stdout

# 获取输出内容
output = mystdout.getvalue()

# 以UTF-8编码写入文件
with open('temp_models.py', 'w', encoding='utf-8') as f:
    f.write(output)

print('模型文件已成功生成，保存为 temp_models.py') 