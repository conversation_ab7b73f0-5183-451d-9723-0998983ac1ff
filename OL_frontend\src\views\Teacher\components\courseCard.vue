<template>
  <div class="course-card" v-if="course">
    <div class="course-image">
      <img :src="course.cover_url || '@/assets/img/course.png'" alt="课程封面" class="course-cover">
    </div>
    <div class="course-info">
      <h4 class="course-title">{{ course.title }}</h4>
      <!-- <p class="course-semester">{{ course.create_time | formatDate }}</p> -->

      <p class="course-desc" v-if="course.description">{{ course.description }}</p>
      <p class="course-desc" v-else>暂无课程简介</p>
      
      <div class="course-actions">
        <el-button size="medium" @click="$emit('edit', course)">编辑</el-button>
        
        <template v-if="isPublished">
          <el-button type="warning" size="medium" @click="$emit('unpublish', course)" plain>
            取消发布
          </el-button>
        </template>
        
        <template v-else>
          <el-button type="primary" size="medium" @click="$emit('publish', course)" plain>
            发布
          </el-button>
          <el-button type="danger" size="medium" @click="$emit('delete', course)" plain>
            删除
          </el-button>
        </template>
      </div>
    </div>
  </div>
  <div v-else class="loading-card">加载中...</div>
</template>

<script setup>
import { defineProps, computed } from 'vue'

const props = defineProps({
  course: {
    type: Object,
    required: true
  },
  isPublished: {
    type: Boolean,
    default: false
  }
})

// 日期格式化过滤器
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  
  try {
    const date = new Date(dateString)
    return `${date.getFullYear()}年${date.getMonth() + 1}月`
  } catch (error) {
    console.error('日期格式化失败:', error)
    return dateString
  }
}

// 计算属性，用于调试
const courseDebug = computed(() => {
  console.log('课程卡片数据:', props.course)
  return props.course
})
</script>

<style scoped>
.course-grid {
    margin-top: 20px;
}

/* 课程卡片样式 */
.course-card {
    background-color: var(--bg-card);
    border: 1px solid var(--course-card-border);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    cursor: pointer;
}

.course-card:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}


.course-image {
    width: 100%;
    height: 160px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--primary-light-9);
    position: relative;
}

.course-image img {
    width: 100%;
    height: 100%;
}

.course-info {
  padding: 15px;
}

.course-title {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: var(--text-primary);
}

.course-semester {
  color: #999;
  font-size: 13px;
  margin-bottom: 10px;
}

.course-desc {
    margin: 0 0 15px 0;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    height: 63px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.course-actions {
  display: flex;
  justify-content: space-between;
}

.loading-card {
  width: 280px;
  height: 240px;
  line-height: 240px;
  text-align: center;
  color: #999;
  border: 1px dashed #eee;
  border-radius: 4px;
}
</style>