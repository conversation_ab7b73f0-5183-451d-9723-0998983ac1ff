<template>
    <div class="auth-container">
        <el-card class="auth-card">
            <h2>用户登录</h2>
            <el-form :model="form" :rules="rules" ref="loginForm" @submit.prevent="handleLogin">
                <el-form-item prop="username">
                    <el-input v-model="form.username" placeholder="请输入账号" prefix-icon="User" />
                </el-form-item>

                <el-form-item prop="password">
                    <el-input v-model="form.password" type="password" placeholder="请输入密码" prefix-icon="Lock"
                        show-password />
                </el-form-item>

                <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading">
                    登录
                </el-button>
            </el-form>

            <div class="auth-footer">
                <span>还没有账号？</span>
                <el-link type="primary" @click="gotoRegister">立即注册</el-link>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const form = ref({
    username: '',
    password: ''
})

const rules = {
    username: [
        { required: true, message: '请输入账号', trigger: 'blur' },
        { pattern: /^\d{10}$/, message: '账号必须为10位数字', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, message: '密码长度至少8位', trigger: 'blur' }
    ]
}

const loginForm = ref(null)
const loading = ref(false)

const handleLogin = () => {
    loginForm.value.validate(async valid => {
        if (valid) {
            loading.value = true
            try {
                await userStore.login(form.value)
            } finally {
                loading.value = false
            }
        }
    })
}

const gotoRegister = () => {
    router.push('/register')
}
</script>

<style scoped>
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f7fa;
}

.auth-card {
    width: 400px;
    padding: 30px;
}

h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.submit-btn {
    width: 100%;
    margin-top: 10px;
}

.auth-footer {
    margin-top: 20px;
    text-align: center;
    color: #666;
}
</style>