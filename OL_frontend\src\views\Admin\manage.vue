<template>
    <div class="admin-container">
        <!-- 推荐课程管理 -->
        <div class="recommend-course-section">
            <h2 class="section-title">推荐课程管理</h2>

            <div class="operation-bar">
                <el-button type="primary" @click="showRecommendDialog = true">
                    <el-icon>
                        <Plus />
                    </el-icon> 添加推荐课程
                </el-button>

                <el-input v-model="searchQuery" placeholder="搜索课程名称" style="width: 300px; margin-left: 20px;" clearable
                    @clear="handleSearchClear">
                    <template #append>
                        <el-button @click="handleSearch">
                            <el-icon>
                                <Search />
                            </el-icon>
                        </el-button>
                    </template>
                </el-input>
            </div>

            <el-table :data="filteredCourses" border style="width: 100%; margin-top: 20px;" v-loading="loading">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="title" label="课程名称" />
                <el-table-column prop="category" label="分类"
                    :formatter="(row) => categoryMap[row.categoryId] || '未知分类'" />
                <el-table-column prop="students" label="学习人数" width="120" />
                <el-table-column prop="likes" label="收藏数" width="120" />
                <el-table-column label="推荐级别" width="150">
                    <template #default="{ row }">
                        <el-rate v-model="row.recommendLevel" :max="3" :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                            @change="handleRateChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                        <el-button size="small" type="danger" @click="handleRemove(row.id)">移除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="totalCourses"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>

        <!-- 系统消息通知管理 -->
        <div class="notification-section">
            <h2 class="section-title">系统消息通知管理</h2>

            <div class="operation-bar">
                <el-button type="primary" @click="handleAddNotification">
                    <el-icon>
                        <Plus />
                    </el-icon> 添加通知
                </el-button>
            </div>

            <el-table :data="notifications" border style="width: 100%; margin-top: 20px;">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="notification_text" label="通知内容" />
                <el-table-column prop="publish_time" label="发布时间" width="180" />
                <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                        <el-button size="small" @click="handleEditNotification(row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="handleDeleteNotification(row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 推荐课程对话框 -->
        <el-dialog v-model="showRecommendDialog" :title="isEditing ? '编辑推荐课程' : '添加推荐课程'" width="50%">
            <el-form :model="recommendForm" label-width="120px">
                <el-form-item label="课程名称" required>
                    <el-select v-model="recommendForm.courseId" filterable remote reserve-keyword placeholder="请输入课程名称"
                        :remote-method="searchCourses" :loading="searchLoading" style="width: 100%">
                        <el-option v-for="item in courseOptions" :key="item.id" :label="item.title" :value="item.id"
                            @click="saveRecommendId(item.id)" />
                    </el-select>
                </el-form-item>
                <el-form-item label="推荐级别">
                    <el-rate v-model="recommendForm.recommendLevel" :max="3"
                        :colors="['#99A9BF', '#F7BA2A', '#FF9900']" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showRecommendDialog = false">取消</el-button>
                    <el-button type="primary" @click="setRecommend">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 通知编辑对话框 -->
        <el-dialog v-model="isEditingNotification" title="编辑通知" width="50%">
            <el-form :model="notificationForm" label-width="120px">
                <el-form-item label="通知内容" required>
                    <el-input v-model="notificationForm.notification_text" type="textarea" :rows="3"
                        placeholder="请输入通知内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="isEditingNotification = false">取消</el-button>
                    <el-button type="primary" @click="submitEditNotification">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 通知管理对话框 -->
        <el-dialog v-model="showNotificationDialog" title="添加通知'" width="50%">
            <el-form :model="notificationForm" label-width="120px">
                <el-form-item label="通知内容" required>
                    <el-input v-model="notificationForm.notification_text" type="textarea" :rows="3"
                        placeholder="请输入通知内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showNotificationDialog = false">取消</el-button>
                    <el-button type="primary" @click="handleNotificationSubmit">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { nextTick } from 'vue';
import axios from 'axios'
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 推荐课程管理相关状态
const loading = ref(false)
const courses = ref([])
const recommendId = ref(null)
const editId = ref(null)
const filteredCourses = ref([])
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalCourses = ref(0)
const showRecommendDialog = ref(false)
const isEditing = ref(false)
const recommendForm = ref({
    id: null,
    courseId: null,
    recommendLevel: 1
})
const courseOptions = ref([])
const searchLoading = ref(false)
// 课程分类
const categoryMap = {
    1: '计算机科学',
    2: '数学与统计',
    3: '物理与化学'
}

// 通知管理相关状态
const notifications = ref([])
const showNotificationDialog = ref(false)
const isEditingNotification = ref(false)
const notificationForm = ref({
    id: null,
    notification_text: '',
    publish_time: ''
})

// 获取推荐课程列表
const fetchRecommendedCourses = async () => {
    loading.value = true
    try {
        const courseRes = await axios.get('/api/courses_list')
        totalCourses.value = courseRes.data.map(c => ({
            id: c.id,
            title: c.title,
            description: c.description,
            coverUrl: c.cover_url,
            updateTime: c.update_time,
            categoryId: c.category.id,
            students: c.enroll_count,
            likes: c.favorite_count,
            isRecommended: c.recommend_level >= 1,
            isPopular: c.enroll_count >= 1,
            isNew: Date.now() - new Date(c.update_time).getTime() < 30 * 24 * 60 * 60 * 1000
        }))
        courses.value = totalCourses.value.filter((c) => c.isRecommended)
        console.log(courses.value)
        filterCourses()
    } catch (error) {
        ElMessage.error('获取推荐课程失败')
        console.error(error)
    } finally {
        loading.value = false
    }
}

// 获取通知列表
const fetchNotifications = async () => {
    try {
        const res = await axios.get('/api/notifications')
        notifications.value = res.data
    } catch (error) {
        ElMessage.error('获取通知列表失败')
        console.error(error)
    }
}

// 搜索课程
const searchCourses = async (keyword) => {
    if (!keyword) return
    searchLoading.value = true
    try {
        const res = await axios.get('/api/courses/search', {
            params: { keyword }
        })
        console.log(res.data)
        courseOptions.value = res.data
        console.log(courseOptions.value)
    } catch (error) {
        ElMessage.error('搜索课程失败')
        console.error(error)
    } finally {
        searchLoading.value = false
    }
}

const saveRecommendId = (courseId) => {
    recommendId.value = courseId
}

const setRecommend = async () => {
    try {
        await axios.put(`/api/courses/${recommendId.value}/add-recommend/`);
        ElMessage.success('添加推荐课程成功')
        fetchRecommendedCourses()
        showRecommendDialog.value = false
    } catch (error) {
        ElMessage.error('添加推荐课程失败')
    }
}

// 过滤课程
const filterCourses = () => {
    if (searchQuery.value) {
        filteredCourses.value = courses.value.filter(course =>
            course.title.includes(searchQuery.value))
    } else {
        filteredCourses.value = courses.value

    }
}

// 处理搜索
const handleSearch = () => {
    currentPage.value = 1
    fetchRecommendedCourses()
}

// 处理搜索清除
const handleSearchClear = () => {
    searchQuery.value = ''
    handleSearch()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
    pageSize.value = val
    fetchRecommendedCourses()
}

// 处理当前页变化
const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchRecommendedCourses()
}

// 处理推荐级别变化
const handleRateChange = async (row) => {
    ElMessage.success('更新推荐级别成功')
}

// 移除推荐课程
const handleRemove = async (courseId) => {
    try {
        const response = await axios.put(`/api/courses/${courseId}/remove-recommend/`);
        ElMessage.success('推荐课程已移除')    // 刷新列表或更新状态...
        fetchRecommendedCourses()
    } catch (error) {
        ElMessage.error('移除推荐课程失败')
    }
};

// 编辑通知
const handleEditNotification = async (row) => {
    notificationForm.value.notification_text = row.notification_text
    // 设置编辑ID
    editId.value = row.id
    await nextTick(); // 等待 DOM 更新
    isEditingNotification.value = true
}

// 提交编辑通知
const submitEditNotification = async () => {
    try {
        const response = await axios.put(`/api/notifications/${editId.value}/`, notificationForm.value);
        ElMessage.success('编辑通知成功')    // 刷新列表或更新状态...
        fetchNotifications()
        isEditingNotification.value = false
    } catch (error) {
        ElMessage.error('编辑通知失败')
    }
}

// 删除通知
const handleDeleteNotification = (id) => {
    ElMessageBox.confirm('确定要删除该通知吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await axios.delete(`/api/notifications/${id}/`)
            ElMessage.success('删除成功')
            fetchNotifications()
        } catch (error) {
            ElMessage.error('删除失败')
            console.error(error)
        }
    }).catch(() => { })
}

const handleAddNotification = () => {
    // 重置表单
    notificationForm.value = {
        id: null,
        notification_text: '',
        publish_time: ''
    }
    showNotificationDialog.value = true
    isEditingNotification.value = false
}   

// 提交通知表单
const handleNotificationSubmit = async () => {
    try {
        await axios.post('/api/notifications/', notificationForm.value)
        ElMessage.success('添加通知成功')
        showNotificationDialog.value = false
        fetchNotifications()
    } catch (error) {
        ElMessage.error(isEditingNotification.value ? '更新通知失败' : '添加通知失败')
        console.error(error)
    }
}

// 初始化数据
onMounted(() => {
    fetchRecommendedCourses()
    fetchNotifications()
})
</script>

<style scoped>
.admin-container {
    padding: 0 60px 40px 60px;
}

.section-title {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
    font-weight: bold;
}

.operation-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.recommend-course-section {
    margin-bottom: 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.notification-section {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
}
</style>