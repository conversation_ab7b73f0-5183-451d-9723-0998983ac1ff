<script setup>
import Layout from './components/Layout.vue'
import Footer from './components/Footer.vue'
import { useRoute } from 'vue-router' // 必须显式导入
const route = useRoute() // 正确获取当前路由
</script>

<template>
    <Layout v-if="!route.meta?.hideLayout"/>
    <router-view />
    <Footer v-if="!route.meta?.hideFooter"/>
    <!-- <RouterView /> -->
</template>

<style>
@import './assets/base.css';

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--bg-page);
    color: var(--text-regular);
}

.page-container {
    padding: 20px;
}
</style>
