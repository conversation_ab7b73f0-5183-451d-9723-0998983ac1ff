<template>
  <div class="question-bank-detail">
    <div class="header">
      <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      <h1 class="title">{{ bank.name }}</h1>
    </div>

    <div class="info-container">
      <el-card class="basic-info">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">出题老师：</span>
            <span>{{ bank.teacher_info?.username || '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span>{{ formatDate(bank.create_time) }}</span>
          </div>
          <div class="info-item">
            <span class="label">题目数量：</span>
            <span>{{ bank.question_count }} 道</span>
          </div>
          <div class="info-item">
            <span class="label">引用次数：</span>
            <span>{{ bank.use_count }}</span>
          </div>
          <div class="info-item">
            <span class="label">平均正确率：</span>
            <span>{{ (bank.accuracy_rate * 100).toFixed(1) }}%</span>
          </div>
        </div>
      </el-card>

      <el-card class="description-card">
        <template #header>
          <div class="card-header">
            <span>题库描述</span>
          </div>
        </template>
        <div class="description-content">
          {{ bank.description || '暂无描述' }}
        </div>
      </el-card>

      <el-card class="tags-card">
        <template #header>
          <div class="card-header">
            <span>题库标签</span>
          </div>
        </template>
        <div class="tags-container">
          <el-tag v-for="tag in bank.tags" :key="tag" type="info" size="medium"
            style="margin-right: 8px; margin-bottom: 8px;">
            {{ tag }}
          </el-tag>
        </div>
      </el-card>

      <el-card class="questions-card">
        <template #header>
          <div class="card-header">
            <span>题目列表</span>
            <el-button type="primary" size="small" @click="fetchQuestions">刷新题目</el-button>
          </div>
        </template>

        <el-table :data="questions" stripe border v-loading="questionLoading" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="type" label="题型" width="120">
            <template #default="{ row }">
              {{ questionTypeMap[row.type] || row.type }}
            </template>
          </el-table-column>
          <el-table-column prop="content" label="题目内容" min-width="300" />
          <el-table-column prop="difficulty" label="难度" width="100" align="center">
            <template #default="{ row }">
              <el-rate v-model="row.difficulty" disabled :max="5" show-score text-color="#ff9900"
                score-template="{value}" />
            </template>
          </el-table-column>
          <el-table-column prop="score" label="分值" width="80" align="center" />
        </el-table>

        <el-pagination @size-change="handleQuestionSizeChange" @current-change="handleQuestionPageChange"
          :current-page="questionPagination.current" :page-sizes="[10, 20, 50]" :page-size="questionPagination.size"
          layout="total, sizes, prev, pager, next, jumper" :total="questionPagination.total"
          style="margin-top: 20px;" />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'

const route = useRoute()
const bank = ref({})
const loading = ref(false)
const questions = ref([])
const questionLoading = ref(false)

// 题型映射
const questionTypeMap = {
  'single_choice': '单选题',
  'multiple_choice': '多选题',
  'judgment': '判断题',
  'fill_blank': '填空题',
  'essay': '问答题'
}

// 题目分页
const questionPagination = ref({
  current: 1,
  size: 10,
  total: 0
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取题库详情
const fetchBankDetail = async () => {
  const res = await axios.get(`/question-banks/${route.params.id}/`)
  bank.value = res.data
}

// 获取题目列表
const fetchQuestions = async () => {
  const params = {
    page: questionPagination.value.current,
    page_size: questionPagination.value.size
  }
  const res = await axios.get(`/question-banks/${route.params.id}/questions/`, { params })
  questions.value = res.data.results
}
// 分页变化
const handleQuestionSizeChange = (size) => {
  questionPagination.value.size = size
  fetchQuestions()
}

const handleQuestionPageChange = (current) => {
  questionPagination.value.current = current
  fetchQuestions()
}

onMounted(() => {
  fetchBankDetail()
  fetchQuestions()
})
</script>

<style scoped>
.question-bank-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header .title {
  margin-left: 20px;
  font-size: 24px;
  color: #303133;
}

.info-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.basic-info {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.description-content {
  line-height: 1.6;
  color: #606266;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.questions-card {
  margin-top: 20px;
}
</style>