<template>
  <div class="my-exam">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">我的考试</h2>
        <el-tag type="info" effect="light" class="total-tag">
          共 {{ totalExams }} 场考试
        </el-tag>
      </div>
      <div class="header-right">
        <el-input v-model="searchText" placeholder="搜索考试名称" clearable style="width: 200px; margin-right: 10px;"
          @keyup.enter="fetchExams" @clear="fetchExams" />
        <el-button type="primary" @click="createNewExam">
          <el-icon>
            <Plus />
          </el-icon> 新建考试
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeStatus" class="exam-tabs" @tab-change="fetchExams">
      <el-tab-pane label="进行中" name="ongoing">
        <ExamCard v-for="exam in filteredExams" :key="exam.id" :exam="exam" @edit="editExam" @delete="deleteExam"
          @view-result="viewExamResult" />
      </el-tab-pane>
      <el-tab-pane label="未开始" name="upcoming">
        <ExamCard v-for="exam in filteredExams" :key="exam.id" :exam="exam" @edit="editExam" @delete="deleteExam"
          @view-result="viewExamResult" />
      </el-tab-pane>
      <el-tab-pane label="已结束" name="completed">
        <ExamCard v-for="exam in filteredExams" :key="exam.id" :exam="exam" @edit="editExam" @delete="deleteExam"
          @view-result="viewExamResult" />
      </el-tab-pane>
    </el-tabs>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalExams"
      layout="total, sizes, prev, pager, next, jumper" @size-change="fetchExams" @current-change="fetchExams" />

    <!-- 考试编辑/创建对话框 -->
    <el-dialog v-model="showExamDialog" :title="isEditMode ? '编辑考试' : '新建考试'" width="80%" top="5vh">
      <ExamEditor v-if="showExamDialog" :exam-id="currentExamId" @success="handleExamSuccess"
        @cancel="showExamDialog = false" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import ExamCard from './examCard.vue'
import ExamEditor from './ExamEditor.vue'
import axios from '@/utils/axios'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const router = useRouter()

// 数据状态
const activeStatus = ref('ongoing')
const searchText = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalExams = ref(0)
const exams = ref([])
const showExamDialog = ref(false)
const currentExamId = ref(null)
const isEditMode = ref(false)
const loading = ref(false) // 添加loading状态

// 计算属性
const filteredExams = computed(() => {
  return exams.value.filter(exam =>
    exam.status === activeStatus.value &&
    exam.title.includes(searchText.value))
})

// 方法
const fetchExams = async () => {
  try {
    loading.value = true;
    const teacherId = userStore.username;

    // 状态映射
    const statusMap = {
      'ongoing': 2,
      'upcoming': 1,
      'completed': 3
    };

    const params = {
      teacher_id: teacherId,
      status: statusMap[activeStatus.value], // 使用数字状态值
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchText.value
    }

    const response = await axios.get('/exams/', { params })
    exams.value = response.data.results
    totalExams.value = response.data.count
  } catch (error) {
    ElMessage.error('获取考试列表失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false;
  }
}

const createNewExam = () => {
  currentExamId.value = null
  isEditMode.value = false
  showExamDialog.value = true
}

const editExam = (id) => {
  currentExamId.value = id
  isEditMode.value = true
  showExamDialog.value = true
}

const deleteExam = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除此考试吗？删除后无法恢复', '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.delete(`/exams/${id}/`)
    ElMessage.success('删除成功')
    fetchExams()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
    }
  }
}

const viewExamResult = (id) => {
  router.push({ name: 'ExamResult', params: { id } })
}

const handleExamSuccess = () => {
  showExamDialog.value = false
  fetchExams()
}

// 初始化
onMounted(() => {
  fetchExams()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.total-tag {
  height: 28px;
  line-height: 26px;
}

.exam-tabs {
  margin-bottom: 20px;
}

.el-pagination {
  margin-top: 20px;
  justify-content: center;
}
</style>