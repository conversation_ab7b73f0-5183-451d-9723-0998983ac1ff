<template>
    <div class="main-page">
        <!-- <Header /> -->
        <div class="main-part">
            <!-- 左侧导航栏 -->
            <div class="left-nav">
                <el-collapse v-model="activeCollapse" accordion>
                    <el-collapse-item title="题库管理" name="questionBankManagement">
                        <el-menu :default-active="activeMenu" @select="handleMenuSelect" active-text-color="#409EFF">
                            <el-menu-item index="1">查阅题库</el-menu-item>
                            <el-menu-item index="2">我的题库</el-menu-item>
                        </el-menu>
                    </el-collapse-item>
                    <el-collapse-item title="考试管理" name="examManagement">
                        <el-menu :default-active="activeMenu" @select="handleMenuSelect" active-text-color="#409EFF">
                            <el-menu-item index="3">我的试卷</el-menu-item>
                            <el-menu-item index="4">我的考试</el-menu-item>
                        </el-menu>
                    </el-collapse-item>
                </el-collapse>
            </div>
            <!-- 右侧内容区域 -->
            <div class="right-content">
                <component :is="currentComponent" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import Header from '@/components/Header.vue';

// 导入四个子组件
import QuestionBankList from '@/views/Teacher/components/QuestionBankList.vue';
import MyQuestionBank from '@/views/Teacher/components/MyQuestionBank.vue';
import MyPaper from '@/views/Teacher/Paper/components/myPaper.vue';
import MyExam from '@/views/Teacher/Exam/components/myExam.vue';

const router = useRouter();
const selectedMenuIndex = ref(localStorage.getItem('selectedMenuIndex') || '1');

// 根据当前菜单动态渲染组件
const currentComponent = computed(() => {
    const componentMap = {
        '1': QuestionBankList,
        '2': MyQuestionBank,
        '3': MyPaper,
        '4': MyExam
    };
    return componentMap[selectedMenuIndex.value] || QuestionBankList;
});

// 处理菜单选择事件
const handleMenuSelect = (index) => {
    selectedMenuIndex.value = index;
    localStorage.setItem('selectedMenuIndex', index);
};

onMounted(() => {
    // 页面加载时根据存储的索引设置当前组件
    handleMenuSelect(selectedMenuIndex.value);
});
</script>

<style scoped>
.main-page {
    padding: 20px 40px;
    min-height: calc(100vh - 40px);
    background-color: #f8fafc;
}

.main-part {
    display: flex;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.left-nav {
    width: 220px;
    border-right: 1px solid #e4e7ed;
    background-color: #f5f7fa;
    padding: 15px 0;
    flex-shrink: 0;
    min-height: calc(100vh - 120px);
}

.left-nav>>>.el-collapse {
    border: none;
}

.left-nav>>>.el-collapse-item__header {
    padding-left: 20px;
    font-weight: 600;
    color: #333;
    border-bottom: none;
}

.left-nav>>>.el-collapse-item__wrap {
    border-bottom: none;
    background-color: transparent;
}

.left-nav>>>.el-menu {
    border-right: none;
    background-color: transparent;
}

.left-nav>>>.el-menu-item {
    height: 40px;
    line-height: 40px;
    margin: 4px 0;
    border-radius: 4px;
    transition: all 0.3s;
}

.left-nav>>>.el-menu-item:hover {
    background-color: #ecf5ff;
}

.left-nav>>>.el-menu-item.is-active {
    background-color: #ecf5ff;
}

.right-content {
    flex: 1;
    padding: 25px 30px;
    min-width: 0;
    background-color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-page {
        padding: 10px;
    }

    .main-part {
        flex-direction: column;
    }

    .left-nav {
        width: 100%;
        min-height: auto;
    }

    .right-content {
        padding: 15px;
    }
}
</style>


