<template>
  <el-card class="exam-card" :shadow="hoverShadow">
    <div class="card-header">
      <div class="status-dot" :class="getStatusClass(exam.status)"></div>
      <h3 class="exam-name">{{ exam.name }}</h3>
    </div>
    <div class="card-content">
      <div class="info-grid">
        <div class="info-item">
          <span class="label">考试编号：</span>
          <span class="value">{{ exam.code }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联试卷：</span>
          <span class="value">{{ exam.paperName }}</span>
        </div>
        <div class="info-item">
          <span class="label">考试时间：</span>
          <span class="value">{{ exam.timeRange }}</span>
        </div>
        <div class="info-item" v-if="exam.status === 'ongoing'">
          <span class="label">参与人数：</span>
          <span class="value">{{ exam.studentCount }}/{{ exam.totalStudents }}</span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <el-button
        type="info"
        size="small"
        @click="viewExam(exam.id)"
        v-if="exam.status === 'ongoing'"
      >
        <el-icon><Eye /></el-icon> 查看试卷
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="editExam(exam.id)"
        v-if="exam.status === 'upcoming'"
      >
        <el-icon><Edit /></el-icon> 编辑
      </el-button>
      <el-button
        type="info"
        size="small"
        @click="viewResult(exam.id)"
        v-if="exam.status === 'completed'"
      >
        <el-icon><Document /></el-icon> 查看结果
      </el-button>
    </div>
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({ exam: Object })
const router = useRouter()
const hoverShadow = ref('hover')

const getStatusClass = (status) => ({
  ongoing: 'bg-blue-500',
  upcoming: 'bg-orange-500',
  completed: 'bg-green-500'
}[status] || 'bg-gray-500')

const viewExam = (id) => {
  router.push('paper-detail')
}

const editExam = (id) => {
  router.push('exam-edit')
}

const viewResult = (id) => {
  router.push('exam-result')
}
</script>