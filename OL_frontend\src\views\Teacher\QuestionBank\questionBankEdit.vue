<template>
    <div class="questionbank-edit">
        <!-- 题库基本信息板块 -->
        <div class="bank-info-section">
            <div class="section-header">
                <h2 v-if="isEditMode">编辑题库：{{ bankForm.name }}</h2>
                <h2 v-else>新建题库</h2>
                <div class="header-actions">
                    <el-button v-if="!isEditingBankInfo && isEditMode" type="primary" @click="startEditingBankInfo">
                        编辑信息
                    </el-button>
                    <div v-else class="edit-actions">
                        <el-button type="primary" @click="saveBankInfo" :loading="savingBank">
                            {{ isEditMode ? '保存修改' : '创建题库' }}
                        </el-button>
                        <el-button @click="cancelEditBankInfo">取消</el-button>
                    </div>
                </div>
            </div>

            <!-- 编辑表单 -->
            <el-form :model="bankForm" label-width="100px" v-if="isEditingBankInfo || !isEditMode">
                <el-form-item label="题库名称" required>
                    <el-input v-model="bankForm.name" placeholder="请输入题库名称（必填）" />
                </el-form-item>

                <el-form-item label="题库描述">
                    <el-input v-model="bankForm.description" type="textarea" :rows="3" placeholder="请输入题库描述" />
                </el-form-item>

                <el-form-item label="题库权限">
                    <el-radio-group v-model="bankForm.visibility">
                        <el-radio :label="0">私有（仅自己可见）</el-radio>
                        <el-radio :label="1">公开（所有教师可见）</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="关联课程">
                    <el-select v-model="bankForm.courses" multiple filterable placeholder="请选择关联课程" style="width: 100%"
                        :loading="loadingCourses">
                        <el-option v-for="course in allCourses" :key="course.id" :label="course.title"
                            :value="course.id" />
                    </el-select>
                </el-form-item>

                <el-form-item label="标签">
                    <div class="tag-container">
                        <el-tag v-for="tag in bankForm.tags" :key="tag" closable @close="removeTag(tag)"
                            class="custom-tag">
                            {{ tag }}
                        </el-tag>
                        <el-input v-if="inputVisible" ref="tagInput" v-model="inputValue" size="small"
                            @keyup.enter="addTag" @blur="addTag" style="width: 100px;" />
                        <el-button v-else size="small" @click="showInput" class="add-tag-btn">
                            + 添加标签
                        </el-button>
                    </div>
                </el-form-item>
            </el-form>

            <!-- 只读信息展示 -->
            <div class="info-grid" v-else>
                <div class="info-item">
                    <label>题库描述</label>
                    <p>{{ bankForm.description || '暂无描述' }}</p>
                </div>
                <div class="info-item">
                    <label>题库权限</label>
                    <p>
                        <el-tag :type="bankForm.visibility === 0 ? 'danger' : 'success'">
                            {{ bankForm.visibility === 0 ? '私有' : '公开' }}
                        </el-tag>
                    </p>
                </div>
                <div class="info-item">
                    <label>关联课程</label>
                    <p>
                        <span v-if="bankForm.courses.length === 0">未关联课程</span>
                        <el-tag v-for="courseId in bankForm.courses" :key="courseId" style="margin-right: 5px;">
                            {{ getCourseName(courseId) }}
                        </el-tag>
                    </p>
                </div>
                <div class="info-item">
                    <label>标签</label>
                    <p>
                        <el-tag v-for="tag in bankForm.tags" :key="tag" style="margin-right: 5px;">
                            {{ tag }}
                        </el-tag>
                        <span v-if="bankForm.tags.length === 0">暂无标签</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- 题库试题管理（仅编辑模式显示） -->
        <div class="question-management" v-if="isEditMode">
            <div class="management-header">
                <h3 style="margin-bottom: 10px;">题库试题管理</h3>
                <el-button type="primary" @click="showQuestionDialog('choice')" v-if="!isEditingBankInfo">
                    + 添加试题
                </el-button>
            </div>

            <el-tabs v-model="activeQuestionTab">
                <el-tab-pane label="选择题" name="choice">
                    <QuestionTable :questions="choiceQuestions" @edit="editQuestion" @delete="confirmDeleteQuestion" />
                </el-tab-pane>
                <el-tab-pane label="判断题" name="judgement">
                    <QuestionTable :questions="judgementQuestions" @edit="editQuestion"
                        @delete="confirmDeleteQuestion" />
                </el-tab-pane>
                <el-tab-pane label="填空题" name="fillblank">
                    <QuestionTable :questions="fillBlankQuestions" @edit="editQuestion"
                        @delete="confirmDeleteQuestion" />
                </el-tab-pane>
                <el-tab-pane label="问答题" name="essay">
                    <QuestionTable :questions="essayQuestions" @edit="editQuestion" @delete="confirmDeleteQuestion" />
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- 题库数据分析（仅编辑模式显示） -->
        <div class="data-analysis" v-if="isEditMode && !isEditingBankInfo">
            <h3 style="margin-bottom: 10px;">题库数据分析</h3>
            <BankStatsPanel :stats="bankStats" />
        </div>

        <!-- 试题编辑对话框 -->
        <QuestionDialog v-model="dialogVisible" :question="currentQuestion" :dialogTitle="dialogTitle"
            @submit="handleQuestionSubmit" />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from '@/utils/axios'
import QuestionTable from './components/questionTable.vue'
import QuestionDialog from './components/QuestionDialog.vue'
import BankStatsPanel from './components/BankStatsPanel.vue'

const route = useRoute()
const router = useRouter()

// 调试信息
console.log('当前路由参数:', route.params)
console.log('当前查询参数:', route.query)

// 状态管理
const isEditMode = computed(() => {
    // 同时检查 params 和 query 中的 id
    const id = route.params.id || route.query.id
    return !!id && id !== 'new' // 排除明确的新建标识
})
const isEditingBankInfo = ref(!isEditMode.value)
const savingBank = ref(false)
const loadingCourses = ref(false)

// 表单数据
const bankForm = ref({
    name: '',
    description: '',
    visibility: 0, // 默认私有
    courses: [],
    tags: []
})

// 标签管理
const inputVisible = ref(false)
const inputValue = ref('')
const tagInput = ref(null)

// 课程数据
const allCourses = ref([])

// 试题数据
const activeQuestionTab = ref('choice')
const choiceQuestions = ref([])
const judgementQuestions = ref([])
const fillBlankQuestions = ref([])
const essayQuestions = ref([])
const bankStats = ref({})

// 对话框控制
const dialogVisible = ref(false)
const dialogTitle = ref('')
const currentQuestion = ref(initEmptyQuestion())

// 获取当前题库ID
const getBankId = () => {
    return route.params.id || route.query.id
}

// 初始化空试题
function initEmptyQuestion() {
    return {
        id: null,
        type: '',
        content: '',
        options: [],
        answer: '',
        difficulty: 3,
        analysis: ''
    }
}

// 获取课程列表
const fetchCourses = async () => {
    try {
        loadingCourses.value = true
        const res = await axios.get('/courses/list')
        allCourses.value = res.data.data || []
    } catch (error) {
        ElMessage.error(`获取课程失败: ${error.message}`)
    } finally {
        loadingCourses.value = false
    }
}

// 分类试题
const classifyQuestions = (responseData) => {
    const questions = ensureArray(responseData)

    choiceQuestions.value = questions.filter(q =>
        q.type && ['single_choice', 'multiple_choice'].includes(q.type)
    )
    judgementQuestions.value = questions.filter(q => q.type === 'judgment')
    fillBlankQuestions.value = questions.filter(q => q.type === 'fill_blank')
    essayQuestions.value = questions.filter(q => q.type === 'essay')
}

const ensureArray = (data) => {
    if (!data) return []
    if (Array.isArray(data)) return data
    if (Array.isArray(data.results)) return data.results
    if (Array.isArray(data.data)) return data.data
    return []
}


// 显示试题对话框
const showQuestionDialog = (type) => {
    currentQuestion.value = {
        ...initEmptyQuestion(),
        type: type === 'choice' ? 'single_choice' :
            type === 'judgement' ? 'judgment' :
                type === 'fillblank' ? 'fill_blank' :
                    'essay'
    }
    dialogTitle.value = `添加${getQuestionTypeName(type)}`
    dialogVisible.value = true
}

// 处理试题提交
const handleQuestionSubmit = async (questionData) => {
    try {
        const bankId = getBankId()
        const url = questionData.id
            ? `/question-banks/${bankId}/questions/${questionData.id}`
            : `/question-banks/${bankId}/questions/`

        const method = questionData.id ? 'put' : 'post'

        // 处理答案格式
        const payload = {
            ...questionData,
            // 如果是选择题，将答案转换为字符串格式
            answer: Array.isArray(questionData.answer)
                ? questionData.answer.join(',')
                : questionData.answer
        }

        const res = await axios[method](url, payload)
        ElMessage.success(questionData.id ? '试题更新成功' : '试题添加成功')
        dialogVisible.value = false
        await fetchBankDetail(bankId)
    } catch (error) {
        ElMessage.error(`保存试题失败: ${error.message}`)
    }
}

// 获取题库详情
const fetchBankDetail = async (id) => {
    try {
        const [bankRes, questionsRes] = await Promise.all([
            axios.get(`/question-banks/${id}/`),
            axios.get(`/question-banks/${id}/questions/`)
        ])

        // 处理题库信息
        bankForm.value = {
            ...bankRes.data,
            courses: bankRes.data.courses || [],
            tags: bankRes.data.tags || []
        }

        // 处理问题数据
        classifyQuestions(questionsRes.data)

        // 获取统计信息
        await fetchBankStats(id)
    } catch (error) {
        console.error('加载题库失败:', {
            error: error.message,
            response: error.response?.data
        })
        ElMessage.error(`加载题库失败: ${error.response?.data?.message || error.message}`)
        router.push('/my-question-banks')
    }
}

// 获取题库统计信息
const fetchBankStats = async (id) => {
    try {
        const res = await axios.get(`/question-banks/${id}/stats`)
        bankStats.value = res.data.data || {}
    } catch (error) {
        ElMessage.error(`获取统计信息失败: ${error.message}`)
    }
}

// 保存题库信息
const saveBankInfo = async () => {
    if (!bankForm.value.name.trim()) {
        return ElMessage.error('题库名称不能为空')
    }

    savingBank.value = true
    try {
        const bankId = getBankId()
        const url = isEditMode.value
            ? `/question-banks/${bankId}`
            : '/question-banks'

        const method = isEditMode.value ? 'put' : 'post'

        const res = await axios[method](url, bankForm.value)

        ElMessage.success(isEditMode.value ? '题库已更新' : '题库创建成功')

        if (!isEditMode.value) {
            // 新建成功后跳转到编辑页
            router.push({
                path: `/questionbank-edit/${res.data.id}`,
                query: { id: res.data.id }
            })
        } else {
            isEditingBankInfo.value = false
            // 刷新数据
            await fetchBankDetail(bankId)
        }
    } catch (error) {
        ElMessage.error(`保存失败: ${error.response?.data?.message || error.message}`)
    } finally {
        savingBank.value = false
    }
}

// 开始编辑题库信息
const startEditingBankInfo = () => {
    isEditingBankInfo.value = true
}

// 取消编辑题库信息
const cancelEditBankInfo = () => {
    if (isEditMode.value) {
        isEditingBankInfo.value = false
        // 重新加载原始数据
        fetchBankDetail(getBankId())
    } else {
        router.push('/my-question-banks')
    }
}

// --- 标签管理方法 ---
const removeTag = (tag) => {
    bankForm.value.tags = bankForm.value.tags.filter(t => t !== tag)
}

const showInput = () => {
    inputVisible.value = true
    nextTick(() => {
        tagInput.value.focus()
    })
}

const addTag = () => {
    if (inputValue.value && !bankForm.value.tags.includes(inputValue.value)) {
        bankForm.value.tags.push(inputValue.value)
        inputValue.value = ''
    }
    inputVisible.value = false
}

// 获取课程名称
const getCourseName = (courseId) => {
    const course = allCourses.value.find(c => c.id === courseId)
    return course ? course.title : '未知课程'
}

const getQuestionTypeName = (type) => {
    const typeNames = {
        'choice': '选择题',
        'judgement': '判断题',
        'fillblank': '填空题',
        'essay': '问答题'
    }
    return typeNames[type] || '试题'
}

// 初始化加载
onMounted(async () => {
    await fetchCourses()
    const bankId = getBankId()
    if (bankId) {
        await fetchBankDetail(bankId)
    }
})
</script>

<style scoped>
.questionbank-edit {
    padding: 0 60px 40px 60px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.bank-info-section {
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #606266;
}

.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.custom-tag {
    border-radius: 12px;
    padding: 0 12px;
    height: 28px;
    line-height: 26px;
    background-color: #f0f4ff;
    border-color: #d9e0ff;
    color: #3a6bff;
}

.add-tag-btn {
    border-radius: 12px;
    padding: 0 12px;
    height: 28px;
    line-height: 26px;
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
}

.question-management {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.data-analysis {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
}
</style>