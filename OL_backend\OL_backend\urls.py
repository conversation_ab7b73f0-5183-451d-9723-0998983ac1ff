"""
URL configuration for OL_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

"""
URL configuration for OL_backend project.
"""

from django.contrib import admin
from django.urls import path
from app.views import (
    # 登录、注册
    LoginView, RegisterView,
    # OSS配置
    OssUploadView,
    # （教师）课程相关
    MinimalCourseListView, TeacherCourseView, CourseDetailView, CourseStatusView, CourseBatchCreateView, CourseBatchUpdateView,
    CourseChapterView, CourseCarouselListView, CourseCreateView, CourseCategoryListView, CourseChapterView, CourseMaterialView,
    # 教师管理学生账号
    StudentListView, ResetPasswordView, ToggleAccountStatusView,
    # （学生）课程相关
    StudentCourseView, StudentCourseStudyView, StudentCourseEnrollView,
    CourseCommentView, CommentLikeView, CourseFavoriteView, CourseMaterialDownloadView,
    # 题库
    PublicQuestionBankView, QuestionBankDetailView, QuestionBankQuestionsView, QuestionBankStatsView,
    MyQuestionBankView, QuestionBankCreateView, QuestionBankUpdateView, QuestionBankDeleteView,
    # 试卷、考试
    ExamPaperListView, ExamPaperDetailView,
    ExamPaperPublishView, ExamPaperUnpublishView,
    RandomQuestionView, ExamListView, ExamDetailView,
    TeacherListView, ClassListView,
    # 学生考试相关
    StudentExamListView, StudentExamDetailView, StudentExamSubmitView,

    CourseListView, RemoveRecommendLevelView, CourseSearchView, AddRecommendLevelView, NotificationListCreateAPIView, NotificationRetrieveUpdateDestroyAPIView,
    # 用户个人信息
    UserProfileView
)

# urls.py
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/login/', LoginView.as_view(), name='login'),
    path('api/register/', RegisterView.as_view(), name='register'),
    path('api/courses/list', MinimalCourseListView.as_view(), name='course-list'),
    
    # 课程相关URL
    path('api/courses/', CourseCreateView.as_view(), name='course-create'),
    path('api/courses_list/', CourseListView.as_view(), name='course-list'),
    path('api/courses/<int:course_id>/', CourseDetailView.as_view(), name='course-detail'),
    path('api/courses/<int:course_id>/status/', CourseStatusView.as_view(), name='course-status'),
    
    # 管理员管理系统
    path(
        'api/courses/<int:course_id>/remove-recommend/', 
        RemoveRecommendLevelView.as_view(), 
        name='remove-recommend-level'
    ),
    path(
        'api/courses/<int:course_id>/add-recommend/', 
        AddRecommendLevelView.as_view(), 
        name='add-recommend-level'
    ),
    path('api/courses/search/', CourseSearchView.as_view(), name='course-search'),
    path('api/notifications/', NotificationListCreateAPIView.as_view(), name='notification-list-create'),
    path('api/notifications/<int:pk>/', NotificationRetrieveUpdateDestroyAPIView.as_view(), name='notification-retrieve-update-destroy'),
    
    # 教师课程管理
    path('api/teacher/courses/', TeacherCourseView.as_view(), name='teacher-courses'),
   
    # 教师学生账号管理
    path('api/students/info', StudentListView.as_view(), name='student-info'),
    path('api/students/<int:user_id>/reset-password/', ResetPasswordView.as_view(), name='reset-password'),
    path('api/students/<int:user_id>/toggle-status/', ToggleAccountStatusView.as_view(), name='toggle-status'),
   
    # 学生课程管理
    path('api/student/courses/', StudentCourseView.as_view(), name='student-courses'),
    path('api/student/courses/<int:course_id>/study/', StudentCourseStudyView.as_view(), name='student-course-study'),
    path('api/student/courses/<int:course_id>/enroll/', StudentCourseEnrollView.as_view(), name='student-course-enroll'),
   
    # 章节管理
    path('api/courses/<int:course_id>/chapters/', CourseChapterView.as_view(), name='course-chapters'),
    
    # 轮播图管理
    path('api/courses/<int:course_id>/carousels/', CourseCarouselListView.as_view(), name='course-carousels'),
    
    # 批量课程操作
    path('api/courses/batch-create/', CourseBatchCreateView.as_view(), name='course-batch-create'),
    path('api/courses/<int:course_id>/batch-update/', CourseBatchUpdateView.as_view(), name='course-batch-update'),
    
    # 课件管理
    path('api/chapters/<int:chapter_id>/materials/', CourseMaterialView.as_view(), name='material-list'),
    path('api/materials/<int:material_id>/', CourseMaterialView.as_view(), name='material-detail'),
    
    # 文件上传
    path('api/upload/<str:upload_type>/', OssUploadView.as_view(), name='oss-upload'),
    
    # 课程分类
    path('api/course-categories/', CourseCategoryListView.as_view(), name='course-category-list'),
    
    # 评论相关
    path('api/courses/<int:course_id>/comments/', CourseCommentView.as_view(), name='course-comments'),
    path('api/comments/<int:comment_id>/like/', CommentLikeView.as_view(), name='comment-like'),
    
    # 收藏相关
    path('api/courses/<int:course_id>/favorite/', CourseFavoriteView.as_view(), name='course-favorite'),
    path('api/favorites/', CourseFavoriteView.as_view(), name='user-favorites'),
    
    # 课件下载
    path('api/materials/<int:material_id>/download/', CourseMaterialDownloadView.as_view(), name='material-download'),
    path('api/materials/batch-download/', CourseMaterialDownloadView.as_view(), name='materials-batch-download'),
    
    # 公开题库获取
    path('api/question-banks/public/', PublicQuestionBankView.as_view(), name='public-question-banks'),

    # 题库相关路由
    path('api/question-banks/<int:bank_id>/', QuestionBankDetailView.as_view(), name='question-bank-detail'), # 题库详情
    path('api/question-banks/<int:bank_id>/questions/', QuestionBankQuestionsView.as_view(), name='question-bank-questions'), # 题库试题
    path('api/question-banks/<int:bank_id>/stats/', QuestionBankStatsView.as_view(), name='question-bank-stats'), # 题库数据分析
    
    # 我的题库
    path('api/question-banks/mine', MyQuestionBankView.as_view(), name='my-question-banks'),
    path('api/question-banks', QuestionBankCreateView.as_view(), name='create-question-bank'),
    path('api/question-banks/<int:bank_id>', QuestionBankUpdateView.as_view(), name='update-question-bank'),
    path('api/question-banks/<int:bank_id>/delete', QuestionBankDeleteView.as_view(), name='delete-question-bank'),
    
    # 试卷相关
    path('api/exam-papers/', ExamPaperListView.as_view(), name='exam-paper-list'),
    path('api/exam-papers/<int:pk>/', ExamPaperDetailView.as_view(), name='exam-paper-detail'),
    path('api/exam-papers/<int:pk>/publish/', ExamPaperPublishView.as_view(), name='exam-paper-publish'),
    path('api/exam-papers/<int:pk>/unpublish/', ExamPaperUnpublishView.as_view(), name='exam-paper-unpublish'),
    path('api/questions/random/', RandomQuestionView.as_view(), name='random-questions'),
    
    # 考试相关
    path('api/exams/', ExamListView.as_view(), name='exam-list'),
    path('api/exams/<int:pk>/', ExamDetailView.as_view(), name='exam-detail'),

    # 学生考试相关
    path('api/student/exams/', StudentExamListView.as_view(), name='student-exam-list'),
    path('api/student/exams/<int:exam_id>/', StudentExamDetailView.as_view(), name='student-exam-detail'),
    path('api/student/exams/<int:exam_id>/submit/', StudentExamSubmitView.as_view(), name='student-exam-submit'),

    path('api/teachers/', TeacherListView.as_view(), name='teacher-list'),
    path('api/classes/', ClassListView.as_view(), name='class-list'),
    
    # 用户个人信息
    path('api/user/profile/', UserProfileView.as_view(), name='user-profile'),
]