:root {
  /* 主色调及其衍生 */
  --primary-color: #409EFF;
  --primary-light-1: #53a8ff;
  --primary-light-2: #66b1ff;
  --primary-light-3: #79bbff;
  --primary-light-4: #8cc5ff;
  --primary-light-5: #9fcfff;
  --primary-light-6: #b2d8ff;
  --primary-light-7: #c5e2ff;
  --primary-light-8: #d8ebff;
  --primary-light-9: #ebf5ff;
  --primary-dark-1: #3a8ee6;
  --primary-dark-2: #337ecc;
  
  /* 功能色 */
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  /* 文字颜色 */
  --text-primary: #303133;    /* 主要文字 */
  --text-regular: #606266;    /* 常规文字 */
  --text-secondary: #909399;  /* 次要文字 */
  --text-placeholder: #c0c4cc; /* 占位文字 */
  
  /* 背景色 */
  --bg-page: #f5f7fa;         /* 页面背景 */
  --bg-container: #ffffff;    /* 容器背景 */
  --bg-card: #ffffff;         /* 卡片背景 */
  --bg-header: #ffffff;       /* 头部背景 */
  --bg-footer: #fafafa;       /* 底部背景 */
  --bg-aside: #f0f2f5;        /* 侧边栏背景 */
  --bg-hover: #f5f7fa;        /* 悬停背景 */
  
  /* 边框色 */
  --border-base: #dcdfe6;     /* 基础边框 */
  --border-light: #e4e7ed;    /* 浅色边框 */
  --border-lighter: #ebeef5;  /* 更浅边框 */
  --border-extra-light: #f2f6fc; /* 最浅边框 */
  
  /* 状态色 */
  --active-state: #ebf5ff;    /* 激活状态 */
  --disabled-bg: #f5f7fa;     /* 禁用背景 */
  --disabled-text: #c0c4cc;   /* 禁用文字 */
  
  /* 按钮色 */
  --btn-primary: var(--primary-color);
  --btn-primary-hover: var(--primary-light-1);
  --btn-primary-active: var(--primary-dark-1);
  --btn-text: var(--text-regular);
  --btn-text-hover: var(--primary-color);
  
  /* 课程相关色 */
  --course-card-border: var(--border-extra-light);
  --course-tag-bg: var(--primary-light-8);
  --course-progress: var(--primary-color);
  
  /* 交互状态 */
  --link-color: var(--primary-color);
  --link-hover: var(--primary-light-1);
  --icon-color: var(--text-secondary);
  --icon-hover: var(--primary-color);
}
