#!/usr/bin/env python
"""
创建更多类型题目的脚本 - 包括填空题、选择题、解答题
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OL_backend.settings')
django.setup()

from app.models import User, Course, QuestionBank, Question, ExamPaper, PaperQuestion, Exam

def create_comprehensive_exam():
    """创建包含多种题型的综合考试"""
    
    # 获取或创建教师用户
    teacher, created = User.objects.get_or_create(
        username='teacher_test',
        defaults={
            'password': 'password123',
            'role': 'teacher',
            'status': 1
        }
    )
    
    # 获取或创建课程
    course, created = Course.objects.get_or_create(
        title='Python程序设计',
        defaults={
            'teacher_id': teacher.username,
            'category_id': 1,
            'description': 'Python程序设计基础课程',
            'status': 'published'
        }
    )
    
    # 获取或创建题库
    question_bank, created = QuestionBank.objects.get_or_create(
        name='Python程序设计题库',
        defaults={
            'description': 'Python程序设计综合题库',
            'teacher_id': teacher.username,
            'visibility': QuestionBank.PUBLIC
        }
    )
    
    # 定义各种类型的题目
    questions_data = [
        # 单选题
        {
            'type': 'single_choice',
            'content': 'Python中哪个关键字用于定义函数？',
            'options': ['function', 'def', 'define', 'func'],
            'answer': 'B',
            'score': 5,
            'difficulty': 1
        },
        {
            'type': 'single_choice',
            'content': '以下哪个不是Python的数据类型？',
            'options': ['list', 'tuple', 'array', 'dict'],
            'answer': 'C',
            'score': 5,
            'difficulty': 2
        },
        {
            'type': 'single_choice',
            'content': 'Python中用于处理异常的关键字是？',
            'options': ['catch', 'try', 'handle', 'exception'],
            'answer': 'B',
            'score': 5,
            'difficulty': 2
        },
        
        # 多选题
        {
            'type': 'multiple_choice',
            'content': '以下哪些是Python的内置数据类型？（多选）',
            'options': ['int', 'float', 'string', 'boolean'],
            'answer': ['A', 'B', 'C', 'D'],
            'score': 8,
            'difficulty': 2
        },
        {
            'type': 'multiple_choice',
            'content': 'Python中哪些语句可以用于循环？（多选）',
            'options': ['for', 'while', 'loop', 'repeat'],
            'answer': ['A', 'B'],
            'score': 8,
            'difficulty': 2
        },
        
        # 判断题
        {
            'type': 'judgment',
            'content': 'Python是一种解释型编程语言。',
            'options': [],
            'answer': 'T',
            'score': 3,
            'difficulty': 1
        },
        {
            'type': 'judgment',
            'content': 'Python中的列表是不可变的数据类型。',
            'options': [],
            'answer': 'F',
            'score': 3,
            'difficulty': 2
        },
        {
            'type': 'judgment',
            'content': 'Python支持面向对象编程。',
            'options': [],
            'answer': 'T',
            'score': 3,
            'difficulty': 1
        },
        
        # 填空题
        {
            'type': 'fill_blank',
            'content': '在Python中，使用 _____ 关键字可以导入模块。',
            'options': [],
            'answer': 'import',
            'score': 6,
            'difficulty': 1
        },
        {
            'type': 'fill_blank',
            'content': 'Python中创建字典的语法是使用 _____ 符号包围键值对。',
            'options': [],
            'answer': '{}',
            'score': 6,
            'difficulty': 2
        },
        {
            'type': 'fill_blank',
            'content': '在Python中，_____ 函数用于获取列表的长度。',
            'options': [],
            'answer': 'len',
            'score': 6,
            'difficulty': 1
        },
        
        # 简答题/解答题
        {
            'type': 'short_answer',
            'content': '请简述Python中列表(list)和元组(tuple)的区别。',
            'options': [],
            'answer': '列表是可变的数据类型，可以修改其中的元素；元组是不可变的数据类型，创建后不能修改。列表使用[]定义，元组使用()定义。',
            'score': 10,
            'difficulty': 2
        },
        {
            'type': 'short_answer',
            'content': '解释Python中的缩进规则及其重要性。',
            'options': [],
            'answer': 'Python使用缩进来表示代码块，而不是使用大括号。缩进必须一致，通常使用4个空格。缩进决定了代码的逻辑结构和执行顺序。',
            'score': 10,
            'difficulty': 2
        },
        

    ]
    
    # 创建题目
    questions = []
    for q_data in questions_data:
        question, created = Question.objects.get_or_create(
            bank=question_bank,
            content=q_data['content'],
            defaults={
                'type': q_data['type'],
                'options': q_data['options'],
                'answer': q_data['answer'],
                'score': q_data['score'],
                'difficulty': q_data['difficulty']
            }
        )
        questions.append(question)
        if created:
            print(f"创建题目: {question.content[:30]}...")
    
    # 创建试卷
    exam_paper, created = ExamPaper.objects.get_or_create(
        title='Python程序设计综合测试',
        defaults={
            'description': 'Python程序设计综合能力测试，包含选择题、填空题和简答题',
            'teacher': teacher,
            'status': 'published',
            'total_score': sum(q['score'] for q in questions_data),
            'difficulty': 2.5
        }
    )
    
    # 添加题目到试卷
    for i, question in enumerate(questions):
        paper_question, created = PaperQuestion.objects.get_or_create(
            paper=exam_paper,
            question=question,
            defaults={
                'order_num': i + 1,
                'score': question.score
            }
        )
    
    # 创建考试
    now = timezone.now()
    start_time = now - timedelta(hours=1)  # 1小时前开始
    end_time = now + timedelta(hours=3)    # 3小时后结束
    
    exam, created = Exam.objects.get_or_create(
        title='Python程序设计期末考试',
        course=course,
        defaults={
            'teacher': teacher,
            'paper': exam_paper,
            'description': 'Python程序设计期末综合考试，包含多种题型，全面考查Python基础知识',
            'start_time': start_time,
            'end_time': end_time,
            'duration_minutes': 120,  # 2小时
            'pass_score': 60,
            'total_score': exam_paper.total_score,
            'status': Exam.ONGOING,
            'shuffle_questions': True,
            'shuffle_options': True,
            'allow_break': False
        }
    )
    
    print(f"\n=== 综合考试创建成功！===")
    print(f"考试ID: {exam.id}")
    print(f"考试标题: {exam.title}")
    print(f"课程ID: {course.id}")
    print(f"课程标题: {course.title}")
    print(f"试卷ID: {exam_paper.id}")
    print(f"试卷标题: {exam_paper.title}")
    print(f"总题数: {len(questions)}")
    print(f"总分: {exam_paper.total_score}")
    print(f"及格分: {exam.pass_score}")
    print(f"考试时长: {exam.duration_minutes} 分钟")
    print(f"考试状态: {exam.get_status_display()}")
    print(f"开始时间: {exam.start_time}")
    print(f"结束时间: {exam.end_time}")
    
    # 统计题型分布
    type_count = {}
    for q in questions_data:
        type_name = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'judgment': '判断题',
            'fill_blank': '填空题',
            'short_answer': '简答题'
        }.get(q['type'], q['type'])

        type_count[type_name] = type_count.get(type_name, 0) + 1
    
    print(f"\n=== 题型分布 ===")
    for type_name, count in type_count.items():
        print(f"{type_name}: {count} 题")

if __name__ == '__main__':
    create_comprehensive_exam()
