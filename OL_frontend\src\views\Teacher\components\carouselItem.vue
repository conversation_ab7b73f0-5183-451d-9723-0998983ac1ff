<template>
  <div 
    class="carousel-item-component" 
    draggable="true" 
    @dragstart="handleDragStart" 
    @dragover="handleDragOver" 
    @drop="handleDrop($event, index)"
  >
    <img :src="image" alt="轮播图图片" class="carousel-image">
    <div class="button-group">
      <el-button type="primary" size="small" @click="handleSetAsHome">设为首页轮播图</el-button>
      <el-button type="danger" size="small" @click="handleDelete">删除</el-button>
    </div>
  </div>
</template>

<script setup>
import { ElButton, ElMessageBox } from 'element-plus'
import { ref } from 'vue'

const props = defineProps({
  image: {
    type: String,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emits = defineEmits([
  'setAsHome',   // 设置为首页轮播图
  'deleteImage', // 删除当前图片
  'drop'         // 拖拽排序
])

const dragIndex = ref(null)

const handleDragStart = () => {
  dragIndex.value = props.index
}

const handleDragOver = (e) => {
  e.preventDefault() // 阻止默认行为，允许 drop
}

const handleDrop = (e, targetIndex) => {
  e.preventDefault()
  e.stopPropagation() // 阻止事件冒泡
  
  if (dragIndex.value !== null && dragIndex.value !== targetIndex) {
    emits('drop', dragIndex.value, targetIndex)
  }
  dragIndex.value = null
}

const handleSetAsHome = () => {
  emits('setAsHome', props.image)
}

const handleDelete = () => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emits('deleteImage', props.index)
  }).catch(() => {
    // 用户取消，不执行任何操作
  })
}
</script>

<style scoped>
.carousel-item-component {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  width: 220px;
  height: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.carousel-item-component:hover {
  transform: translateY(-5px);
}

.carousel-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 15px;
}

.button-group {
  display: flex;
  gap: 8px;
  width: 100%;
  justify-content: center;
}
</style>