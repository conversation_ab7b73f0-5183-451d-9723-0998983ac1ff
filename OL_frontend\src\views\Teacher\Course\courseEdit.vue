<template>
    <div class="course-edit">
        <el-card v-loading="loading">
            <template #header>
                <div class="card-header">
                    <h1 style="margin-bottom: 10px;">编辑课程</h1>
                    <el-button @click="router.back()">返回</el-button>
                </div>
            </template>

            <CourseForm v-if="initialData" ref="courseForm" :initial-data="initialData" :is-edit-mode="true"
                :teacher-id="currentTeacherId" @submit="handleSubmit" @cancel="router.back()" />
            <div v-else class="loading-state">
                <el-empty description="加载中..." />
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'
import CourseForm from '@/views/Teacher/components/courseForm.vue'

const route = useRoute()
const router = useRouter()
const courseId = route.params.courseId
const loading = ref(false)
const initialData = ref(null)
const currentTeacherId = ref('2022150024')

const fetchCourseData = async () => {
    try {
        loading.value = true
        const [courseRes, chaptersRes, carouselsRes] = await Promise.all([
            axios.get(`/courses/${courseId}/`),
            axios.get(`/courses/${courseId}/chapters/`),
            axios.get(`/courses/${courseId}/carousels/`)
        ])

        initialData.value = {
            id: courseId,
            ...courseRes.data.data,
            chapters: chaptersRes.data.data || [],
            carousels: carouselsRes.data.data || []
        }
    } catch (error) {
        ElMessage.error('获取课程数据失败: ' + (error.response?.data?.message || error.message))
        router.back()
    } finally {
        loading.value = false
    }
}

const handleSubmit = () => {
    router.push('/course-manage')
}

onMounted(fetchCourseData)
</script>
<style scoped>
.course-edit {
    padding: 0 60px 40px 60px;
}

.loading-state {
    padding: 40px 0;
}
</style>