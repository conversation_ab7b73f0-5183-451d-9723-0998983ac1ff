<template>
    <el-card class="paper-card" :shadow="hoverShadow">
        <div class="card-header">
            <div class="status-tag" :class="getStatusClass(paper.status)">
                {{ getStatusText(paper.status) }}
            </div>
            <h3 class="paper-name">{{ paper.name }}</h3>
        </div>
        <div class="card-content">
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">试卷编号：</span>
                    <span class="value">{{ paper.code }}</span>
                </div>
                <div class="info-item">
                    <span class="label">题目数量：</span>
                    <span class="value">{{ paper.questionCount }} 道</span>
                </div>
                <div class="info-item">
                    <span class="label">总分值：</span>
                    <span class="value">{{ paper.totalScore }} 分</span>
                </div>
                <div class="info-item">
                    <span class="label">创建时间：</span>
                    <span class="value">{{ paper.createTime }}</span>
                </div>
                <div class="info-item">
                    <span class="label">考试时长：</span>
                    <span class="value">{{ paper.duration }} 分钟</span>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <el-button type="primary" size="small" @click="editPaper(paper.id)" v-if="paper.status === 'draft'">
                <el-icon>
                    <Edit />
                </el-icon> 继续编辑
            </el-button>
            <el-button type="success" size="small" @click="publishPaper(paper.id)" v-if="paper.status === 'draft'">
                <el-icon>
                    <Check />
                </el-icon> 发布试卷
            </el-button>
            <el-button type="info" size="small" @click="viewPaper(paper.id)" v-if="paper.status === 'published'">
                <el-icon>
                    <Eye />
                </el-icon> 查看详情
            </el-button>
            <el-button type="warning" size="small" @click="unpublishPaper(paper.id)"
                v-if="paper.status === 'published'">
                <el-icon>
                    <Close />
                </el-icon> 取消发布
            </el-button>
            <el-button type="danger" size="small" @click="deletePaper(paper.id)" v-if="paper.status === 'draft'">
                <el-icon>
                    <Delete />
                </el-icon> 删除
            </el-button>
        </div>
    </el-card>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'

const props = defineProps({ paper: Object })
const router = useRouter()
const hoverShadow = ref('hover')

const getStatusText = (status) => ({
    draft: '草稿',
    published: '已发布'
}[status] || status)

const getStatusClass = (status) => ({
    draft: 'bg-red-50 text-red-600',
    published: 'bg-blue-50 text-blue-600'
}[status] || 'bg-gray-50 text-gray-600')

const editPaper = (id) => {
    router.push('./paper-edit')
}

const viewPaper = (id) => {
  router.push("./paper-detail")  // 传递试卷ID
}

const publishPaper = (id) => {
    ElMessageBox.confirm('确定要发布此试卷吗？发布后将无法编辑题目', '发布确认', {
        confirmButtonText: '确认发布',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        props.paper.status = 'published'
        ElMessage.success('试卷发布成功！')
    })
}

const unpublishPaper = (id) => {
    ElMessageBox.confirm('确定要取消发布此试卷吗？将退回草稿箱', '取消发布确认', {
        confirmButtonText: '确认取消',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        props.paper.status = 'draft'
        ElMessage.success('试卷已退回草稿箱！')
    })
}

const deletePaper = (id) => {
    ElMessageBox.confirm('确定要删除此试卷吗？删除后无法恢复', '删除确认', {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'danger'
    }).then(() => {
        // 模拟API调用
        setTimeout(() => {
            ElMessage.success('试卷已删除！')
            // 触发自定义事件通知父组件
            document.dispatchEvent(new CustomEvent('delete-paper', { detail: id }))
        }, 500)
    })
}
</script>