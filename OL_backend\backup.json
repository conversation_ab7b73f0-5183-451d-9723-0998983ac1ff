[{"model": "app.user", "pk": 1, "fields": {"last_login": null, "username": "2022150070", "password": "xh12345678", "role": "admin", "status": 1, "create_time": "2020-09-01T00:00:00Z", "update_time": "2020-09-01T00:00:00Z"}}, {"model": "app.user", "pk": 2, "fields": {"last_login": null, "username": "**********", "password": "zys123123", "role": "teacher", "status": 1, "create_time": "2020-09-01T00:00:00Z", "update_time": "2020-09-01T00:00:00Z"}}, {"model": "app.user", "pk": 3, "fields": {"last_login": null, "username": "2023150116", "password": "lzh12345678", "role": "student", "status": 1, "create_time": "2020-09-01T00:00:00Z", "update_time": "2020-09-01T00:00:00Z"}}, {"model": "app.user", "pk": 4, "fields": {"last_login": "2025-06-14T13:17:22.520Z", "username": "1234567878", "password": "12345678aa", "role": "student", "status": 1, "create_time": "2025-06-14T13:17:22.520Z", "update_time": "2025-06-14T13:17:22.520Z"}}, {"model": "app.user", "pk": 5, "fields": {"last_login": null, "username": "**********", "password": "teacher01", "role": "teacher", "status": 1, "create_time": "2025-06-14T22:20:43Z", "update_time": "2025-06-14T22:20:51Z"}}, {"model": "app.user", "pk": 6, "fields": {"last_login": null, "username": "**********", "password": "teacher02", "role": "teacher", "status": 1, "create_time": "2025-06-14T22:20:46Z", "update_time": "2025-06-14T22:20:54Z"}}, {"model": "app.user", "pk": 7, "fields": {"last_login": "2025-06-15T00:20:34.926Z", "username": "1234567890", "password": "1111aaaa", "role": "student", "status": 1, "create_time": "2025-06-15T00:20:34.926Z", "update_time": "2025-06-15T00:20:34.927Z"}}, {"model": "app.user", "pk": 8, "fields": {"last_login": null, "username": "student1", "password": "password123", "role": "student", "status": 1, "create_time": "2025-06-15T12:52:01.232Z", "update_time": "2025-06-15T12:52:01.232Z"}}, {"model": "app.coursecategory", "pk": 1, "fields": {"name": "计算机科学", "status": true, "create_time": "2019-09-01T12:00:00Z", "update_time": "2019-09-01T12:00:00Z"}}, {"model": "app.coursecategory", "pk": 2, "fields": {"name": "数学", "status": true, "create_time": "2019-09-01T12:00:00Z", "update_time": "2019-09-01T12:00:00Z"}}, {"model": "app.coursecategory", "pk": 3, "fields": {"name": "物理", "status": true, "create_time": "2019-09-01T12:00:00Z", "update_time": "2019-09-01T12:00:00Z"}}, {"model": "app.course", "pk": 1, "fields": {"teacher_id": "**********", "category": 1, "title": "C++程序设计", "description": "课程注重培养学生的编程思维与算法设计能力，引导学生学会将实际问题转化为计算机可处理的算法与程序。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/d3edab38-6b4a-4b96-9c40-b30db54fde13.png", "status": "draft", "recommend_level": 0, "favorite_count": 0, "enroll_count": 1, "create_time": "2020-09-01T12:00:00Z", "update_time": "2025-06-15T13:22:54.588Z"}}, {"model": "app.course", "pk": 2, "fields": {"teacher_id": "**********", "category": 1, "title": "C语言程序设计", "description": "C 语言程序设计课程围绕 C 语言的语法规则、数据类型、控制结构、函数等核心内容展开教学。课程通过大量编程实例，让学生掌握 C 语言编程的基本方法与技巧，能够运用 C 语言编写结构清晰、逻辑正确的程序。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/87cb0b3d-fc40-4cae-a052-58a714ed1d99.png", "status": "published", "recommend_level": 1, "favorite_count": 56, "enroll_count": 205, "create_time": "2020-09-01T12:00:00Z", "update_time": "2025-06-14T16:26:58.119Z"}}, {"model": "app.course", "pk": 6, "fields": {"teacher_id": "**********", "category": 1, "title": "数据结构", "description": "数据结构课程专注于研究数据的组织、存储与操作方法，是计算机科学的核心基础课程之一。课程深入讲解线性表、栈、队列、树、图等常见数据结构的原理、实现方式与应用场景，同时介绍排序、查找等经典算法。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/8dfdb654-db85-4b2a-8bce-a6fbd52ab446.png", "status": "published", "recommend_level": 1, "favorite_count": 102, "enroll_count": 376, "create_time": "2025-06-13T14:48:44.147Z", "update_time": "2025-06-15T13:35:41.188Z"}}, {"model": "app.course", "pk": 7, "fields": {"teacher_id": "**********", "category": 3, "title": "大学物理（2）", "description": "大学物理（2）在大学物理（1）的基础上，进一步探索电磁学、光学与近代物理领域。课程中，学生将学习麦克斯韦方程组对电磁场的精妙描述，领略光的干涉、衍射等神奇现象，还会接触到相对论与量子力学的前沿知识。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-8.png", "status": "published", "recommend_level": 0, "favorite_count": 84, "enroll_count": 238, "create_time": "2025-06-11T16:10:30Z", "update_time": "2025-06-13T16:12:11Z"}}, {"model": "app.course", "pk": 8, "fields": {"teacher_id": "**********", "category": 2, "title": "高等数学B", "description": "高等数学 B 在内容上涵盖函数与极限、一元与多元函数微积分等，相较于高等数学 A，更侧重于数学知识在实际中的应用。课程通过大量实际案例，引导学生将数学理论与工程、经济等领域的问题相结合，培养学生运用数学方法解决实际问题的能力。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-10.png", "status": "published", "recommend_level": 1, "favorite_count": 17, "enroll_count": 80, "create_time": "2025-06-13T18:19:45Z", "update_time": "2025-06-13T22:17:54Z"}}, {"model": "app.course", "pk": 13, "fields": {"teacher_id": "**********", "category": 1, "title": "数据科学导论", "description": "数据科学导论作为数据科学领域的入门课程，旨在为学生搭建起系统认识数据科学的桥梁。课程从数据科学的基本概念出发，详细介绍数据采集、存储、清洗、分析与可视化的全流程方法，涵盖统计学、机器学习、数据挖掘等核心领域的基础知识。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%8C%BB%E7%96%97%E5%88%86%E4%BC%9A%E5%9C%BA29.jpg", "status": "draft", "recommend_level": 0, "favorite_count": 0, "enroll_count": 0, "create_time": "2025-06-13T15:08:46.779Z", "update_time": "2025-06-14T16:12:03.219Z"}}, {"model": "app.course", "pk": 42, "fields": {"teacher_id": "**********", "category": 2, "title": "高等数学A", "description": "高等数学 A 是一门理论性与逻辑性极强的课程，包含函数、极限、微积分、向量代数与空间解析几何等核心内容。课程注重数学理论的严密推导与证明，深入剖析数学概念的本质。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-9.png", "status": "draft", "recommend_level": 0, "favorite_count": 0, "enroll_count": 0, "create_time": "2025-06-13T22:42:48.536Z", "update_time": "2025-06-14T17:42:42.623Z"}}, {"model": "app.course", "pk": 43, "fields": {"teacher_id": "**********", "category": 3, "title": "大学物理（1）", "description": "大学物理（1）聚焦经典物理的基础理论与核心知识，涵盖力学、热学等内容。课程从牛顿运动定律出发，深入解析物体的运动规律与相互作用，同时探讨热现象背后的微观本质与宏观规律。通过理论讲解与实验案例结合，帮助学生建立系统的物理思维，掌握基础物理知识的实际应用。", "cover_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-7.png", "status": "published", "recommend_level": 1, "favorite_count": 29, "enroll_count": 174, "create_time": "2025-06-14T01:45:18.901Z", "update_time": "2025-06-15T12:49:08.275Z"}}, {"model": "app.coursecarousel", "pk": 32, "fields": {"course": 6, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/8dfdb654-db85-4b2a-8bce-a6fbd52ab446.png", "sort_order": 0, "create_time": "2025-06-13T19:25:10.001Z", "update_time": "2025-06-13T19:25:10.002Z"}}, {"model": "app.coursecarousel", "pk": 33, "fields": {"course": 6, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/13/d757be32-487f-4cde-92b2-fd162a1a48ec.png", "sort_order": 1, "create_time": "2025-06-13T19:25:10.001Z", "update_time": "2025-06-13T19:25:10.002Z"}}, {"model": "app.coursecarousel", "pk": 56, "fields": {"course": 42, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/14/64c8fc74-f304-4a47-a29d-c975ae9e7883.jpg", "sort_order": 0, "create_time": "2025-06-14T00:40:35.872Z", "update_time": "2025-06-14T00:40:35.872Z"}}, {"model": "app.coursecarousel", "pk": 57, "fields": {"course": 42, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/14/4ced5938-11a8-4df7-9536-b71447ee712e.png", "sort_order": 1, "create_time": "2025-06-14T00:40:35.872Z", "update_time": "2025-06-14T00:40:35.872Z"}}, {"model": "app.coursecarousel", "pk": 58, "fields": {"course": 42, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/14/e8302d07-189f-42f3-8b6f-c7a93ee816b8.png", "sort_order": 2, "create_time": "2025-06-14T00:40:35.872Z", "update_time": "2025-06-14T00:40:35.872Z"}}, {"model": "app.coursecarousel", "pk": 59, "fields": {"course": 42, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/14/c317324d-6bc9-42e1-a28d-ad8f2ab001b4.png", "sort_order": 3, "create_time": "2025-06-14T00:40:35.872Z", "update_time": "2025-06-14T00:40:35.872Z"}}, {"model": "app.coursecarousel", "pk": 60, "fields": {"course": 13, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/13/7de1d594-65f7-46af-ac98-c77f34856d00.png", "sort_order": 0, "create_time": "2025-06-14T16:12:03.425Z", "update_time": "2025-06-14T16:12:03.426Z"}}, {"model": "app.coursecarousel", "pk": 61, "fields": {"course": 13, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/carousels/2025/06/13/4e8d7810-7af9-45ce-9924-c71d29aa2ac9.png", "sort_order": 1, "create_time": "2025-06-14T16:12:03.426Z", "update_time": "2025-06-14T16:12:03.426Z"}}, {"model": "app.coursecarousel", "pk": 62, "fields": {"course": 1, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/d3edab38-6b4a-4b96-9c40-b30db54fde13.png", "sort_order": 0, "create_time": "2025-06-05T16:52:18Z", "update_time": "2025-06-13T16:52:34Z"}}, {"model": "app.coursecarousel", "pk": 63, "fields": {"course": 1, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%8C%BB%E7%96%97%E5%88%86%E4%BC%9A%E5%9C%BA29.jpg", "sort_order": 1, "create_time": "2025-06-15T16:53:35Z", "update_time": "2025-06-15T16:53:39Z"}}, {"model": "app.coursecarousel", "pk": 64, "fields": {"course": 2, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/87cb0b3d-fc40-4cae-a052-58a714ed1d99.png", "sort_order": 0, "create_time": "2025-06-15T16:54:26Z", "update_time": "2025-06-15T16:54:29Z"}}, {"model": "app.coursecarousel", "pk": 65, "fields": {"course": 2, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%8C%BB%E7%96%97%E5%88%86%E4%BC%9A%E5%9C%BA29.jpg", "sort_order": 1, "create_time": "2025-06-15T16:55:05Z", "update_time": "2025-06-15T16:55:09Z"}}, {"model": "app.coursecarousel", "pk": 66, "fields": {"course": 7, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-8.png", "sort_order": 0, "create_time": "2025-06-15T16:56:05Z", "update_time": "2025-06-15T16:56:09Z"}}, {"model": "app.coursecarousel", "pk": 67, "fields": {"course": 7, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%8C%BB%E7%96%97%E5%88%86%E4%BC%9A%E5%9C%BA29.jpg", "sort_order": 1, "create_time": "2025-06-15T16:56:40Z", "update_time": "2025-06-15T16:56:44Z"}}, {"model": "app.coursecarousel", "pk": 68, "fields": {"course": 8, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-10.png", "sort_order": 0, "create_time": "2025-06-15T16:57:16Z", "update_time": "2025-06-15T16:57:18Z"}}, {"model": "app.coursecarousel", "pk": 69, "fields": {"course": 43, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-7.png", "sort_order": 0, "create_time": "2025-06-15T16:58:16Z", "update_time": "2025-06-15T16:58:20Z"}}, {"model": "app.coursecarousel", "pk": 70, "fields": {"course": 43, "image_url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/covers/2025/06/13/%E5%88%B6%E4%BD%9C%E7%BD%91%E8%AF%BE%E5%B0%81%E9%9D%A2-10.png", "sort_order": 1, "create_time": "2025-06-15T16:59:00Z", "update_time": "2025-06-15T16:59:05Z"}}, {"model": "app.coursematerial", "pk": 5, "fields": {"chapter": 35, "title": "第一章课件", "type": "doc", "url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/materials/2025/06/14/a57d74b5-ad79-48a7-8c5f-bcb1b7bf069f.docx", "duration": 0, "size": 10202, "sort_order": 0, "create_time": "2025-06-14T00:40:35.844Z", "update_time": "2025-06-14T00:40:35.844Z"}}, {"model": "app.coursematerial", "pk": 7, "fields": {"chapter": 38, "title": "第二章课件", "type": "doc", "url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/materials/2025/06/14/545a1c24-0509-4d34-9707-9ec617d3b2f1.docx", "duration": 0, "size": 10202, "sort_order": 0, "create_time": "2025-06-14T02:42:12.641Z", "update_time": "2025-06-14T02:42:12.641Z"}}, {"model": "app.coursematerial", "pk": 9, "fields": {"chapter": 42, "title": "第三章课件", "type": "doc", "url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/materials/2025/06/14/01b744a0-36c9-4d0f-97e3-11f073ca75cf.docx", "duration": 0, "size": 10202, "sort_order": 0, "create_time": "2025-06-14T03:58:01.408Z", "update_time": "2025-06-14T03:58:01.408Z"}}, {"model": "app.coursematerial", "pk": 11, "fields": {"chapter": 44, "title": "思维导图", "type": "image", "url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/materials/2025/06/15/109e80c9-cc47-4a23-9be1-60ee166fbdf4.png", "duration": 0, "size": 8368, "sort_order": 0, "create_time": "2025-06-14T16:12:03.348Z", "update_time": "2025-06-14T16:12:03.348Z"}}, {"model": "app.coursematerial", "pk": 12, "fields": {"chapter": 45, "title": "第一章思维导图", "type": "image", "url": "https://ol-img.oss-cn-shenzhen.aliyuncs.com/materials/2025/06/15/900385ad-6cac-40c4-b863-bc50af18f620.png", "duration": 0, "size": 8368, "sort_order": 0, "create_time": "2025-06-14T16:26:13.798Z", "update_time": "2025-06-14T16:26:13.798Z"}}, {"model": "app.coursechapter", "pk": 35, "fields": {"course": 42, "title": "第1章", "description": "123", "sort_order": 0, "create_time": "2025-06-14T00:40:35.830Z", "update_time": "2025-06-14T00:40:35.830Z"}}, {"model": "app.coursechapter", "pk": 38, "fields": {"course": 43, "title": "第1章", "description": "初识物理", "sort_order": 0, "create_time": "2025-06-14T02:42:12.624Z", "update_time": "2025-06-14T02:42:12.624Z"}}, {"model": "app.coursechapter", "pk": 39, "fields": {"course": 43, "title": "第2章", "description": "什么是量子力学", "sort_order": 1, "create_time": "2025-06-14T02:42:12.655Z", "update_time": "2025-06-14T02:42:12.655Z"}}, {"model": "app.coursechapter", "pk": 42, "fields": {"course": 1, "title": "默认章节", "description": "", "sort_order": 0, "create_time": "2025-06-14T03:58:01.390Z", "update_time": "2025-06-14T03:58:01.390Z"}}, {"model": "app.coursechapter", "pk": 44, "fields": {"course": 13, "title": "第1章", "description": "奥奥啊", "sort_order": 0, "create_time": "2025-06-14T16:12:03.299Z", "update_time": "2025-06-14T16:12:03.299Z"}}, {"model": "app.coursechapter", "pk": 45, "fields": {"course": 2, "title": "第1章", "description": "测试", "sort_order": 0, "create_time": "2025-06-14T16:26:13.758Z", "update_time": "2025-06-14T16:26:13.758Z"}}, {"model": "app.coursecomment", "pk": 1, "fields": {"course": 2, "user": 4, "content": "真的是一门很不错的课程！", "likes": 4, "parent": null, "create_time": "2025-06-14T15:01:43.120Z", "update_time": "2025-06-14T15:19:27.126Z"}}, {"model": "app.coursecomment", "pk": 2, "fields": {"course": 2, "user": 4, "content": "@1234567878 同意楼主", "likes": 0, "parent": 1, "create_time": "2025-06-14T15:01:51.779Z", "update_time": "2025-06-14T15:01:51.779Z"}}, {"model": "app.coursecomment", "pk": 3, "fields": {"course": 2, "user": 4, "content": "@1234567878 点赞！", "likes": 0, "parent": 1, "create_time": "2025-06-14T15:02:04.721Z", "update_time": "2025-06-14T15:02:04.721Z"}}, {"model": "app.coursecomment", "pk": 4, "fields": {"course": 2, "user": 4, "content": "让人醍醐灌顶", "likes": 0, "parent": 1, "create_time": "2025-06-14T15:19:18.490Z", "update_time": "2025-06-14T15:19:18.490Z"}}, {"model": "app.coursecomment", "pk": 5, "fields": {"course": 2, "user": 4, "content": "@1234567878 我也觉得", "likes": 0, "parent": 1, "create_time": "2025-06-14T15:50:51.496Z", "update_time": "2025-06-14T15:50:51.496Z"}}, {"model": "app.coursecomment", "pk": 6, "fields": {"course": 2, "user": 4, "content": "可以非常不错\n", "likes": 0, "parent": null, "create_time": "2025-06-14T15:50:57.039Z", "update_time": "2025-06-14T15:50:57.039Z"}}, {"model": "app.coursecomment", "pk": 7, "fields": {"course": 6, "user": 7, "content": "学到了很多", "likes": 1, "parent": null, "create_time": "2025-06-15T00:21:19.950Z", "update_time": "2025-06-15T00:21:27.672Z"}}, {"model": "app.coursecomment", "pk": 8, "fields": {"course": 6, "user": 7, "content": "@1234567890 是吧是吧", "likes": 0, "parent": 7, "create_time": "2025-06-15T00:21:25.050Z", "update_time": "2025-06-15T00:21:25.050Z"}}, {"model": "app.coursecomment", "pk": 9, "fields": {"course": 2, "user": 4, "content": "感觉老师讲的节奏可以再快一点", "likes": 0, "parent": null, "create_time": "2025-06-15T03:00:23.096Z", "update_time": "2025-06-15T03:00:23.096Z"}}, {"model": "app.coursecomment", "pk": 10, "fields": {"course": 8, "user": 3, "content": "很不错", "likes": 1, "parent": null, "create_time": "2025-06-15T10:03:57.723Z", "update_time": "2025-06-15T10:04:09.869Z"}}, {"model": "app.coursecomment", "pk": 11, "fields": {"course": 7, "user": 3, "content": "物理太难了。。", "likes": 0, "parent": null, "create_time": "2025-06-15T10:22:01.838Z", "update_time": "2025-06-15T10:22:01.838Z"}}, {"model": "app.coursecomment", "pk": 12, "fields": {"course": 7, "user": 3, "content": "@2023150116 来个大神救救我吧", "likes": 0, "parent": 11, "create_time": "2025-06-15T10:22:10.929Z", "update_time": "2025-06-15T10:22:10.929Z"}}, {"model": "app.coursecomment", "pk": 13, "fields": {"course": 7, "user": 3, "content": "感觉很轻松的一门课啊", "likes": 0, "parent": null, "create_time": "2025-06-15T10:22:23.404Z", "update_time": "2025-06-15T10:22:23.405Z"}}, {"model": "app.coursecomment", "pk": 14, "fields": {"course": 8, "user": 3, "content": "学数学轻轻松松了", "likes": 0, "parent": null, "create_time": "2025-06-15T10:22:48.790Z", "update_time": "2025-06-15T10:22:48.790Z"}}, {"model": "app.coursecomment", "pk": 15, "fields": {"course": 1, "user": 3, "content": "喜欢老师讲话的节奏", "likes": 0, "parent": null, "create_time": "2025-06-15T10:25:10.134Z", "update_time": "2025-06-15T10:25:10.134Z"}}, {"model": "app.coursecomment", "pk": 16, "fields": {"course": 13, "user": 3, "content": "感觉有点难", "likes": 0, "parent": null, "create_time": "2025-06-15T10:26:04.401Z", "update_time": "2025-06-15T10:26:04.401Z"}}, {"model": "app.coursecomment", "pk": 17, "fields": {"course": 42, "user": 3, "content": "所有课程的基础👍", "likes": 0, "parent": null, "create_time": "2025-06-15T10:26:25.893Z", "update_time": "2025-06-15T10:26:25.893Z"}}, {"model": "app.coursecomment", "pk": 18, "fields": {"course": 43, "user": 3, "content": "学完了大物2才发现没学1怎么办😓", "likes": 0, "parent": null, "create_time": "2025-06-15T10:26:58.455Z", "update_time": "2025-06-15T10:26:58.455Z"}}, {"model": "app.coursecomment", "pk": 19, "fields": {"course": 1, "user": 4, "content": "@2023150116 狗叫什么", "likes": 0, "parent": 15, "create_time": "2025-06-15T12:47:05.018Z", "update_time": "2025-06-15T12:47:05.018Z"}}, {"model": "app.coursefavorite", "pk": 5, "fields": {"user": 4, "course": 2, "create_time": "2025-06-14T15:51:08.005Z"}}, {"model": "app.coursefavorite", "pk": 6, "fields": {"user": 7, "course": 6, "create_time": "2025-06-15T00:21:39.732Z"}}, {"model": "app.coursefavorite", "pk": 9, "fields": {"user": 4, "course": 43, "create_time": "2025-06-15T12:49:07.003Z"}}, {"model": "app.coursefavorite", "pk": 11, "fields": {"user": 8, "course": 43, "create_time": "2025-06-15T12:55:10.368Z"}}, {"model": "app.coursefavorite", "pk": 12, "fields": {"user": 4, "course": 6, "create_time": "2025-06-15T13:33:30.035Z"}}, {"model": "app.notification", "pk": 2, "fields": {"notification_text": "期末考试安排已公布，请查看通知。", "publish_time": "2025-03-14T14:42:39Z"}}, {"model": "app.notification", "pk": 3, "fields": {"notification_text": "test1", "publish_time": "2025-05-01T17:34:56Z"}}, {"model": "app.notification", "pk": 4, "fields": {"notification_text": "《Java程序设计基础》开课啦！欢迎选修！", "publish_time": "2025-06-15T04:38:07Z"}}, {"model": "app.notification", "pk": 5, "fields": {"notification_text": "本系统将于2025年6月21日进行常态维修，请大家做好准备，提前保存课程进度。", "publish_time": "2025-06-15T04:51:30Z"}}, {"model": "app.studentenrollcourse", "pk": 1, "fields": {"student": 4, "course": 1, "enroll_time": "2025-06-15T12:41:09.878Z", "progress": 0, "status": 1}}, {"model": "app.studentenrollcourse", "pk": 2, "fields": {"student": 4, "course": 43, "enroll_time": "2025-06-15T12:49:08.236Z", "progress": 0, "status": 1}}, {"model": "app.studentenrollcourse", "pk": 3, "fields": {"student": 3, "course": 6, "enroll_time": "2025-06-15T12:55:52.264Z", "progress": 0, "status": 1}}, {"model": "app.studentenrollcourse", "pk": 4, "fields": {"student": 4, "course": 6, "enroll_time": "2025-06-15T13:35:41.146Z", "progress": 0, "status": 1}}, {"model": "app.questionbank", "pk": 1, "fields": {"name": "高数题库 A", "description": "高等数学第一章测试题库", "teacher_id": "**********", "visibility": 1, "tags": ["数学", "导数"], "question_count": 20, "use_count": 5, "accuracy_rate": 0.85, "create_time": "2025-06-15T15:21:52Z", "update_time": "2025-06-15T15:21:52Z"}}, {"model": "app.questionbank", "pk": 2, "fields": {"name": "Python 题库", "description": "Python 编程基础练习题", "teacher_id": "**********", "visibility": 1, "tags": ["编程", "Python"], "question_count": 15, "use_count": 3, "accuracy_rate": 0.76, "create_time": "2025-06-15T15:21:52Z", "update_time": "2025-06-15T15:21:52Z"}}, {"model": "app.questionbank", "pk": 3, "fields": {"name": "物理题库 C", "description": "大学物理习题精选", "teacher_id": "**********", "visibility": 0, "tags": ["物理", "力学"], "question_count": 30, "use_count": 10, "accuracy_rate": 0.91, "create_time": "2025-06-15T15:21:52Z", "update_time": "2025-06-15T15:21:52Z"}}, {"model": "app.questionbank", "pk": 4, "fields": {"name": "线代题库 D", "description": "线性代数复习题集", "teacher_id": "**********", "visibility": 1, "tags": ["矩阵", "向量"], "question_count": 25, "use_count": 8, "accuracy_rate": 0.88, "create_time": "2025-06-15T15:21:52Z", "update_time": "2025-06-15T15:21:52Z"}}, {"model": "app.questionbank", "pk": 5, "fields": {"name": "数据结构题库", "description": "数据结构真题汇总", "teacher_id": "**********", "visibility": 1, "tags": ["树", "图", "算法"], "question_count": 18, "use_count": 4, "accuracy_rate": 0.72, "create_time": "2025-06-15T15:21:52Z", "update_time": "2025-06-15T15:21:52Z"}}, {"model": "app.question", "pk": 1, "fields": {"bank": 1, "type": "single_choice", "content": "设函数 f(x) = x^2 + 1，求 f(2) 的值。", "content_attachment": null, "options": ["3", "4", "5", "6"], "answer": "5", "analysis": "代入 f(2) = 2^2 + 1 = 5。", "analysis_attachment": null, "difficulty": 1, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 2, "fields": {"bank": 1, "type": "single_choice", "content": "函数 y=sin(x) 的导函数是？", "content_attachment": null, "options": ["cos(x)", "-cos(x)", "-sin(x)", "1"], "answer": "cos(x)", "analysis": "sin(x) 的导数是 cos(x)。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 3, "fields": {"bank": 2, "type": "single_choice", "content": "Python 中打印函数是哪个？", "content_attachment": null, "options": ["print()", "echo()", "say()", "write()"], "answer": "print()", "analysis": "print() 是 Python 中用于输出的函数。", "analysis_attachment": null, "difficulty": 1, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 4, "fields": {"bank": 2, "type": "multiple_choice", "content": "哪些属于 Python 数据类型？", "content_attachment": null, "options": ["int", "list", "input", "float"], "answer": "[\"int\", \"list\", \"float\"]", "analysis": "input 是函数，int、list、float 是类型。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 5, "fields": {"bank": 3, "type": "single_choice", "content": "牛顿第一定律描述的是？", "content_attachment": null, "options": ["惯性定律", "力的相互作用", "加速度", "动量守恒"], "answer": "惯性定律", "analysis": "牛顿第一定律是惯性定律。", "analysis_attachment": null, "difficulty": 1, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 6, "fields": {"bank": 3, "type": "multiple_choice", "content": "属于矢量的物理量是？", "content_attachment": null, "options": ["速度", "时间", "加速度", "质量"], "answer": "[\"速度\", \"加速度\"]", "analysis": "矢量有方向，速度和加速度是矢量。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 7, "fields": {"bank": 4, "type": "single_choice", "content": "矩阵乘法中，A(m*n) × B(n*p) 的结果维度为？", "content_attachment": null, "options": ["m*n", "n*p", "m*p", "n*n"], "answer": "m*p", "analysis": "行×列法则：结果矩阵是 m 行 p 列。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 8, "fields": {"bank": 4, "type": "multiple_choice", "content": "线性相关向量组的特征是？", "content_attachment": null, "options": ["存在非零解", "秩小于列数", "可线性表示", "秩等于行数"], "answer": "[\"存在非零解\", \"秩小于列数\", \"可线性表示\"]", "analysis": "线性相关表示某些向量可由其它向量线性组合得到。", "analysis_attachment": null, "difficulty": 3, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 9, "fields": {"bank": 5, "type": "single_choice", "content": "树的中序遍历是？", "content_attachment": null, "options": ["左根右", "根左右", "右左根", "左右根"], "answer": "左根右", "analysis": "中序遍历顺序是左-根-右。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 10, "fields": {"bank": 5, "type": "multiple_choice", "content": "下列哪些是图的遍历算法？", "content_attachment": null, "options": ["DFS", "BFS", "<PERSON>jk<PERSON>", "Prim"], "answer": "[\"DFS\", \"BFS\"]", "analysis": "DFS 和 BFS 是遍历算法，Dijkstra 和 Prim 是最短路和最小生成树算法。", "analysis_attachment": null, "difficulty": 3, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:38:00Z", "update_time": "2025-06-15T15:38:00Z"}}, {"model": "app.question", "pk": 11, "fields": {"bank": 1, "type": "judge", "content": "导函数 f'(x) 恒为正，则函数 f(x) 是单调递增的。", "content_attachment": null, "options": ["正确", "错误"], "answer": "true", "analysis": "导函数大于 0，函数递增。", "analysis_attachment": null, "difficulty": 1, "score": 2, "blank_count": 0, "create_time": "2025-06-15T15:46:47Z", "update_time": "2025-06-15T15:46:47Z"}}, {"model": "app.question", "pk": 12, "fields": {"bank": 2, "type": "judge", "content": "Python 中变量必须先声明类型才能使用。", "content_attachment": null, "options": ["正确", "错误"], "answer": "false", "analysis": "Python 是动态类型语言，不需预先声明变量类型。", "analysis_attachment": null, "difficulty": 1, "score": 2, "blank_count": 0, "create_time": "2025-06-15T15:46:47Z", "update_time": "2025-06-15T15:46:47Z"}}, {"model": "app.question", "pk": 13, "fields": {"bank": 3, "type": "judge", "content": "静止的物体不受任何力的作用。", "content_attachment": null, "options": ["正确", "错误"], "answer": "false", "analysis": "静止可能是多个力平衡的结果，不代表无受力。", "analysis_attachment": null, "difficulty": 2, "score": 3, "blank_count": 0, "create_time": "2025-06-15T15:46:47Z", "update_time": "2025-06-15T15:46:47Z"}}, {"model": "app.question", "pk": 14, "fields": {"bank": 4, "type": "judge", "content": "两个线性无关的向量可以构成二维空间的基。", "content_attachment": null, "options": ["正确", "错误"], "answer": "true", "analysis": "两个线性无关向量可构成二维基。", "analysis_attachment": null, "difficulty": 2, "score": 3, "blank_count": 0, "create_time": "2025-06-15T15:46:47Z", "update_time": "2025-06-15T15:46:47Z"}}, {"model": "app.question", "pk": 15, "fields": {"bank": 5, "type": "judge", "content": "栈是一种先进先出（FIFO）的数据结构。", "content_attachment": null, "options": ["正确", "错误"], "answer": "false", "analysis": "栈是后进先出（LIFO），队列才是 FIFO。", "analysis_attachment": null, "difficulty": 1, "score": 2, "blank_count": 0, "create_time": "2025-06-15T15:46:47Z", "update_time": "2025-06-15T15:46:47Z"}}, {"model": "app.question", "pk": 16, "fields": {"bank": 1, "type": "fill_in", "content": "函数 f(x) = sin(x) 的导数是 ____。", "content_attachment": null, "options": [], "answer": "cos(x)", "analysis": "使用导数公式 d/dx[sin(x)] = cos(x)", "analysis_attachment": null, "difficulty": 1, "score": 3, "blank_count": 1, "create_time": "2025-06-15T15:47:09Z", "update_time": "2025-06-15T15:47:09Z"}}, {"model": "app.question", "pk": 17, "fields": {"bank": 2, "type": "fill_in", "content": "请写出输出 \"Hello, <PERSON>!\" 的 Python 语句：____。", "content_attachment": null, "options": [], "answer": "print(\"Hello, <PERSON>!\")", "analysis": "Python 使用 print() 输出文本。", "analysis_attachment": null, "difficulty": 1, "score": 3, "blank_count": 1, "create_time": "2025-06-15T15:47:09Z", "update_time": "2025-06-15T15:47:09Z"}}, {"model": "app.question", "pk": 18, "fields": {"bank": 3, "type": "fill_in", "content": "地球重力加速度的标准值是 ____ m/s²。", "content_attachment": null, "options": [], "answer": "9.8", "analysis": "地球表面重力加速度约为 9.8 m/s²。", "analysis_attachment": null, "difficulty": 2, "score": 3, "blank_count": 1, "create_time": "2025-06-15T15:47:09Z", "update_time": "2025-06-15T15:47:09Z"}}, {"model": "app.question", "pk": 19, "fields": {"bank": 4, "type": "fill_in", "content": "一个 n 阶单位矩阵的行列式值为 ____。", "content_attachment": null, "options": [], "answer": "1", "analysis": "单位矩阵对角线为 1，其余为 0，行列式为 1。", "analysis_attachment": null, "difficulty": 2, "score": 3, "blank_count": 1, "create_time": "2025-06-15T15:47:09Z", "update_time": "2025-06-15T15:47:09Z"}}, {"model": "app.question", "pk": 20, "fields": {"bank": 5, "type": "fill_in", "content": "在链表中，最后一个节点的指针指向 ____。", "content_attachment": null, "options": [], "answer": "NULL", "analysis": "链表最后一个节点指向空（NULL）。", "analysis_attachment": null, "difficulty": 1, "score": 2, "blank_count": 1, "create_time": "2025-06-15T15:47:09Z", "update_time": "2025-06-15T15:47:09Z"}}, {"model": "app.question", "pk": 21, "fields": {"bank": 1, "type": "essay", "content": "请说明导数在实际问题中的应用。", "content_attachment": null, "options": [], "answer": "例如速度、最大值、最小值等问题中会用到导数。", "analysis": "开放性回答，考察导数实际应用场景。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:47:25Z", "update_time": "2025-06-15T15:47:25Z"}}, {"model": "app.question", "pk": 22, "fields": {"bank": 2, "type": "essay", "content": "请简述 Python 中的列表和元组的区别。", "content_attachment": null, "options": [], "answer": "列表是可变的，元组是不可变的。", "analysis": "考查对数据结构的理解。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:47:25Z", "update_time": "2025-06-15T15:47:25Z"}}, {"model": "app.question", "pk": 23, "fields": {"bank": 3, "type": "essay", "content": "试分析牛顿第二定律在实际生活中的应用。", "content_attachment": null, "options": [], "answer": "F=ma 适用于各种运动分析，如车辆起步。", "analysis": "牛顿定律在力学分析中广泛应用。", "analysis_attachment": null, "difficulty": 3, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:47:25Z", "update_time": "2025-06-15T15:47:25Z"}}, {"model": "app.question", "pk": 24, "fields": {"bank": 4, "type": "essay", "content": "请解释线性相关与线性无关的区别。", "content_attachment": null, "options": [], "answer": "线性相关表示向量间可线性表示，线性无关不能。", "analysis": "本题用于区分向量组之间的线性关系。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:47:25Z", "update_time": "2025-06-15T15:47:25Z"}}, {"model": "app.question", "pk": 25, "fields": {"bank": 5, "type": "essay", "content": "请比较顺序表与链表的优缺点。", "content_attachment": null, "options": [], "answer": "顺序表访问快但插删慢，链表相反。", "analysis": "考查数据结构选择能力。", "analysis_attachment": null, "difficulty": 2, "score": 5, "blank_count": 0, "create_time": "2025-06-15T15:47:25Z", "update_time": "2025-06-15T15:47:25Z"}}, {"model": "app.exampaper", "pk": 1, "fields": {"title": "高等数学A期中考试", "description": "涵盖函数、极限与导数的基础知识测试", "teacher": "**********", "status": "published", "total_score": 100, "difficulty": 3.5, "create_time": "2025-06-15T09:00:00Z", "update_time": "2025-06-15T09:00:00Z"}}, {"model": "app.exampaper", "pk": 2, "fields": {"title": "C语言程序设计期末考试", "description": "全面测试C语言基础语法和编程能力", "teacher": "**********", "status": "published", "total_score": 120, "difficulty": 3.0, "create_time": "2025-06-15T10:00:00Z", "update_time": "2025-06-15T10:00:00Z"}}, {"model": "app.exampaper", "pk": 3, "fields": {"title": "数据结构期中测验", "description": "线性表、栈和队列相关知识测试", "teacher": "**********", "status": "draft", "total_score": 80, "difficulty": 2.5, "create_time": "2025-06-15T11:00:00Z", "update_time": "2025-06-15T11:00:00Z"}}, {"model": "app.exampaper", "pk": 4, "fields": {"title": "大学物理(1)力学单元测试", "description": "牛顿力学基础知识和简单应用", "teacher": "**********", "status": "published", "total_score": 60, "difficulty": 2.0, "create_time": "2025-06-15T12:00:00Z", "update_time": "2025-06-15T12:00:00Z"}}, {"model": "app.exampaper", "pk": 5, "fields": {"title": "Python编程基础考试", "description": "Python语法基础测试", "teacher": "**********", "status": "published", "total_score": 100, "difficulty": 2.0, "create_time": "2025-06-15T13:00:00Z", "update_time": "2025-06-15T13:00:00Z"}}, {"model": "app.exam", "pk": 1, "fields": {"teacher": "**********", "paper": 1, "course": 42, "title": "2025年高等数学A期中考试", "description": "测试学生对函数、极限与导数基础知识的掌握情况", "start_time": "2025-04-10T09:00:00Z", "end_time": "2025-04-10T11:00:00Z", "duration_minutes": 120, "pass_score": 60, "total_score": 100, "status": 3, "shuffle_questions": true, "shuffle_options": true, "allow_break": false, "break_affect_time": false, "allow_multiple": false, "show_analysis": true, "add_to_wrong": true, "show_score_type": 2, "anti_cheat_options": {"no_copy": true, "full_screen": true}, "create_time": "2025-03-20T00:00:00Z", "update_time": "2025-04-11T00:00:00Z"}}, {"model": "app.exam", "pk": 2, "fields": {"teacher": "**********", "paper": 2, "course": 2, "title": "2025年C语言程序设计期末考试", "description": "全面测试学生C语言编程能力", "start_time": "2025-06-20T09:00:00Z", "end_time": "2025-06-20T11:30:00Z", "duration_minutes": 150, "pass_score": 72, "total_score": 120, "status": 2, "shuffle_questions": true, "shuffle_options": true, "allow_break": true, "break_affect_time": true, "allow_multiple": false, "show_analysis": false, "add_to_wrong": true, "show_score_type": 1, "anti_cheat_options": {"no_copy": true, "full_screen": true, "face_recognition": true}, "create_time": "2025-05-15T00:00:00Z", "update_time": "2025-06-15T14:00:00Z"}}, {"model": "app.exam", "pk": 3, "fields": {"teacher": "**********", "paper": 3, "course": 6, "title": "数据结构期中测验", "description": "测试线性表、栈和队列相关知识", "start_time": "2025-09-15T14:00:00Z", "end_time": "2025-09-15T15:30:00Z", "duration_minutes": 90, "pass_score": 48, "total_score": 80, "status": 1, "shuffle_questions": false, "shuffle_options": false, "allow_break": true, "break_affect_time": false, "allow_multiple": true, "show_analysis": true, "add_to_wrong": true, "show_score_type": 1, "anti_cheat_options": {"full_screen": true}, "create_time": "2025-08-01T00:00:00Z", "update_time": "2025-08-01T00:00:00Z"}}, {"model": "app.exam", "pk": 4, "fields": {"teacher": "**********", "paper": 4, "course": 43, "title": "大学物理(1)力学单元测试", "description": "测试牛顿运动定律等基础知识", "start_time": "2025-03-05T08:00:00Z", "end_time": "2025-03-05T09:00:00Z", "duration_minutes": 60, "pass_score": 36, "total_score": 60, "status": 3, "shuffle_questions": true, "shuffle_options": false, "allow_break": false, "break_affect_time": false, "allow_multiple": false, "show_analysis": true, "add_to_wrong": false, "show_score_type": 2, "anti_cheat_options": {}, "create_time": "2025-02-20T00:00:00Z", "update_time": "2025-03-06T00:00:00Z"}}, {"model": "app.exam", "pk": 5, "fields": {"teacher": "**********", "paper": 5, "course": 13, "title": "Python编程基础期末考试", "description": "测试Python基础语法和简单编程能力", "start_time": "2025-06-18T13:00:00Z", "end_time": "2025-06-18T15:00:00Z", "duration_minutes": 120, "pass_score": 60, "total_score": 100, "status": 2, "shuffle_questions": true, "shuffle_options": true, "allow_break": true, "break_affect_time": true, "allow_multiple": false, "show_analysis": true, "add_to_wrong": true, "show_score_type": 1, "anti_cheat_options": {"full_screen": true}, "create_time": "2025-05-10T00:00:00Z", "update_time": "2025-06-15T15:00:00Z"}}]