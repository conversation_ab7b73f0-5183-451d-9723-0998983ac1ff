from rest_framework import serializers
from .models import (
    User, CourseCategory, Course, CourseCarousel, CourseChapter, CourseMaterial, CourseComment, CourseFavorite,
    QuestionBank, Question, ExamPaper, PaperQuestion, Exam, Notification
)

class UserSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['username', 'role']
        
# 获取用户信息的序列化器
class UserSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'role',
            'role_display',
            'status',
            'status_display',
            'create_time',
            'update_time',
            'last_login'
        ]

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(max_length=50)
    password = serializers.Char<PERSON>ield(max_length=100)
    role = serializers.Cha<PERSON><PERSON><PERSON>(required=False)

    def validate(self, data):
        user = User.objects.filter(username=data['username']).first()
        if not user:
            raise serializers.ValidationError("用户不存在")
        return data
    
class RegisterSerializer(serializers.Serializer):
    username = serializers.CharField(max_length=50, required=True)
    password = serializers.CharField(max_length=100, required=True)

class CourseCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseCategory
        fields = '__all__'

class CourseCarouselSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseCarousel
        fields = ['image_url', 'sort_order']

class CourseMaterialSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseMaterial
        fields = ['id', 'title', 'type', 'url', 'duration', 'size', 'sort_order']

class CourseChapterSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseChapter
        fields = ['id', 'title', 'description', 'sort_order', 'create_time']
        read_only_fields = ['id', 'create_time']  # 这些字段不允许修改

    def validate_sort_order(self, value):
        """验证排序值"""
        if value < 0:
            raise serializers.ValidationError("排序值不能为负数")
        return value

class CourseSerializer(serializers.ModelSerializer):
    # category = CourseCategorySerializer(read_only=True)  # 仅用于读取时嵌套显示
    # category = serializers.PrimaryKeyRelatedField(queryset=CourseCategory.objects.all())
    
    category = CourseCategorySerializer(read_only=True)  # 用于读取时嵌套显示
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=CourseCategory.objects.all(),
        source='category',
        write_only=True  # 仅在写入时使用
    )
    carousels = CourseCarouselSerializer(many=True, read_only=True)
    
    class Meta:
        model = Course
        fields = [
            'id', 'teacher_id', 'category', 'category_id', 'title', 'description', 
            'cover_url', 'status', 'recommend_level', 'favorite_count', 
            'enroll_count', 'create_time', 'update_time', 'carousels'
        ]

class CourseCreateSerializer(serializers.ModelSerializer):
    # 课程章节
    chapters = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True
    )
    # 课程分类
    category = serializers.PrimaryKeyRelatedField( # 关键修改：明确声明为 PrimaryKeyRelatedField，允许直接传递整数ID
        queryset=CourseCategory.objects.all(),
        required=True,
        write_only=True  # 仅在写入时使用，读取时仍用嵌套序列化器
    )
    carousels = CourseCarouselSerializer(many=True, required=False)

    class Meta:
        model = Course
        fields = [
            'teacher_id', 'category', 'title', 'description',
            'cover_url', 'status', 'recommend_level', 'carousels'
        ]
        extra_kwargs = {
            'status': {'read_only': True},
            'recommend_level': {'read_only': True},
            'teacher_id': {'required': False}
        }

    def create(self, validated_data):
        chapters_data = validated_data.pop('chapters', [])
        carousels_data = validated_data.pop('carousels', [])
        
        course = Course.objects.create(**validated_data)
        
        # 创建章节和材料
        for chapter_data in chapters_data:
            chapter = CourseChapter.objects.create(
                course=course,
                title=chapter_data['title'],
                description=chapter_data.get('description', ''),
                sort_order=chapter_data.get('sort_order', 0)
            )
            
            for material_data in chapter_data.get('materials', []):
                CourseMaterial.objects.create(
                    chapter=chapter,
                    title=material_data['title'],
                    type=material_data['type'],
                    url=material_data['url'],
                    size=material_data.get('size', 0),
                    duration=material_data.get('duration', 0),
                    sort_order=material_data.get('sort_order', 0)
                )
        
        # 创建轮播图
        CourseCarousel.objects.bulk_create([
            CourseCarousel(
                course=course,
                image_url=item['image_url'],
                sort_order=item.get('sort_order', index)
            ) for index, item in enumerate(carousels_data)
        ])
        
        return course

class CourseCreateSerializer(serializers.ModelSerializer):
    category = serializers.PrimaryKeyRelatedField(
        queryset=CourseCategory.objects.all(),
        required=True
    )
    carousels = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True  # 仅用于写入，不包含在响应中
    )

    class Meta:
        model = Course
        fields = [
            'teacher_id', 'category', 'title', 'description',
            'cover_url', 'status', 'recommend_level', 'carousels'
        ]
        extra_kwargs = {
            'status': {'read_only': True},
            'recommend_level': {'read_only': True},
            'teacher_id': {'required': False}
        }

    def create(self, validated_data):
        # 1. 提取轮播图数据（从 validated_data 中移除，避免传递给 Course.create）
        carousels_data = validated_data.pop('carousels', [])
        
        # 2. 创建课程（不包含 carousels）
        course = Course.objects.create(**validated_data)
        
        # 3. 批量创建关联的轮播图
        CourseCarousel.objects.bulk_create([
            CourseCarousel(
                course=course,
                image_url=item['image_url'],
                sort_order=item.get('sort_order', index)  # 默认使用数组索引作为排序
            ) for index, item in enumerate(carousels_data)
        ])
        
        return course

# 添加评论序列化器
class CourseCommentSerializer(serializers.ModelSerializer):
    username = serializers.SerializerMethodField()
    time = serializers.SerializerMethodField()
    replies = serializers.SerializerMethodField()
    
    class Meta:
        model = CourseComment
        fields = ['id', 'username', 'content', 'likes', 'time', 'replies']
    
    def get_username(self, obj):
        return obj.user.username
    
    def get_time(self, obj):
        return obj.create_time.strftime('%Y-%m-%d %H:%M')
    
    def get_replies(self, obj):
        if obj.parent is not None:
            # 这是回复，不需要再嵌套回复
            return []
        
        # 获取该评论的所有回复
        replies = obj.replies.all()
        return CourseCommentReplySerializer(replies, many=True).data

class CourseCommentReplySerializer(serializers.ModelSerializer):
    username = serializers.SerializerMethodField()
    time = serializers.SerializerMethodField()
    
    class Meta:
        model = CourseComment
        fields = ['id', 'username', 'content', 'time']
    
    def get_username(self, obj):
        return obj.user.username
    
    def get_time(self, obj):
        return obj.create_time.strftime('%Y-%m-%d %H:%M')

# 添加收藏序列化器
class CourseFavoriteSerializer(serializers.ModelSerializer):
    course_title = serializers.SerializerMethodField()
    
    class Meta:
        model = CourseFavorite
        fields = ['id', 'course', 'course_title', 'create_time']
    
    def get_course_title(self, obj):
        return obj.course.title
    
# 添加通知序列化器
class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'notification_text', 'publish_time']

    
# ===============================================================
# 题考中心
# ===============================================================
    
class QuestionBankSerializer(serializers.ModelSerializer):
    teacher_info = serializers.SerializerMethodField(read_only=True)
    teacher_id = serializers.CharField(
        max_length=150,
        required=True,
        write_only=True,
        help_text="教师用户名（非ID）"
    )

    class Meta:
        model = QuestionBank
        fields = [
            'id', 'name', 'description', 'teacher_id', 'teacher_info',
            'visibility', 'tags', 'question_count', 'use_count',
            'accuracy_rate', 'create_time', 'update_time'
        ]
        read_only_fields = [
            'id', 'teacher_info', 'question_count', 
            'use_count', 'accuracy_rate', 'create_time', 'update_time'
        ]
        extra_kwargs = {
            'description': {'allow_null': True},
            'tags': {'allow_null': True}
        }

    def get_teacher_info(self, obj):
        """返回教师详细信息"""
        teacher = obj.teacher
        if not teacher:
            return None
        return {
            'username': teacher.username,
            'role': teacher.role,
            # 可根据需要添加更多字段
        }

    def validate_teacher_id(self, value):
        """验证教师用户名有效性"""
        if not User.objects.filter(username=value, role='teacher').exists():
            raise serializers.ValidationError("指定的教师用户不存在或不是教师角色")
        return value

    def create(self, validated_data):
        """创建时自动关联当前用户（如果需要）"""
        request = self.context.get('request')
        if request and request.user.role == 'teacher':
            validated_data['teacher_id'] = request.user.username
        return super().create(validated_data)

class QuestionSerializer(serializers.ModelSerializer):
    bank_name = serializers.CharField(source='bank.name', read_only=True)
    
    class Meta:
        model = Question
        fields = [
            'id', 'bank', 'bank_name', 'type', 'content', 'content_attachment',
            'options', 'answer', 'analysis', 'analysis_attachment', 'difficulty',
            'score', 'blank_count', 'create_time', 'update_time'
        ]
        read_only_fields = ['create_time', 'update_time']

    def validate_bank(self, value):
        """验证题库是否属于当前用户或公开"""
        request = self.context.get('request')
        if not request:
            return value
            
        if value.teacher != request.user and value.visibility != QuestionBank.PUBLIC:
            raise serializers.ValidationError("没有权限在此题库中添加题目")
        return value


class ExamPaperSerializer(serializers.ModelSerializer):
    teacher = serializers.SlugRelatedField(slug_field='username', queryset=User.objects.all())
    question_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ExamPaper
        fields = [
            'id', 'title', 'description', 'teacher', 'status', 'total_score',
            'difficulty', 'create_time', 'update_time', 'question_count'
        ]
        read_only_fields = ['create_time', 'update_time']

    def get_question_count(self, obj):
        return obj.paper_questions.count()

    def validate_teacher(self, value):
        """确保试卷创建者是当前用户"""
        request = self.context.get('request')
        if request and request.user != value:
            raise serializers.ValidationError("只能创建自己的试卷")
        return value


class PaperQuestionSerializer(serializers.ModelSerializer):
    question_detail = QuestionSerializer(source='question', read_only=True)
    
    class Meta:
        model = PaperQuestion
        fields = ['id', 'paper', 'question', 'question_detail', 'order_num', 'score']
        read_only_fields = ['paper']

    def validate(self, data):
        """验证题目是否属于当前用户或公开题库"""
        request = self.context.get('request')
        if not request:
            return data
            
        question = data.get('question')
        if question.bank.teacher != request.user and question.bank.visibility != QuestionBank.PUBLIC:
            raise serializers.ValidationError({"question": "没有权限使用此题目"})
        return data


class ExamSerializer(serializers.ModelSerializer):
    teacher = serializers.SlugRelatedField(slug_field='username', queryset=User.objects.all())
    paper_title = serializers.CharField(source='paper.title', read_only=True)
    status = serializers.CharField(read_only=True)
    
    class Meta:
        model = Exam
        fields = [
            'id', 'teacher', 'paper', 'paper_title', 'title', 'description',
            'start_time', 'end_time', 'duration_minutes', 'pass_score', 'total_score',
            'status', 'shuffle_questions', 'shuffle_options', 'allow_break',
            'break_affect_time', 'allow_multiple', 'show_analysis', 'add_to_wrong',
            'show_score_type', 'anti_cheat_options', 'create_time', 'update_time'
        ]
        read_only_fields = ['create_time', 'update_time']

    def validate_teacher(self, value):
        """确保考试创建者是当前用户"""
        request = self.context.get('request')
        if request and request.user != value:
            raise serializers.ValidationError("只能创建自己的考试")
        return value

    def validate(self, data):
        """验证考试时间合理性"""
        if 'start_time' in data and 'end_time' in data:
            if data['start_time'] >= data['end_time']:
                raise serializers.ValidationError({
                    "end_time": "结束时间必须晚于开始时间"
                })
        return data

class ExamPaperCreateSerializer(serializers.ModelSerializer):
    """试卷创建序列化器（包含题目添加功能）"""
    questions = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="题目列表（包含题目ID和分值）"
    )
    
    class Meta:
        model = ExamPaper
        fields = [
            'id', 'title', 'description', 'teacher', 'status', 
            'total_score', 'difficulty', 'questions'
        ]
        read_only_fields = ['id', 'status', 'total_score', 'difficulty']
        extra_kwargs = {
            'teacher': {'required': False}
        }
    
    def validate(self, data):
        """验证题目数据"""
        request = self.context.get('request')
        if not request:
            raise serializers.ValidationError("缺少请求上下文")
            
        # 确保教师是当前用户
        if 'teacher' in data and data['teacher'] != request.user:
            raise serializers.ValidationError({"teacher": "只能创建自己的试卷"})
            
        # 如果没有传入teacher，设置为当前用户
        if 'teacher' not in data:
            data['teacher'] = request.user
            
        return data
    
    def create(self, validated_data):
        """创建试卷并关联题目"""
        questions_data = validated_data.pop('questions', [])
        
        with transaction.atomic():
            # 1. 创建试卷
            paper = ExamPaper.objects.create(**validated_data)
            
            # 2. 添加题目到试卷
            self._add_questions_to_paper(paper, questions_data)
            
            # 3. 计算总分和平均难度
            self._update_paper_stats(paper)
            
        return paper
    
    def update(self, instance, validated_data):
        """更新试卷并处理题目变更"""
        questions_data = validated_data.pop('questions', None)
        
        with transaction.atomic():
            # 1. 更新试卷基本信息
            instance = super().update(instance, validated_data)
            
            # 2. 如果传入了questions数据，则更新题目关联
            if questions_data is not None:
                # 先清除现有题目
                instance.paper_questions.all().delete()
                # 再添加新题目
                self._add_questions_to_paper(instance, questions_data)
            
            # 3. 更新统计信息
            self._update_paper_stats(instance)
            
        return instance
    
    def _add_questions_to_paper(self, paper, questions_data):
        """将题目添加到试卷"""
        if not questions_data:
            return
            
        # 准备批量创建数据
        paper_questions = []
        for order_num, item in enumerate(questions_data, start=1):
            try:
                question = Question.objects.get(
                    id=item['id'],
                    bank__teacher_id=paper.teacher.username
                )
                paper_questions.append(
                    PaperQuestion(
                        paper=paper,
                        question=question,
                        order_num=order_num,
                        score=item.get('score', question.score)
                    )
                )
            except Question.DoesNotExist:
                raise serializers.ValidationError(
                    {"questions": f"题目ID {item['id']} 不存在或不属于当前用户"}
                )
        
        # 批量创建
        PaperQuestion.objects.bulk_create(paper_questions)
    
    def _update_paper_stats(self, paper):
        """更新试卷统计信息（总分和平均难度）"""
        from django.db.models import Sum, Avg
        
        stats = paper.paper_questions.aggregate(
            total_score=Sum('score'),
            avg_difficulty=Avg('question__difficulty')
        )
        
        paper.total_score = stats['total_score'] or 0
        paper.difficulty = round(stats['avg_difficulty'] or 2.0, 1)
        paper.save()