from django.db import models
from django.contrib.auth.models import AbstractBaseUser
from django.contrib.auth.hashers import make_password, check_password
from django.utils import timezone
from django.contrib.auth import get_user_model

class User(AbstractBaseUser):
    username = models.CharField(max_length=50, unique=True)
    password = models.CharField(max_length=100)  # 注意云端可能是明文存储
    role = models.CharField(max_length=10, choices=[
        ('student', '学生'),
        ('teacher', '教师'),
        ('admin', '管理员'),
        ('visitor', '访客')
    ])

    status = models.SmallIntegerField(default=1, choices=[
        (0, '禁用'),
        (1, '启用'),
    ])  # 假设1表示"启用"状态
    create_time = models.DateTimeField(default=timezone.now)  # 自动设置当前时间为默认值
    update_time = models.DateTimeField(auto_now=True)  # 每次保存自动更新
    
    USERNAME_FIELD = 'username'
    
    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'


class CourseCategory(models.Model):
    name = models.CharField(max_length=50, verbose_name="分类名称")
    status = models.BooleanField(default=True, verbose_name="是否启用")
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

class Course(models.Model):
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布')
    ]
    
    teacher_id = models.CharField(max_length=50)
    category = models.ForeignKey(CourseCategory, on_delete=models.CASCADE, db_column='category_id')
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    cover_url = models.CharField(max_length=200, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    recommend_level = models.SmallIntegerField(default=0)
    favorite_count = models.IntegerField(default=0)
    enroll_count = models.IntegerField(default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['title'], name='idx_title'),
            models.Index(fields=['category_id'], name='idx_category_id'),
            models.Index(fields=['status'], name='idx_status')
        ]

class CourseCarousel(models.Model):
    course = models.ForeignKey(Course, on_delete=models.CASCADE, db_column='course_id')
    image_url = models.CharField(max_length=200)
    sort_order = models.IntegerField(default=0)
    create_time = models.DateTimeField(default=timezone.now)  # 修改为default而不是auto_now_add
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['course_id'], name='idx_course_id')
        ]

class CourseMaterial(models.Model):
    """课程课件表"""
    CHAPTER_TYPE = (
        ('doc', '文档'),
        ('video', '视频'),
        ('image', '图片'),
        ('audio', '音频'),
    )
    
    # 修正外键引用，确保模型名称正确（CourseChapter而非coursechapter）
    chapter = models.ForeignKey(
        'CourseChapter',  # 直接引用模型类名
        on_delete=models.CASCADE,
        related_name='materials',
        verbose_name="章节"
    )
    title = models.CharField(max_length=50, verbose_name="课件标题")
    type = models.CharField(max_length=10, choices=CHAPTER_TYPE, verbose_name="类型")
    url = models.URLField(max_length=255, verbose_name="资源地址")
    duration = models.IntegerField(default=0, verbose_name="时长（秒，视频/音频用）")
    size = models.BigIntegerField(default=0, verbose_name="文件大小（字节）")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

class CourseChapter(models.Model):
    """课程章节表"""
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='chapters', verbose_name="课程")
    title = models.CharField(max_length=100, verbose_name="章节标题")
    description = models.TextField(blank=True, null=True, verbose_name="章节描述")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "课程章节"
        verbose_name_plural = "课程章节"
        ordering = ['sort_order', 'id']

class Homework(models.Model):
    """作业表"""
    DEADLINE_STATUS = (
        (1, '未发布'),
        (2, '已发布'),
        (3, '已截止'),
    )
    teacher = models.ForeignKey(User, on_delete=models.CASCADE, related_name='homeworks', verbose_name="教师")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='homeworks', verbose_name="课程")
    title = models.CharField(max_length=100, verbose_name="作业标题")
    content = models.TextField(verbose_name="作业内容")
    attachment_url = models.URLField(max_length=255, blank=True, null=True, verbose_name="附件URL")  # 存储OSS URL
    deadline = models.DateTimeField(verbose_name="截止时间")
    total_score = models.SmallIntegerField(default=100, verbose_name="总分")
    status = models.SmallIntegerField(choices=DEADLINE_STATUS, default=1, verbose_name="状态")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

class CourseComment(models.Model):
    """课程评论表"""
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='comments', verbose_name="课程")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments', verbose_name="用户")
    content = models.TextField(verbose_name="评论内容")
    likes = models.IntegerField(default=0, verbose_name="点赞数")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies', verbose_name="父评论")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "课程评论"
        verbose_name_plural = "课程评论"
        ordering = ['-create_time']

class CourseFavorite(models.Model):
    """课程收藏表"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites', verbose_name="用户")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='favorites', verbose_name="课程")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "课程收藏"
        verbose_name_plural = "课程收藏"
        unique_together = ('user', 'course')  # 确保一个用户只能收藏一个课程一次
        
# 通知模型
class Notification(models.Model):
    notification_text = models.TextField()
    publish_time = models.DateTimeField(auto_now_add=True)  # 自动设置为当前时间

    def __str__(self):
        return self.notification_text
        
# 学生注册课程模型
class StudentEnrollCourse(models.Model):
    """学生注册课程表"""
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='enrolled_courses', verbose_name="学生")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrolled_students', verbose_name="课程")
    enroll_time = models.DateTimeField(auto_now_add=True, verbose_name="注册时间")
    progress = models.IntegerField(default=0, verbose_name="学习进度(%)")
    status = models.IntegerField(default=1, verbose_name="状态：1=在学，2=已结课，3=已退课")

    class Meta:
        verbose_name = "学生注册课程"
        verbose_name_plural = "学生注册课程"
        unique_together = ('student', 'course')  # 确保一个学生只能注册一个课程一次


# ===============================================================
# 题考中心
# ===============================================================
User = get_user_model()

class QuestionBank(models.Model):
    """题库模型（teacher_id字段存储username）"""
    PRIVATE = 0
    PUBLIC = 1
    VISIBILITY_CHOICES = (
        (PRIVATE, '私有'),
        (PUBLIC, '公开'),
    )

    name = models.CharField(max_length=100, verbose_name="题库名称")
    description = models.TextField(blank=True, null=True, verbose_name="题库描述")
    teacher_id = models.CharField(max_length=150, verbose_name="教师用户名")  # 存储username但保持字段名
    visibility = models.SmallIntegerField(
        choices=VISIBILITY_CHOICES, 
        default=PRIVATE, 
        verbose_name="可见性"
    )
    tags = models.JSONField(
        default=list, 
        blank=True, 
        verbose_name="标签列表"
    )
    question_count = models.IntegerField(
        default=0, 
        verbose_name="试题数量"
    )
    use_count = models.IntegerField(
        default=0, 
        verbose_name="使用次数"
    )
    accuracy_rate = models.FloatField(
        default=0, 
        verbose_name="平均正确率"
    )
    create_time = models.DateTimeField(
        default=timezone.now, 
        verbose_name="创建时间"
    )
    update_time = models.DateTimeField(
        auto_now=True, 
        verbose_name="更新时间"
    )

    @property
    def teacher(self):
        """通过teacher_id获取教师对象"""
        return User.objects.filter(username=self.teacher_id).first()

    def __str__(self):
        return f"{self.name}（{self.teacher_id}）"

    class Meta:
        verbose_name = '题库'
        verbose_name_plural = '题库'
        indexes = [
            models.Index(fields=['teacher_id'], name='idx_question_bank_teacher'),
            models.Index(fields=['name'], name='idx_question_bank_name'),
            models.Index(fields=['visibility'], name='idx_question_bank_visibility'),
        ]


class Question(models.Model):
    """试题模型"""
    SINGLE_CHOICE = 'single_choice'
    MULTIPLE_CHOICE = 'multiple_choice'
    JUDGMENT = 'judgment'
    FILL_BLANK = 'fill_blank'
    ESSAY = 'essay'
    
    QUESTION_TYPE_CHOICES = (
        (SINGLE_CHOICE, '单选题'),
        (MULTIPLE_CHOICE, '多选题'),
        (JUDGMENT, '判断题'),
        (FILL_BLANK, '填空题'),
        (ESSAY, '问答题'),
    )

    bank = models.ForeignKey(QuestionBank, on_delete=models.CASCADE, related_name='questions', verbose_name="所属题库")
    type = models.CharField(max_length=20, choices=QUESTION_TYPE_CHOICES, verbose_name="题型")
    content = models.TextField(verbose_name="题目内容")
    content_attachment = models.CharField(max_length=200, blank=True, null=True, verbose_name="题干附件")
    options = models.JSONField(default=list, blank=True, verbose_name="选项列表")
    answer = models.TextField(verbose_name="答案")
    analysis = models.TextField(blank=True, null=True, verbose_name="题目解析")
    analysis_attachment = models.CharField(max_length=200, blank=True, null=True, verbose_name="解析附件")
    difficulty = models.SmallIntegerField(default=2, verbose_name="难度(1-5)")
    score = models.SmallIntegerField(default=5, verbose_name="默认分值")
    blank_count = models.SmallIntegerField(default=1, verbose_name="填空数量")
    create_time = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = '试题'
        verbose_name_plural = '试题'
        indexes = [
            models.Index(fields=['bank'], name='idx_question_bank'),
            models.Index(fields=['type'], name='idx_question_type'),
            models.Index(fields=['difficulty'], name='idx_question_difficulty'),
        ]

    def __str__(self):
        return f"{self.get_type_display()}: {self.content[:50]}"

class ExamPaper(models.Model):
    """试卷模型"""
    DRAFT = 'draft'
    PUBLISHED = 'published'
    STATUS_CHOICES = (
        (DRAFT, '草稿'),
        (PUBLISHED, '已发布'),
    )

    title = models.CharField(max_length=100, verbose_name="试卷名称")
    description = models.TextField(blank=True, null=True, verbose_name="试卷描述")
    teacher = models.ForeignKey(
        User,
        to_field='username',  # 关键修改：关联到username字段
        on_delete=models.CASCADE,
        related_name='exam_papers',
        verbose_name="创建教师",
        db_column='teacher_id'  # 保持数据库列名不变
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=DRAFT, verbose_name="状态")
    total_score = models.IntegerField(default=100, verbose_name="总分")
    difficulty = models.FloatField(default=2.0, verbose_name="难度(1-5)")
    create_time = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = '试卷'
        verbose_name_plural = '试卷'
        indexes = [
            models.Index(fields=['teacher_id'], name='idx_exam_paper_teacher'),  # 注意索引字段名
            models.Index(fields=['status'], name='idx_exam_paper_status'),
        ]

    def __str__(self):
        return self.title


class PaperQuestion(models.Model):
    """试卷题目关联模型"""
    paper = models.ForeignKey(ExamPaper, on_delete=models.CASCADE, related_name='paper_questions', verbose_name="试卷")
    question = models.ForeignKey(Question, on_delete=models.CASCADE, verbose_name="试题")
    order_num = models.IntegerField(default=0, verbose_name="题目顺序")
    score = models.SmallIntegerField(verbose_name="题目分值")

    class Meta:
        verbose_name = '试卷题目'
        verbose_name_plural = '试卷题目'
        ordering = ['order_num']
        unique_together = [['paper', 'question']]
        indexes = [
            models.Index(fields=['paper'], name='idx_paper_question_paper'),
            models.Index(fields=['question'], name='idx_paper_question_question'),
        ]

    def __str__(self):
        return f"{self.paper.title} - {self.question.content[:30]}"

class Exam(models.Model):
    NOT_STARTED = 1
    ONGOING = 2
    ENDED = 3
    STATUS_CHOICES = (
        (NOT_STARTED, '未开始'),
        (ONGOING, '进行中'),
        (ENDED, '已结束'),
    )

    teacher = models.ForeignKey(
        User,
        to_field='username',  # 关键修改：关联到username字段
        on_delete=models.CASCADE,
        related_name='exams',
        verbose_name="创建教师",
        db_column='teacher_id'  # 保持数据库列名不变
    )
    paper = models.ForeignKey(ExamPaper, on_delete=models.CASCADE, verbose_name="关联试卷")
    course = models.ForeignKey(
        Course, 
        on_delete=models.CASCADE, 
        related_name='exams', 
        verbose_name="关联课程",
        null=False,
        blank=False
    )
    title = models.CharField(max_length=100, verbose_name="考试名称")
    description = models.TextField(blank=True, null=True, verbose_name="考试描述")
    start_time = models.DateTimeField(verbose_name="开始时间")
    end_time = models.DateTimeField(verbose_name="结束时间")
    duration_minutes = models.IntegerField(default=60, verbose_name="考试时长(分钟)")
    pass_score = models.SmallIntegerField(default=60, verbose_name="及格分数")
    total_score = models.IntegerField(default=100, verbose_name="总分")
    status = models.SmallIntegerField(choices=STATUS_CHOICES, default=NOT_STARTED, verbose_name="状态")
    shuffle_questions = models.BooleanField(default=False, verbose_name="随机题目顺序")
    shuffle_options = models.BooleanField(default=False, verbose_name="随机选项顺序")
    allow_break = models.BooleanField(default=False, verbose_name="允许中断")
    break_affect_time = models.BooleanField(default=False, verbose_name="中断计时")
    allow_multiple = models.BooleanField(default=False, verbose_name="允许多次考试")
    show_analysis = models.BooleanField(default=False, verbose_name="显示解析")
    add_to_wrong = models.BooleanField(default=False, verbose_name="错题入错题本")
    show_score_type = models.SmallIntegerField(default=1, verbose_name="成绩显示方式(1=立即,2=考试后,3=不显示)")
    anti_cheat_options = models.JSONField(default=dict, blank=True, verbose_name="防作弊设置")
    create_time = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = '考试'
        verbose_name_plural = '考试'
        indexes = [
            models.Index(fields=['teacher_id'], name='idx_exam_teacher'),  # 注意索引字段名
            models.Index(fields=['paper'], name='idx_exam_paper'),
            models.Index(fields=['course'], name='idx_exam_course'),
            models.Index(fields=['status'], name='idx_exam_status'),
            models.Index(fields=['start_time'], name='idx_exam_start_time'),
        ]

    def __str__(self):
        return f"{self.title}（课程：{self.course.title}）"

    def save(self, *args, **kwargs):
        # 校验课程是否属于当前教师
        if not Course.objects.filter(id=self.course_id, teacher_id=self.teacher.username).exists():
            raise ValueError("考试关联的课程必须由当前教师创建")
        
        # 状态逻辑
        now = timezone.now()
        if now < self.start_time:
            self.status = self.NOT_STARTED
        elif self.start_time <= now <= self.end_time:
            self.status = self.ONGOING
        else:
            self.status = self.ENDED
        super().save(*args, **kwargs)

# 学生个人信息模型
class StudentInfo(models.Model):
    """学生个人信息表"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True, related_name='student_info', verbose_name="用户")
    nickname = models.CharField(max_length=50, blank=True, null=True, verbose_name="昵称")
    signature = models.CharField(max_length=200, blank=True, null=True, verbose_name="个人签名")
    avatar = models.CharField(max_length=200, blank=True, null=True, verbose_name="头像地址")
    gender = models.CharField(max_length=10, blank=True, null=True, verbose_name="性别")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "学生信息"
        verbose_name_plural = "学生信息"

    def __str__(self):
        return f"{self.user.username}的个人信息"