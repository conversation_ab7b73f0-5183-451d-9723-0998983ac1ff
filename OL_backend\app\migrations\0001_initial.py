# Generated by Django 5.2.3 on 2025-06-15 13:37

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('username', models.CharField(max_length=50, unique=True)),
                ('password', models.CharField(max_length=100)),
                ('role', models.Char<PERSON>ield(choices=[('student', '学生'), ('teacher', '教师'), ('admin', '管理员'), ('visitor', '访客')], max_length=10)),
                ('status', models.SmallIntegerField(default=1)),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('update_time', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
            },
        ),
        migrations.CreateModel(
            name='CourseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='分类名称')),
                ('status', models.BooleanField(default=True, verbose_name='是否启用')),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_text', models.TextField()),
                ('publish_time', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('single_choice', '单选题'), ('multiple_choice', '多选题'), ('judgment', '判断题'), ('fill_blank', '填空题'), ('essay', '问答题')], max_length=20, verbose_name='题型')),
                ('content', models.TextField(verbose_name='题目内容')),
                ('content_attachment', models.CharField(blank=True, max_length=200, null=True, verbose_name='题干附件')),
                ('options', models.JSONField(blank=True, default=list, verbose_name='选项列表')),
                ('answer', models.TextField(verbose_name='答案')),
                ('analysis', models.TextField(blank=True, null=True, verbose_name='题目解析')),
                ('analysis_attachment', models.CharField(blank=True, max_length=200, null=True, verbose_name='解析附件')),
                ('difficulty', models.SmallIntegerField(default=2, verbose_name='难度(1-5)')),
                ('score', models.SmallIntegerField(default=5, verbose_name='默认分值')),
                ('blank_count', models.SmallIntegerField(default=1, verbose_name='填空数量')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '试题',
                'verbose_name_plural': '试题',
            },
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('teacher_id', models.CharField(max_length=50)),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('cover_url', models.CharField(blank=True, max_length=200, null=True)),
                ('status', models.CharField(choices=[('draft', '草稿'), ('published', '已发布')], default='draft', max_length=20)),
                ('recommend_level', models.SmallIntegerField(default=0)),
                ('favorite_count', models.IntegerField(default=0)),
                ('enroll_count', models.IntegerField(default=0)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
                ('update_time', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(db_column='category_id', on_delete=django.db.models.deletion.CASCADE, to='app.coursecategory')),
            ],
        ),
        migrations.CreateModel(
            name='CourseChapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='章节标题')),
                ('description', models.TextField(blank=True, null=True, verbose_name='章节描述')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chapters', to='app.course', verbose_name='课程')),
            ],
            options={
                'verbose_name': '课程章节',
                'verbose_name_plural': '课程章节',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='CourseComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='评论内容')),
                ('likes', models.IntegerField(default=0, verbose_name='点赞数')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='app.course', verbose_name='课程')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='app.coursecomment', verbose_name='父评论')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '课程评论',
                'verbose_name_plural': '课程评论',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='CourseFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to='app.course', verbose_name='课程')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '课程收藏',
                'verbose_name_plural': '课程收藏',
            },
        ),
        migrations.CreateModel(
            name='CourseMaterial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50, verbose_name='课件标题')),
                ('type', models.CharField(choices=[('doc', '文档'), ('video', '视频'), ('image', '图片'), ('audio', '音频')], max_length=10, verbose_name='类型')),
                ('url', models.URLField(max_length=255, verbose_name='资源地址')),
                ('duration', models.IntegerField(default=0, verbose_name='时长（秒，视频/音频用）')),
                ('size', models.BigIntegerField(default=0, verbose_name='文件大小（字节）')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='app.coursechapter', verbose_name='章节')),
            ],
        ),
        migrations.CreateModel(
            name='ExamPaper',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='试卷名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='试卷描述')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('published', '已发布')], default='draft', max_length=20, verbose_name='状态')),
                ('total_score', models.IntegerField(default=100, verbose_name='总分')),
                ('difficulty', models.FloatField(default=2.0, verbose_name='难度(1-5)')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('teacher', models.ForeignKey(db_column='teacher_id', on_delete=django.db.models.deletion.CASCADE, related_name='exam_papers', to=settings.AUTH_USER_MODEL, to_field='username', verbose_name='创建教师')),
            ],
            options={
                'verbose_name': '试卷',
                'verbose_name_plural': '试卷',
            },
        ),
        migrations.CreateModel(
            name='Exam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='考试名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='考试描述')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(verbose_name='结束时间')),
                ('duration_minutes', models.IntegerField(default=60, verbose_name='考试时长(分钟)')),
                ('pass_score', models.SmallIntegerField(default=60, verbose_name='及格分数')),
                ('total_score', models.IntegerField(default=100, verbose_name='总分')),
                ('status', models.SmallIntegerField(choices=[(1, '未开始'), (2, '进行中'), (3, '已结束')], default=1, verbose_name='状态')),
                ('shuffle_questions', models.BooleanField(default=False, verbose_name='随机题目顺序')),
                ('shuffle_options', models.BooleanField(default=False, verbose_name='随机选项顺序')),
                ('allow_break', models.BooleanField(default=False, verbose_name='允许中断')),
                ('break_affect_time', models.BooleanField(default=False, verbose_name='中断计时')),
                ('allow_multiple', models.BooleanField(default=False, verbose_name='允许多次考试')),
                ('show_analysis', models.BooleanField(default=False, verbose_name='显示解析')),
                ('add_to_wrong', models.BooleanField(default=False, verbose_name='错题入错题本')),
                ('show_score_type', models.SmallIntegerField(default=1, verbose_name='成绩显示方式(1=立即,2=考试后,3=不显示)')),
                ('anti_cheat_options', models.JSONField(blank=True, default=dict, verbose_name='防作弊设置')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exams', to='app.course', verbose_name='关联课程')),
                ('teacher', models.ForeignKey(db_column='teacher_id', on_delete=django.db.models.deletion.CASCADE, related_name='exams', to=settings.AUTH_USER_MODEL, to_field='username', verbose_name='创建教师')),
                ('paper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.exampaper', verbose_name='关联试卷')),
            ],
            options={
                'verbose_name': '考试',
                'verbose_name_plural': '考试',
            },
        ),
        migrations.CreateModel(
            name='Homework',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='作业标题')),
                ('content', models.TextField(verbose_name='作业内容')),
                ('attachment_url', models.URLField(blank=True, max_length=255, null=True, verbose_name='附件URL')),
                ('deadline', models.DateTimeField(verbose_name='截止时间')),
                ('total_score', models.SmallIntegerField(default=100, verbose_name='总分')),
                ('status', models.SmallIntegerField(choices=[(1, '未发布'), (2, '已发布'), (3, '已截止')], default=1, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='homeworks', to='app.course', verbose_name='课程')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='homeworks', to=settings.AUTH_USER_MODEL, verbose_name='教师')),
            ],
        ),
        migrations.CreateModel(
            name='PaperQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_num', models.IntegerField(default=0, verbose_name='题目顺序')),
                ('score', models.SmallIntegerField(verbose_name='题目分值')),
                ('paper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='paper_questions', to='app.exampaper', verbose_name='试卷')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.question', verbose_name='试题')),
            ],
            options={
                'verbose_name': '试卷题目',
                'verbose_name_plural': '试卷题目',
                'ordering': ['order_num'],
            },
        ),
        migrations.CreateModel(
            name='QuestionBank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='题库名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='题库描述')),
                ('teacher_id', models.CharField(max_length=150, verbose_name='教师用户名')),
                ('visibility', models.SmallIntegerField(choices=[(0, '私有'), (1, '公开')], default=0, verbose_name='可见性')),
                ('tags', models.JSONField(blank=True, default=list, verbose_name='标签列表')),
                ('question_count', models.IntegerField(default=0, verbose_name='试题数量')),
                ('use_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('accuracy_rate', models.FloatField(default=0, verbose_name='平均正确率')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '题库',
                'verbose_name_plural': '题库',
                'indexes': [models.Index(fields=['teacher_id'], name='idx_question_bank_teacher'), models.Index(fields=['name'], name='idx_question_bank_name'), models.Index(fields=['visibility'], name='idx_question_bank_visibility')],
            },
        ),
        migrations.AddField(
            model_name='question',
            name='bank',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='app.questionbank', verbose_name='所属题库'),
        ),
        migrations.CreateModel(
            name='StudentEnrollCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enroll_time', models.DateTimeField(auto_now_add=True, verbose_name='注册时间')),
                ('progress', models.IntegerField(default=0, verbose_name='学习进度(%)')),
                ('status', models.IntegerField(default=1, verbose_name='状态：1=在学，2=已结课，3=已退课')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrolled_students', to='app.course', verbose_name='课程')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrolled_courses', to=settings.AUTH_USER_MODEL, verbose_name='学生')),
            ],
            options={
                'verbose_name': '学生注册课程',
                'verbose_name_plural': '学生注册课程',
            },
        ),
        migrations.CreateModel(
            name='CourseCarousel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image_url', models.CharField(max_length=200)),
                ('sort_order', models.IntegerField(default=0)),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('update_time', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(db_column='course_id', on_delete=django.db.models.deletion.CASCADE, to='app.course')),
            ],
            options={
                'indexes': [models.Index(fields=['course_id'], name='idx_course_id')],
            },
        ),
        migrations.AddIndex(
            model_name='course',
            index=models.Index(fields=['title'], name='idx_title'),
        ),
        migrations.AddIndex(
            model_name='course',
            index=models.Index(fields=['category_id'], name='idx_category_id'),
        ),
        migrations.AddIndex(
            model_name='course',
            index=models.Index(fields=['status'], name='idx_status'),
        ),
        migrations.AlterUniqueTogether(
            name='coursefavorite',
            unique_together={('user', 'course')},
        ),
        migrations.AddIndex(
            model_name='exampaper',
            index=models.Index(fields=['teacher_id'], name='idx_exam_paper_teacher'),
        ),
        migrations.AddIndex(
            model_name='exampaper',
            index=models.Index(fields=['status'], name='idx_exam_paper_status'),
        ),
        migrations.AddIndex(
            model_name='exam',
            index=models.Index(fields=['teacher_id'], name='idx_exam_teacher'),
        ),
        migrations.AddIndex(
            model_name='exam',
            index=models.Index(fields=['paper'], name='idx_exam_paper'),
        ),
        migrations.AddIndex(
            model_name='exam',
            index=models.Index(fields=['course'], name='idx_exam_course'),
        ),
        migrations.AddIndex(
            model_name='exam',
            index=models.Index(fields=['status'], name='idx_exam_status'),
        ),
        migrations.AddIndex(
            model_name='exam',
            index=models.Index(fields=['start_time'], name='idx_exam_start_time'),
        ),
        migrations.AddIndex(
            model_name='paperquestion',
            index=models.Index(fields=['paper'], name='idx_paper_question_paper'),
        ),
        migrations.AddIndex(
            model_name='paperquestion',
            index=models.Index(fields=['question'], name='idx_paper_question_question'),
        ),
        migrations.AlterUniqueTogether(
            name='paperquestion',
            unique_together={('paper', 'question')},
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['bank'], name='idx_question_bank'),
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['type'], name='idx_question_type'),
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['difficulty'], name='idx_question_difficulty'),
        ),
        migrations.AlterUniqueTogether(
            name='studentenrollcourse',
            unique_together={('student', 'course')},
        ),
    ]
