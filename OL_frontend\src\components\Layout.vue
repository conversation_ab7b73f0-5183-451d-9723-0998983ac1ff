<template>
    <div class="layout">
        <!-- 顶部菜单 -->
        <header class="header">
            <div class="header-container">
                <div class="logo-container">
                    <el-icon class="logo-icon" :size="30">
                        <Picture />
                    </el-icon>
                    <span class="logo-text">学习系统</span>
                </div>
                <nav class="main-nav">
                    <el-menu mode="horizontal" :ellipsis="false" router :default-active="activeIndex">
                        <el-menu-item v-for="item in menuItems" :key="item.path" :index="item.path"
                            @click="handleMenuClick(item.path)">
                            <span>{{ item.name }}</span>
                        </el-menu-item>
                    </el-menu>
                </nav>
                <div class="header-right">
                    <div class="search-box">
                        <el-input placeholder="搜索课程、资料..." :prefix-icon="Search" @keyup.enter="handleSearch"
                            v-model="searchQuery" clearable @clear="handleSearchClear">
                            <template #append>
                                <el-button :icon="Search" @click="handleSearch" />
                            </template>
                        </el-input>
                    </div>
                    <div class="user-actions" v-if="!isLoggedIn">
                        <el-button type="primary" @click="gotoLogin">登录</el-button>
                        <el-button @click="gotoRegister">注册</el-button>
                    </div>
                    <div class="user-info" v-else>
                        <el-dropdown trigger="click">
                            <div class="user-dropdown-link">
                                <el-avatar :size="44" :icon="UserFilled" />
                                <span class="username">{{ username }}</span>
                                <el-icon>
                                    <ArrowDown />
                                </el-icon>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <template v-if="userStore.role === 'admin'">
                                        <el-dropdown-item @click="handleMenuClick('/manage')">管理系统</el-dropdown-item>
                                        <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                                    </template>
                                    <template v-else-if="userStore.role === 'teacher'">
                                        <el-dropdown-item @click="handleMenuClick('/teacherCenter')">个人中心</el-dropdown-item>
                                        <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                                    </template>
                                    <template v-else>
                                        <el-dropdown-item @click="handleMenuClick('/personal')">个人中心</el-dropdown-item>
                                        <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                                    </template>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>
        </header>

    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { Search, Picture, UserFilled, ArrowDown } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const activeIndex = ref('/')
const searchQuery = ref('')
// 动态获取登录状态和用户名
const { isLoggedIn, username } = storeToRefs(userStore)

// 菜单项配置
const menuItems = computed(() => {
    return router.options.routes
        .filter((route) => !route.meta?.hideInMenu)
        .map((route) => ({
            path: route.path,
            name: route.name || route.path.substring(1) || '首页',
        }))
})

// 处理菜单点击事件
const handleMenuClick = (path) => {
    activeIndex.value = path
    router.push(path)
}

// 组件挂载时，根据当前路由设置活动菜单项
onMounted(() => {
    activeIndex.value = route.path
})

const gotoLogin = () => {
    router.push('/login')
}
const gotoRegister = () => {
    router.push('/register')
}

// 处理搜索
const handleSearch = () => {
  const query = searchQuery.value.trim()
  if (query) {
    router.push({
      path: '/search',
      query: { q: query },
    })
    // 清空搜索框
    searchQuery.value = ''
  }
}

// 处理搜索清除
const handleSearchClear = () => {
  if (route.path === '/search') {
    router.push('/')
  }
}

// 处理退出登录
const handleLogout = () => {
    userStore.logout() // 调用user.ts中的logout方法
}
</script>

<style scoped>
.layout {
    display: flex;
    flex-direction: column;
    min-height: 12vh;
}

.header {
    background-color: var(--bg-header);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 60px;
}

.logo-container {
    width: 120px;
    height: 40px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
}

.logo-icon {
    color: var(--primary-color);
    margin-right: 8px;
}

.logo-text {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
}

.main-nav {
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
}

.search-box {
    width: 300px;
    margin: 0 20px;
}

.user-actions {
    display: flex;
    gap: 10px;
}

.user-info {
    cursor: pointer;
    width: 200px;
}

.user-dropdown-link {
    display: flex;
    align-items: center;
    gap: 8px;
}

.username {
    font-size: 14px;
    color: var(--text-primary);
}

.main-content {
    flex: 1;
    background-color: var(--bg-page);
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
}

:deep(.el-menu--horizontal) {
    border-bottom: none;
}

:deep(.el-menu-item) {
    font-size: 16px;
}

:deep(.el-menu-item.is-active) {
    color: var(--primary-color);
}
</style>
