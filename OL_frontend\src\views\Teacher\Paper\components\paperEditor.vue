<template>
    <div class="paper-editor">
        <el-form :model="form" label-width="100px" ref="formRef">
            <!-- 基础信息 -->
            <el-card class="info-card">
                <template #header>
                    <div class="card-header">
                        <span>试卷基本信息</span>
                    </div>
                </template>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="试卷名称" prop="title" required>
                            <el-input v-model="form.title" placeholder="请输入试卷名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="考试时长" prop="duration_minutes" required>
                            <el-input-number v-model="form.duration_minutes" :min="30" :max="300"
                                controls-position="right" placeholder="请输入考试时长(分钟)" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="试卷描述" prop="description">
                    <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入试卷描述" />
                </el-form-item>
            </el-card>

            <!-- 题目管理 -->
            <el-card class="questions-card">
                <template #header>
                    <div class="card-header">
                        <span>试卷题目管理</span>
                        <div class="header-actions">
                            <el-button type="primary" size="small" @click="showQuestionSelector = true">
                                <el-icon>
                                    <Plus />
                                </el-icon> 添加题目
                            </el-button>
                            <el-button type="info" size="small" @click="fetchRandomQuestions">
                                <el-icon>
                                    <Refresh />
                                </el-icon> 随机组题
                            </el-button>
                        </div>
                    </div>
                </template>

                <!-- 题目列表 -->
                <div class="question-list">
                    <div v-for="(item, index) in form.questions" :key="item.id" class="question-item" draggable="true"
                        @dragstart="handleDragStart($event, index)" @dragover.prevent="handleDragOver($event, index)"
                        @dragenter.prevent="handleDragEnter($event, index)" @dragleave="handleDragLeave($event, index)"
                        @drop="handleDrop($event, index)" @dragend="handleDragEnd($event, index)"
                        :class="{ 'drag-over': dragOverIndex === index }">
                        <div class="question-header">
                            <el-icon class="drag-handle">
                                <Rank />
                            </el-icon>
                            <span class="question-index">{{ index + 1 }}.</span>
                            <el-tag size="small" :type="getQuestionTypeTag(item.type)">
                                {{ getQuestionTypeText(item.type) }}
                            </el-tag>
                            <span class="question-content" v-html="item.content"></span>

                            <div class="question-actions">
                                <el-input-number v-model="item.score" :min="1" :max="100" controls-position="right"
                                    size="small" style="width: 100px; margin-right: 10px;" />
                                <el-button type="danger" size="small" circle @click="removeQuestion(index)">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                </el-button>
                            </div>
                        </div>
                    </div>
                    <div v-if="form.questions.length === 0" class="empty-tip">
                        <el-empty description="暂无题目，请添加题目" />
                    </div>
                </div>

                <!-- 试卷统计 -->
                <div class="paper-stats">
                    <el-descriptions :column="4" border>
                        <el-descriptions-item label="题目数量">{{ form.questions.length }}</el-descriptions-item>
                        <el-descriptions-item label="总分">
                            {{ totalScore }}
                            <el-button type="text" @click="autoDistributeScores" v-if="form.questions.length > 0">
                                (自动分配)
                            </el-button>
                        </el-descriptions-item>
                        <el-descriptions-item label="平均难度">
                            {{ averageDifficulty.toFixed(1) }}
                        </el-descriptions-item>
                        <el-descriptions-item label="预计用时">
                            {{ estimatedTime }}分钟
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-card>

            <!-- 底部操作按钮 -->
            <div class="form-actions">
                <el-button type="primary" @click="submitForm">
                    {{ isEditMode ? '保存修改' : '创建试卷' }}
                </el-button>
                <el-button @click="cancel">取消</el-button>
            </div>
        </el-form>

        <!-- 题目选择器对话框 -->
        <el-dialog v-model="showQuestionSelector" title="从题库选择题目" width="80%" top="5vh">
            <QuestionSelector :selected-questions="form.questions" @select="handleQuestionSelect"
                @cancel="showQuestionSelector = false" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'
import QuestionSelector from './QuestionSelector.vue'

const props = defineProps({
    paperId: {
        type: String,
        default: null
    }
})

const emit = defineEmits(['success', 'cancel'])

// 表单数据
const form = ref({
    title: '',
    description: '',
    duration_minutes: 90,
    questions: [],
    status: 'draft'
})

const formRef = ref(null)
const showQuestionSelector = ref(false)
const isEditMode = computed(() => !!props.paperId)

// 拖拽相关状态
const dragItemIndex = ref(null)
const dragOverIndex = ref(null)
const dragging = ref(false)

// 计算属性
const totalScore = computed(() => {
    return form.value.questions.reduce((sum, q) => sum + (q.score || 0), 0)
})

const averageDifficulty = computed(() => {
    if (form.value.questions.length === 0) return 0
    const sum = form.value.questions.reduce((sum, q) => sum + (q.difficulty || 3), 0)
    return sum / form.value.questions.length
})

const estimatedTime = computed(() => {
    // 根据题目数量和难度估算时间
    const baseTime = form.value.questions.length * 2
    const difficultyFactor = averageDifficulty.value / 3
    return Math.round(baseTime * difficultyFactor)
})

// 方法
const getQuestionTypeText = (type) => {
    const types = {
        'single_choice': '单选题',
        'multiple_choice': '多选题',
        'judgment': '判断题',
        'fill_blank': '填空题',
        'essay': '问答题'
    }
    return types[type] || type
}

const getQuestionTypeTag = (type) => {
    const typeTags = {
        'single_choice': '',
        'multiple_choice': 'success',
        'judgment': 'warning',
        'fill_blank': 'info',
        'essay': 'danger'
    }
    return typeTags[type] || ''
}

const isCorrectOption = (question, index) => {
    if (question.type === 'single_choice') {
        return String.fromCharCode(65 + index) === question.answer
    } else if (question.type === 'multiple_choice') {
        return question.answer.includes(String.fromCharCode(65 + index))
    }
    return false
}

const fetchPaperDetail = async () => {
    try {
        const response = await axios.get(`/exam-papers/${props.paperId}/`)
        form.value = response.data

        // 确保每个题目都有score属性
        form.value.questions = form.value.questions.map(q => ({
            ...q,
            score: q.score || 5 // 默认5分
        }))
    } catch (error) {
        ElMessage.error('获取试卷详情失败: ' + (error.response?.data?.message || error.message))
        emit('cancel')
    }
}

const fetchRandomQuestions = async () => {
    try {
        const response = await axios.get('/questions/random/', {
            params: {
                count: 10, // 随机获取10道题
                exclude: form.value.questions.map(q => q.id).join(',')
            }
        })

        const newQuestions = response.data.map(q => ({
            ...q,
            score: 5 // 默认5分
        }))

        form.value.questions = [...form.value.questions, ...newQuestions]
        ElMessage.success('已随机添加10道题目')
    } catch (error) {
        ElMessage.error('随机获取题目失败: ' + (error.response?.data?.message || error.message))
    }
}

const handleQuestionSelect = (selectedQuestions) => {
    // 合并新选择的题目，避免重复
    const existingIds = form.value.questions.map(q => q.id)
    const newQuestions = selectedQuestions.filter(q => !existingIds.includes(q.id))

    form.value.questions = [
        ...form.value.questions,
        ...newQuestions.map(q => ({
            ...q,
            score: q.score || 5 // 默认5分
        }))
    ]

    showQuestionSelector.value = false
    ElMessage.success(`已添加${newQuestions.length}道题目`)
}

const removeQuestion = (index) => {
    form.value.questions.splice(index, 1)
}

// 拖拽相关方法
const handleDragStart = (e, index) => {
    e.dataTransfer.setData('text/plain', index)
    dragItemIndex.value = index
    dragging.value = true
    e.target.style.opacity = '0.4'
}

const handleDragOver = (e, index) => {
    e.preventDefault()
    if (dragItemIndex.value !== index) {
        dragOverIndex.value = index
    }
}

const handleDragEnter = (e, index) => {
    e.preventDefault()
    if (dragItemIndex.value !== index) {
        dragOverIndex.value = index
    }
}

const handleDragLeave = (e, index) => {
    e.preventDefault()
    if (dragOverIndex.value === index) {
        dragOverIndex.value = null
    }
}

const handleDrop = (e, index) => {
    e.preventDefault()
    if (dragItemIndex.value === null || dragItemIndex.value === index) return

    const draggedItem = form.value.questions[dragItemIndex.value]
    const newQuestions = [...form.value.questions]

    // 移除拖拽的项目
    newQuestions.splice(dragItemIndex.value, 1)
    // 插入到新位置
    newQuestions.splice(index, 0, draggedItem)

    form.value.questions = newQuestions
    dragOverIndex.value = null
}

const handleDragEnd = (e) => {
    e.target.style.opacity = '1'
    dragItemIndex.value = null
    dragOverIndex.value = null
    dragging.value = false
}

const autoDistributeScores = () => {
    const total = 100 // 目标总分
    const count = form.value.questions.length
    if (count === 0) return

    const baseScore = Math.floor(total / count)
    const remainder = total % count

    form.value.questions.forEach((q, index) => {
        q.score = index < remainder ? baseScore + 1 : baseScore
    })
}

const submitForm = async () => {
    try {
        // 验证表单
        if (!form.value.title) {
            ElMessage.error('请输入试卷名称')
            return
        }

        if (form.value.questions.length === 0) {
            ElMessage.error('请至少添加一道题目')
            return
        }

        // 准备数据
        const payload = {
            ...form.value,
            questions: form.value.questions.map((q, index) => ({
                id: q.id,
                order_num: index + 1,
                score: q.score
            }))
        }

        // 提交数据
        let response
        if (isEditMode.value) {
            response = await axios.put(`/exam-papers/${props.paperId}/`, payload)
        } else {
            response = await axios.post('/exam-papers/', payload)
        }

        ElMessage.success(isEditMode.value ? '试卷更新成功' : '试卷创建成功')
        emit('success', response.data)
    } catch (error) {
        ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
    }
}

const cancel = () => {
    emit('cancel')
}

// 初始化
onMounted(() => {
    if (isEditMode.value) {
        fetchPaperDetail()
    }
})
</script>

<style scoped>
.paper-editor {
    padding: 20px;
}

.info-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.question-list {
    margin: 20px 0;
}

.question-item {
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fafafa;
    transition: all 0.3s;
    cursor: move;
}

.question-item:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-item.drag-over {
    border: 1px dashed #409eff;
    background-color: #f0f7ff;
}

.question-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.drag-handle {
    margin-right: 10px;
    cursor: move;
    color: #909399;
}

.question-index {
    margin: 0 10px 0 5px;
    font-weight: bold;
}

.question-content {
    flex: 1;
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.question-actions {
    margin-left: 10px;
}

.question-options {
    margin: 10px 0 10px 40px;
}

.option-item {
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 4px;
}

.correct-option {
    background-color: #f0f9eb;
    color: #67c23a;
    font-weight: bold;
}

.question-answer {
    margin-top: 10px;
    padding: 5px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.question-answer .label {
    font-weight: bold;
    color: #606266;
}

.paper-stats {
    margin-top: 20px;
}

.form-actions {
    margin-top: 20px;
    text-align: center;
}

.empty-tip {
    margin: 40px 0;
}
</style>