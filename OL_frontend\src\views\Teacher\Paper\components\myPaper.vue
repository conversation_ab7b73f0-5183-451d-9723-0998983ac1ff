<template>
  <div class="my-paper">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">我的试卷</h2>
        <el-tag type="info" effect="light" class="total-tag">
          共 {{ totalPapers }} 份试卷
        </el-tag>
      </div>
      <div class="header-right">
        <el-input v-model="searchText" placeholder="搜索试卷名称" clearable style="width: 200px; margin-right: 10px;"
          @keyup.enter="fetchPapers" @clear="fetchPapers" />
        <el-button type="primary" @click="createNewPaper">
          <el-icon>
            <Plus />
          </el-icon> 新增试卷
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="paper-tabs" @tab-change="fetchPapers">
      <el-tab-pane label="已发布" name="published">
        <PaperCard v-for="paper in filteredPapers" :key="paper.id" :paper="paper" @edit="editPaper"
          @delete="deletePaper" @publish="togglePublishPaper" />
      </el-tab-pane>
      <el-tab-pane label="草稿箱" name="draft">
        <PaperCard v-for="paper in filteredPapers" :key="paper.id" :paper="paper" @edit="editPaper"
          @delete="deletePaper" @publish="togglePublishPaper" />
      </el-tab-pane>
    </el-tabs>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalPapers"
      layout="total, sizes, prev, pager, next, jumper" @size-change="fetchPapers" @current-change="fetchPapers" />

    <!-- 试卷编辑/创建对话框 -->
    <el-dialog v-model="showPaperDialog" :title="isEditMode ? '编辑试卷' : '新建试卷'" width="80%" top="5vh">
      <PaperEditor v-if="showPaperDialog" :paper-id="currentPaperId" @success="handlePaperSuccess"
        @cancel="showPaperDialog = false" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import PaperCard from './paperCard.vue'
import PaperEditor from './paperEditor.vue'
import axios from '@/utils/axios'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const router = useRouter()

// 数据状态
const activeTab = ref('published')
const searchText = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalPapers = ref(0)
const papers = ref([])
const showPaperDialog = ref(false)
const currentPaperId = ref(null)
const isEditMode = ref(false)

// 计算属性
const filteredPapers = computed(() => {
  return papers.value.filter(paper =>
    paper.status === activeTab.value &&
    paper.name.includes(searchText.value))
})

// 方法
const fetchPapers = async () => {
  try {
    const teacherId = userStore.username; // 从用户store获取教师ID
    const params = {
      teacher_id: teacherId, // 添加教师ID参数
      status: activeTab.value,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchText.value
    }

    const response = await axios.get('/exam-papers/', { params })
    papers.value = response.data.results
    totalPapers.value = response.data.count
  } catch (error) {
    ElMessage.error('获取试卷列表失败: ' + (error.response?.data?.message || error.message))
  }
}
const createNewPaper = () => {
  currentPaperId.value = null
  isEditMode.value = false
  showPaperDialog.value = true
}

const editPaper = (id) => {
  currentPaperId.value = id
  isEditMode.value = true
  showPaperDialog.value = true
}

const deletePaper = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除此试卷吗？删除后无法恢复', '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.delete(`/exam-papers/${id}/`)
    ElMessage.success('删除成功')
    fetchPapers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
    }
  }
}

const togglePublishPaper = async (id, publish) => {
  try {
    const action = publish ? '发布' : '取消发布'
    await ElMessageBox.confirm(`确定要${action}此试卷吗？`, '确认', {
      confirmButtonText: `确定${action}`,
      cancelButtonText: '取消',
      type: 'warning'
    })

    const url = publish ? `/exam-papers/${id}/publish/` : `/exam-papers/${id}/unpublish/`
    await axios.post(url)
    ElMessage.success(`${action}成功`)
    fetchPapers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${publish ? '发布' : '取消发布'}失败: ${error.response?.data?.message || error.message}`)
    }
  }
}

const handlePaperSuccess = () => {
  showPaperDialog.value = false
  fetchPapers()
}

// 初始化
onMounted(() => {
  fetchPapers()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.total-tag {
  height: 28px;
  line-height: 26px;
}

.paper-tabs {
  margin-bottom: 20px;
}

.el-pagination {
  margin-top: 20px;
  justify-content: center;
}
</style>