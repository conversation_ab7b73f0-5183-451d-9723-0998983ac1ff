<template>
    <header class="header-container">
        <!-- 添加点击事件 -->
        <div class="logo" @click="navigateToHome">
            <img src="@/assets/img/yun.png" alt="Logo" style="width: 40px; height: 32px; margin-right: 8px;" />
            在线学习平台
        </div>
        <!-- 导航栏路由区域 -->
        <div class="nav-menu">
            <el-button 
                v-for="(route, index) in navRoutes" 
                :key="index" 
                text 
                @click="handleNavClick(route)"
            >
                {{ route.name }}
            </el-button>
        </div>
        <div class="user-actions">
            <template v-if="!isLoggedIn">
                <el-button text @click="gotoLogin">登录</el-button>
                <el-button text @click="gotoRegister">注册</el-button>
            </template>
            <template v-else>
                <span>欢迎，用户{{ username }}</span>
            </template>
            <el-dropdown>
                <div class="avatar-wrapper">
                    <el-avatar icon="el-icon-user-solid" size="big" />
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item 
                            v-for="(route, index) in dropdownRoutes" 
                            :key="index" 
                            @click="handleDropdownClick(route)"
                        >
                            {{ route.name }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </header>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 引入相关组件
import OnlineStudy from '@/views/Student/OnlineStudy.vue' 
import CourseRegistration from '@/views/Student/CourseRegistration.vue' 
import CourseManagement from '@/views/Teacher/Course/courseManagement.vue' 
import TikaoCenter from '@/views/Teacher/TiKaoCenter.vue' 
import PersonalCenter from '@/views/Share/PersonalCenter.vue' 
import MyCourses from '@/views/Student/MyCourses.vue' 
import TodoList from '@/views/Student/TodoList.vue' 

const router = useRouter()
const isLoggedIn = ref(false)
const username = ref('1234')

// 导航栏路由
const navRoutes = ref([
    {
        path: '/study',
        name: '在线学习',
        component: OnlineStudy,
        meta: {
            hideInMenu: true 
        }
    },
    {
        path: '/courses',
        name: '课程注册',
        component: CourseRegistration
    },
    {
        path: '/course-manage',
        name: '课程管理',
        component: CourseManagement
    },
    {
        path: '/tikao',
        name: '题考中心首页',
        component: TikaoCenter
    }
])

// 下拉菜单路由
const dropdownRoutes = ref([
    {
        path: '/personal',
        name: '个人中心',
        component: PersonalCenter
    },
    {
        path: '/my-courses',
        name: '我的课程',
        component: MyCourses
    },
    {
        path: '/todo',
        name: '待办事项',
        component: TodoList
    }
])

// 导航函数
const navigateToHome = () => {
    router.push('/') // 跳转到主页
}

const handleNavClick = (route) => {
    router.push(route.path)
}

const handleDropdownClick = (route) => {
    router.push(route.path)
}

const gotoLogin = () => {
    router.push('/login')
}

const gotoRegister = () => {
    router.push('/register')
}
</script>

<style scoped>
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 24px;
    border-bottom: 1px solid #e4e4e4;
    background-color: #fff;
    margin-bottom: 50px;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer; /* 添加光标样式 */
}

.nav-menu {
    display: flex;
    gap: 10px;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.avatar-wrapper {
    cursor: pointer;
}

.avatar-wrapper .el-avatar {
    width: 50px;
    height: 50px;
}

.el-dropdown-menu {
    min-width: 120px;
}
</style>